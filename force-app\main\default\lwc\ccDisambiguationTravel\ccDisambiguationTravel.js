import CcDisambiguationCore from "c/ccDisambiguationCore";

export default class CcDisambiguationTravel extends CcDisambiguationCore {
  get formData() {
    return {
      country: {
        name: "country",
        value: "",
        label:
          "Scegli la nazione di destinazione del tuo viaggio, se visiterai pi<PERSON>, sarà necessario indicare solo quello più lontano.",
        options: this.entityDomains?.ATTIVITA2 || [],
        path: "Ambito.Bene.Viaggio.PaeseDestinazione",
      },
      travelDateStart: {
        name: "travelDateStart",
        type: "date",
        value: "",
        label: "Data di partenza",
        type: "date",
        path: "Ambito.Bene.Viaggio.DataViaggioAndata",
        format: (value) => {
          //Target DD/MM/YYYY
          const options = { year: "numeric", month: "2-digit", day: "2-digit" };
          return value
            ? new Date(value).toLocaleDateString("it-IT", options)
            : "";
        },
        validation: (event) => {
          const value = event.target.value;
          if (!value) {
            event.target.setCustomValidity(
              "La data di partenza è obbligatoria."
            );
          } else if (new Date(value) < new Date()) {
            event.target.setCustomValidity(
              "La data di partenza non può essere nel passato."
            );
          } else if (this.mergeData.travelDateEnd) {
            const today = new Date();
            const travelDateStart = new Date(value);
            const travelDateEnd = new Date(this.mergeData.travelDateEnd.value);
            if (travelDateStart > travelDateEnd) {
              event.target.setCustomValidity(
                "La data di partenza non può essere successiva alla data di ritorno."
              );
            } else {
              this.handleChange(event);
              event.target.setCustomValidity(""); // Clear custom validity if valid
            }
          } else {
            this.handleChange(event);
            event.target.setCustomValidity(""); // Clear custom validity if valid
          }
        },
      },
      travelDateEnd: {
        name: "travelDateEnd",
        type: "date",
        value: "",
        label: "Data di ritorno",
        type: "date",
        path: "Ambito.Bene.Viaggio.DataViaggioRitorno",
        format: (value) => {
          //Target DD/MM/YYYY
          const options = {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          };
          return value
            ? new Date(value).toLocaleDateString("it-IT", options)
            : "";
        },
        validation: (event) => {
          const value = event.target.value;
          if (!value) {
            event.target.setCustomValidity(
              "La data di ritorno è obbligatoria."
            );
          } else if (new Date(value) < new Date()) {
            event.target.setCustomValidity(
              "La data di ritorno non può essere nel passato."
            );
          } else if (this.travelDateStart) {
            const travelDateStart = new Date(this.travelDateStart.value);
            const travelDateEnd = new Date(value);
            if (travelDateStart > travelDateEnd) {
              event.target.setCustomValidity(
                "La data di ritorno non può essere precedente alla data di partenza."
              );
            } else {
              this.handleChange(event);
              event.target.setCustomValidity(""); // Clear custom validity if valid
            }
          } else {
            this.handleChange(event);
            event.target.setCustomValidity(""); // Clear custom validity if valid
          }
        },
      },
      numberOfPeople: {
        name: "numberOfPeople",
        value: "",
        label: "Numero di Viaggiatori",
        type: "number",
        min: 1,
        max: 4,
        path: "Ambito.Bene.Viaggio.NumeroViaggiatori",
        validation: (event) => {
          const value = event.target.value;
          if (value < 1) {
            event.target.setCustomValidity(
              "Il numero di viaggiatori deve essere almeno 1."
            );
          } else {
            this.handleChange(event);
            event.target.setCustomValidity(""); // Clear custom validity if valid
          }
        },
      },
    };
  }
}
