<template>
  <template if:true={field}>
    <template if:true={isVisible}>
      <div class={componentClass}>
        <p if:true={debug} class="temporaryLabel">FIELD: <b>{controlTypeForDebugging}</b></p>

        <template if:true={isPxHidden}>
          <c-dx-field-px-hidden
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
            groups={groups}
          ></c-dx-field-px-hidden>
        </template>

        <template if:true={isCaption}>
          <c-dx-field-caption debug={debug} field={field} decoded-value={decodedValue}></c-dx-field-caption>
        </template>

        <template if:true={isParagraph}>
          <c-dx-field-paragraph debug={debug} field={field}></c-dx-field-paragraph>
        </template>

        <template if:true={isLink}>
          <c-dx-field-link
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-link>
        </template>

        <template if:true={isButton}>
          <c-dx-field-button
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-button>
        </template>

        <template if:true={isPxInteger}>
          <c-dx-field-px-integer
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-px-integer>
        </template>

        <template if:true={isPxTextInput}>
          <c-dx-field-input-text
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-input-text>
        </template>

        <template if:true={isPxIcon}>
          <c-dx-field-px-icon debug={debug} field={field}></c-dx-field-px-icon>
        </template>

        <template if:true={isPxRadioButtons}>
          <c-dx-field-px-radio-buttons
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-px-radio-buttons>
        </template>

        <template if:true={isCheckbox}>
          <c-dx-field-checkbox
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-checkbox>
        </template>

        <template if:true={isDateTime}>
          <c-dx-field-date-time
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-dx-field-date-time>
        </template>

        <template if:true={isPxCurrency}>
          <c-dx-field-px-currency debug={debug} field={field} decoded-value={decodedValue}
            disabled={isDisabled} >
          </c-dx-field-px-currency>
        </template>

        <template if:true={isAutoComplete}>
          <c-dx-field-px-auto-complete
            debug={debug} field={field} decoded-value={decodedValue} disabled={isDisabled}>
          </c-dx-field-px-auto-complete>
        </template>

        <template if:true={isDropdown}>
          <c-dx-field-dropdown
            debug={debug}
            field={field}
            disabled={isDisabled}
            decoded-value={decodedValue}
          ></c-dx-field-dropdown>
        </template>
        
      </div>
    </template>
  </template>
</template>
