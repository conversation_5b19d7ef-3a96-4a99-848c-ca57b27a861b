import { api } from 'lwc';
import LightningModal from 'lightning/modal';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { loadItems, saveItems, upsertItem } from 'c/uuLocalStorageUtils';
import IpEditSavedItemModal from 'c/ipEditSavedItemModal';

export default class IpSavedItemsModal extends LightningModal {
	@api historyStorageKey = 'iPUtilsDebuggerHistory';
	@api mockStorageKey = 'iPUtilsDebuggerMockMap';

	history = [];
	historySearchTerm = '';
	showImportArea = false;
	importJson = '';

	historyColumns = [
		{ label: 'DateTime', fieldName: 'formattedDate', type: 'text' },
		{ label: 'Procedura', fieldName: 'name', type: 'text' },
		{ label: 'Label', fieldName: 'label', type: 'text' },
		{ label: 'Mock attivo', fieldName: 'mockActive', type: 'boolean', cellAttributes: { alignment: 'center' } },
		{
			type: 'action',
			typeAttributes: {
				rowActions: [
					{ label: 'Modifica', name: 'edit', iconName: 'utility:edit' },
					{ label: 'Seleziona', name: 'select', iconName: 'utility:check' },
					{ label: 'Duplica', name: 'duplicate', iconName: 'utility:copy' },
					{ label: 'Rimuovi', name: 'remove', iconName: 'utility:delete' },
					{ label: 'Attiva Mock', name: 'mock_activate', iconName: 'utility:toggle' },
					{ label: 'Disattiva Mock', name: 'mock_deactivate', iconName: 'utility:toggle' }
				],
				menuAlignment: 'right'
			}
		}
	];

	connectedCallback() {
		this.refreshHistory();
	}

	refreshHistory() {
		this.history = loadItems(this.historyStorageKey)
			.map((h) => ({ ...h, mockActive: !!h.mockActive, formattedDate: this.formatDate(h.timestamp) }))
			.sort((a, b) => b.timestamp - a.timestamp);
	}

	formatDate(timestamp) {
		const date = new Date(timestamp);
		return date.toLocaleString();
	}

	get filteredHistory() {
		const term = (this.historySearchTerm || '').toLowerCase();
		if (!term) return this.history;
		return this.history.filter(
			(h) =>
				(h.name || '').toLowerCase().includes(term) ||
				(h.label || '').toLowerCase().includes(term) ||
				(h.request || '').toLowerCase().includes(term)
		);
	}

	get hasFilteredHistory() {
		return (this.filteredHistory || []).length > 0;
	}

	handleHistorySearchChange(event) {
		this.historySearchTerm = event.target.value;
	}

	exportHistory() {
		try {
			const data = JSON.stringify(this.history, null, 2);
			if (typeof navigator !== 'undefined') {
				navigator.clipboard.writeText(data);
				this.dispatchEvent(new ShowToastEvent({ title: 'Esportato', message: 'Cronologia copiata negli appunti', variant: 'success' }));
			}
		} catch (e) {
			this.dispatchEvent(new ShowToastEvent({ title: 'Errore', message: e.message, variant: 'error' }));
		}
	}

	get importToggleButtonLabel() {
		return this.showImportArea ? 'Nascondi import' : 'Mostra import';
	}

	toggleImportArea() {
		this.showImportArea = !this.showImportArea;
	}

	handleImportJsonChange(event) {
		this.importJson = event.target.value;
	}

	importHistory() {
		try {
			const arr = JSON.parse(this.importJson);
			if (!Array.isArray(arr)) throw new Error('Formato non valido: atteso un array');
			const normalized = arr.map((h) => ({
				id: h.id?.toString() || Date.now().toString(),
				name: h.name || '',
				request: typeof h.request === 'string' ? h.request : JSON.stringify(h.request || {}),
				label: h.label || h.name || '',
				timestamp: h.timestamp || Date.now(),
				mockActive: !!h.mockActive,
				response: typeof h.response === 'string' ? h.response : h.response ? JSON.stringify(h.response) : undefined,
			}));
			saveItems(this.historyStorageKey, normalized);
			this.refreshHistory();
			// rebuild mock map from imported items
			const map = {};
			for (const it of this.history) {
				if (it.mockActive && it.name && it.response) {
					try {
						map[it.name] = JSON.parse(it.response);
					} catch (e) {
						// ignore invalid response
					}
				}
			}
			window.localStorage.setItem(this.mockStorageKey, JSON.stringify(map));
			this.dispatchEvent(new ShowToastEvent({ title: 'Import completato', variant: 'success' }));
		} catch (e) {
			this.dispatchEvent(new ShowToastEvent({ title: 'Errore import', message: e.message, variant: 'error' }));
		}
	}

	async handleHistoryRowAction(event) {
		const { action, row } = event.detail || {};
		if (!row || !action) return;
		switch (action.name) {
			case 'edit':
				await this.openEditModal(row);
				break;
			case 'select':
				// return selection to caller
				this.close({ action: 'select', item: row });
				break;
			case 'duplicate':
				this.handleHistoryDuplicate(row);
				break;
			case 'remove':
				this.handleHistoryRemove(row);
				break;
			case 'mock_activate':
				this.handleHistoryMockActivate(row);
				break;
			case 'mock_deactivate':
				this.handleHistoryMockDeactivate(row);
				break;
			default:
				break;
		}
	}

	async openEditModal(row) {
		const requestText = this.prettyJson(row?.request);
		const responseText = this.prettyJson(row?.response);
		const result = await IpEditSavedItemModal.open({
			size: 'large',
			label: 'Modifica elemento salvato',
			requestText,
			responseText,
		});
		if (result && result.saved) {
			const updated = this.history.map((h) =>
				h.id === row.id ? { ...h, request: result.request, response: result.response || undefined } : h
			);
			this.history = updated;
			saveItems(this.historyStorageKey, this.history);
			// update mock if active
			const edited = this.history.find((h) => h.id === row.id);
			if (edited?.mockActive && edited?.name) {
				if (result.response) {
					this.saveMockForProcedure(edited.name, result.response);
				} else {
					this.removeMockForProcedure(edited.name);
				}
			}
			this.dispatchEvent(new ShowToastEvent({ title: 'Salvato', message: 'Elemento aggiornato', variant: 'success' }));
			this.refreshHistory();
		}
	}

	handleHistoryDuplicate(row) {
		const copy = {
			...row,
			id: Date.now().toString(),
			label: `${row.label} (copy)`,
			timestamp: Date.now(),
			mockActive: false,
		};
		this.history = upsertItem(this.historyStorageKey, copy, 'id');
		this.refreshHistory();
	}

	handleHistoryRemove(row) {
		const removed = this.history.find((h) => h.id === row.id);
		this.history = this.history.filter((h) => h.id !== row.id);
		saveItems(this.historyStorageKey, this.history);
		if (removed?.mockActive) {
			this.removeMockForProcedure(removed.name);
		}
		this.refreshHistory();
	}

	handleHistoryMockActivate(row) {
		if (!row.response) {
			this.dispatchEvent(
				new ShowToastEvent({ title: 'Impossibile attivare', message: 'Questo record non ha una response salvata. Esegui la chiamata e salva di nuovo.', variant: 'warning' })
			);
			return;
		}
		const name = row.name;
		this.history = this.history.map((h) => (h.name === name ? { ...h, mockActive: h.id === row.id } : h));
		saveItems(this.historyStorageKey, this.history);
		this.saveMockForProcedure(name, row.response);
		this.refreshHistory();
	}

	handleHistoryMockDeactivate(row) {
		this.history = this.history.map((h) => (h.id === row.id ? { ...h, mockActive: false } : h));
		saveItems(this.historyStorageKey, this.history);
		if (row?.name) this.removeMockForProcedure(row.name);
		this.refreshHistory();
	}

	prettyJson(text) {
		try {
			if (!text) return '';
			const obj = typeof text === 'string' ? JSON.parse(text) : text;
			return JSON.stringify(obj, null, 2);
		} catch (e) {
			return text;
		}
	}

	// Mock map helpers
	loadMockMap() {
		try {
			const raw = window.localStorage.getItem(this.mockStorageKey);
			return raw ? JSON.parse(raw) : {};
		} catch (e) {
			return {};
		}
	}
	saveMockMap(map) {
		try {
			window.localStorage.setItem(this.mockStorageKey, JSON.stringify(map));
		} catch (e) {
			// ignore
		}
	}
	saveMockForProcedure(name, responseStr) {
		try {
			const map = this.loadMockMap();
			let resp;
			try {
				resp = typeof responseStr === 'string' ? JSON.parse(responseStr) : responseStr;
			} catch (e) {
				resp = responseStr;
			}
			map[name] = resp;
			this.saveMockMap(map);
		} catch (e) {
			// ignore
		}
	}
	removeMockForProcedure(name) {
		const map = this.loadMockMap();
		if (map && Object.prototype.hasOwnProperty.call(map, name)) {
			delete map[name];
			this.saveMockMap(map);
		}
	}

	handleClose = () => this.close();
}

