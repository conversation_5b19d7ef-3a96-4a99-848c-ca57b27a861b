/////////////////////////////////////////////////////////////////////
////////////////////////////// FIELDS ///////////////////////////////
/////////////////////////////////////////////////////////////////////

// Field types used in the application.
/**
 * @typedef {'caption'
 *   | 'paragraph'
 *   | 'pxLink'
 *   | 'pxHidden'
 *   | 'pxInteger'
 *   | 'pxButton'
 *   | 'pxTextInput'
 *   | 'pxCheckbox'
 *   | 'pxRadioButtons'
 *   | 'pxDateTime'
 *   | 'pxCurrency'
 *   | 'pxDropdown'
 *   | 'pxIcon'
 *   | 'pxIcon'
 *   | 'pxAutoComplete'} FieldTypes
 */
/** @typedef {Object<string, FieldTypes>} FIELD_TYPES */

/////////////////////////////////////////////////////////////////////
///////////////////////// PX HIDDEN FIELDS //////////////////////////
/////////////////////////////////////////////////////////////////////

// Custom types associated with px-hidden format.
/** @typedef {'analyticsPageFields' 
 *          | 'stepper' 
 *          | 'separator' 
 *          | 'multiStepper' 
 *          | 'header'
 *          | 'circularStepper'
 *          | 'stickyFooterPU'
 *          | 'cardProtezioneDettaglioGaranzia'
 *          | 'toastCard'
 *          | 'cardSezione'
 *          | 'unicoProtezione'
 *          | 'agencyLocator'
 *          | 'assurancePackage'
 *          | 'addressAutocomplete'
 *          | 'carrelloPU'
 *          | 'carouselCard'
 *          | 'carouselWeb'
 *          | 'cardProtezione'
 *          | 'boxIndirizzo'
 *          | 'cardTelematica'
 *          | 'boxPagamento'
 *          | 'cardGaranzieHeader'  } PxHiddenTypes 
 * */
/** @typedef {Object<string, PxHiddenTypes>} PX_HIDDEN_TYPES */

/////////////////////////////////////////////////////////////////////
///////////////////////// CUSTOM ATTRIBUTES /////////////////////////
/////////////////////////////////////////////////////////////////////

/**
 * @typedef {'_URL'
 *   | 'Pdf'
 *   | 'GO_TO_LOGIN'
 *   | 'GO_TO_REGISTRATION'
 *   | 'GO_TO_HOME'
 *   | 'GO_TO_QUOTATION_HOME'
 *   | 'EXIT_FLOW'
 *   | 'GO_TO_APP'
 *   | 'GO_TO_NEW_QUOTATION'} ButtonLinkType
 */

/////////////////////////////////////////////////////////////////////
////////////////////////////// LAYOUTS //////////////////////////////
/////////////////////////////////////////////////////////////////////

/**
 * @typedef {'mimicASentenceCenter'
 *   | 'DefaultBgGrey'
 *   | 'CardBgWhite'
 *   | 'inlineGridXY'
 *   | 'row'
 *   | 'col'
 *   | 'accordionN'
 *   | 'accordionHeader'
*   | 'accordionOpened'
*   | 'customComponent'
*   | 'mimic-a-sentence'
*   | 'DefaultBgGrey'
*   | 'dynamic'
*   | 'default-bg-grey'
*   | 'mimic-a-sentence-center'
*   | 'mimic-a-sentence-center-web'
*   | 'default-tpd'
*   | 'card-bg-white'
*   | 'card-form'
*   | 'card-app'
*   | 'rounded-card'
*   | 'box-web-border-gray1'
*   | 'inline-gridXY'
*   | 'inlineTPD-middle'
*   | 'defaucarrello-PU-Headerlt'
*   | 'unicoProtezione-Ribbon'
*   | 'carrello-PU-Header'
*   | 'flat-card'
*   | 'row-layout'
*   | 'col-layout'
*   | 'card'
*   | 'box'
*   | 'stacked'
*   | 'price-container'
*   | 'assurancePackageRibbon'
*   | 'accordion-header'
*   | 'responsive-2col'
*   | 'responsive-2colPU'
*   | 'responsive-2col8'
*   | 'responsive-3col'
*   | 'responsive-2col1fr2fr'
*   | 'tooltip-card'
*   | 'default'
*   | 'accordionClosed'} SupportedGroupFormat
 */

/////////////////////////////////////////////////////////////////////
////////////////////////////// ACTIONS //////////////////////////////
/////////////////////////////////////////////////////////////////////

/**
 * @typedef {Object} GestioneProcesso
 * @property {string} GestioneProcesso.StepSuccessivo
 * @property {string} GestioneProcesso.IndiceGaranziaSelezionata
 * @property {string} GestioneProcesso.IndiceSezioneSelezionata
 * @property {string} GestioneProcesso.IndiceAttributoGaranziaSelezionata
 * @property {string} GestioneProcesso.IndiceOffertaSelezionata
 * @property {string} GestioneProcesso.IdGaranzia
 * @property {string} GestioneProcesso.ValoreGaranzia
 * @property {string} GestioneProcesso.FlagSalvaPerDopo
 * @property {string} GestioneProcesso.AggiungiConvenzione
 * @property {string} GestioneProcesso.VaiAModificaDati
 * @property {string} GestioneProcesso.IndiceScontoSelezionato
 */

/**
 * @typedef {Object} ActionData
 * @property {string} action
 * @property {{ event: string }[]} events
 * @property {any} actionProcess
 */
