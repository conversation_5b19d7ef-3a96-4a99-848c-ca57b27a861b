import { LightningElement, track, api } from 'lwc';
import DxPegaInspectorModal from 'c/dxPegaInspectorModal';
import {
    parseJSON,
    parseQuery,
    rowMatches,
    indexPayload,
    toCSV,
    download,
    keyField,
    keyView,
    keyAction,
    diffByKey
} from 'c/dxPegaInspectorUtils';

// Reuse logic from original pegaInspector.js with identical behavior.
// Only change: class/component name to dxPegaInspector.

const STORAGE_KEYS = {
    raw: 'pja_raw',
    filter: 'pja_filter',
    tab: 'pja_tab',
    wrap: 'pja_wrap',
};

export default class DxPegaInspector extends LightningElement {
    // Public API
    @api header;
    @api title;

    // UI state
    @track raw = '';
    @track raw2 = '';
    @track filter = '';
    @track activeTab = 'summary';
    @track diffMode = false;
    @track filteredOnly = true;
    @track jumpPath = '';
    @track wrapLongText = true;

    // Header title (fallbacks)
    get headerTitle() {
        return this.header || this.title || 'Pega JSON Analyzer';
    }

    get data() { return parseJSON(this.raw); }
    get data2() { return parseJSON(this.raw2); }
    get parseError() { return this.data && this.data.__error ? String(this.data.__error) : null; }
    get parseError2() { return this.diffMode && this.data2 && this.data2.__error ? String(this.data2.__error) : null; }
    get isRawEmpty() { return !this.raw || !String(this.raw).trim(); }

    // Lifecycle: persist minimal state in localStorage
    connectedCallback() {
        try {
            const p = window.localStorage.getItem(STORAGE_KEYS.raw);
            const f = window.localStorage.getItem(STORAGE_KEYS.filter);
            const t = window.localStorage.getItem(STORAGE_KEYS.tab);
            const w = window.localStorage.getItem(STORAGE_KEYS.wrap);
            if (p) this.raw = p;
            if (f) this.filter = f;
            if (t) this.activeTab = t;
            if (w !== null) this.wrapLongText = w === 'true';
        } catch {}
    }
    renderedCallback() {
        try { window.localStorage.setItem(STORAGE_KEYS.raw, this.raw); } catch {}
        try { window.localStorage.setItem(STORAGE_KEYS.filter, this.filter); } catch {}
        try { window.localStorage.setItem(STORAGE_KEYS.tab, this.activeTab); } catch {}
        try { window.localStorage.setItem(STORAGE_KEYS.wrap, String(this.wrapLongText)); } catch {}
    }

    get index() { const d = this.data; if (!d || d.__error) return { fields: [], views: [], actions: [], paragraphs: [], nodes: [] }; return indexPayload(d); }
    get fields() { return this.index.fields; }
    get views() { return this.index.views; }
    get actions() { return this.index.actions; }
    get paragraphs() { return this.index.paragraphs; }
    get nodes() { return this.index.nodes; }
    get noFields() { return (this.fields || []).length === 0; }
    get noViews() { return (this.views || []).length === 0; }
    get noActions() { return (this.actions || []).length === 0; }

    get stats() {
        const by = (arr, key) => arr.reduce((m, r) => (m[r[key]] = (m[r[key]] || 0) + 1, m), {});
        const idx = this.index;
        return { fields: idx.fields.length, views: idx.views.length, actions: idx.actions.length, paragraphs: idx.paragraphs.length, byControl: by(idx.fields, 'controlType'), byType: by(idx.fields, 'type'), required: idx.fields.filter(f => f.required).length, visible: idx.fields.filter(f => f.visible).length, readOnly: idx.fields.filter(f => f.readOnly).length };
    }
    get byControlList() { const entries = Object.entries(this.stats.byControl || {}).map(([key, value]) => ({ key: key || '(empty)', value })); entries.sort((a,b)=>b.value-a.value); return entries.slice(0,12); }
    get byTypeList() { const entries = Object.entries(this.stats.byType || {}).map(([key, value]) => ({ key: key || '(empty)', value })); entries.sort((a,b)=>b.value-a.value); return entries.slice(0,12); }

    get isSummary() { return this.activeTab === 'summary'; }
    get isFields() { return this.activeTab === 'fields'; }
    get isViews() { return this.activeTab === 'views'; }
    get isActions() { return this.activeTab === 'actions'; }
    get isParagraphs() { return this.activeTab === 'paragraphs'; }
    get isTree() { return this.activeTab === 'tree'; }
    get isDiagnostics() { return this.activeTab === 'diagnostics'; }
    get isStats() { return this.activeTab === 'stats'; }
    get isDiff() { return this.activeTab === 'diff' && this.diffMode && this.data2 && !this.data2.__error; }

    get summaryTabClass() { return this.tabClass('summary'); }
    get fieldsTabClass() { return this.tabClass('fields'); }
    get viewsTabClass() { return this.tabClass('views'); }
    get actionsTabClass() { return this.tabClass('actions'); }
    get paragraphsTabClass() { return this.tabClass('paragraphs'); }
    get treeTabClass() { return this.tabClass('tree'); }
    get diagnosticsTabClass() { return this.tabClass('diagnostics'); }
    get statsTabClass() { return this.tabClass('stats'); }
    get diffTabClass() { return this.tabClass('diff'); }
    get diffDisabled() { return !this.diffMode || !this.raw2 || !!this.parseError2; }
    tabClass(key) { const base = 'slds-button slds-button_neutral'; const selected = this.activeTab === key ? ' slds-button_brand' : ''; return base + selected; }

    // Vertical navigation helpers
    navItemClass(key, disabled = false) {
        const base = 'slds-vertical-navigation__item';
        const active = this.activeTab === key ? ' slds-is-active' : '';
        const dis = disabled ? ' slds-is-disabled' : '';
        return base + active + dis;
    }
    get navItemClassSummary() { return this.navItemClass('summary'); }
    get navItemClassFields() { return this.navItemClass('fields'); }
    get navItemClassViews() { return this.navItemClass('views'); }
    get navItemClassActions() { return this.navItemClass('actions'); }
    get navItemClassParagraphs() { return this.navItemClass('paragraphs'); }
    get navItemClassTree() { return this.navItemClass('tree'); }
    get navItemClassDiagnostics() { return this.navItemClass('diagnostics'); }
    get navItemClassStats() { return this.navItemClass('stats'); }
    get navItemClassDiff() { return this.navItemClass('diff', this.diffDisabled); }

    onTabClick = (e) => { const key = e.currentTarget.dataset.key; this.activeTab = key; }
    onRawChange = (e) => { this.raw = e.target.value; }
    onRaw2Change = (e) => { this.raw2 = e.target.value; }
    onFilterChange = (e) => { this.filter = e.target.value; }
    onToggleFilteredOnly = (e) => { this.filteredOnly = e.target.checked; }
    onToggleDiffMode = (e) => { this.diffMode = e.target.checked; }
    onJumpPathChange = (e) => { this.jumpPath = e.target.value; }
    onToggleWrap = (e) => { this.wrapLongText = e.target.checked; }

    prettyPrint = () => { this.raw = JSON.stringify(parseJSON(this.raw), null, 2); }
    onUploadFile = async (e) => { const file = e.target.files && e.target.files[0]; if (!file) return; const txt = await file.text(); this.raw = txt; }
    fetchUrl = async () => { const url = prompt('URL JSON da scaricare:'); if (!url) return; try { const res = await fetch(url); const txt = await res.text(); this.raw = txt; } catch (e) { alert('Fetch fallita: ' + e); } }

    get filterTokens() { return parseQuery(this.filter); }
    applyFilter(rows) { const tokens = this.filterTokens; if (!this.filter || !this.filter.trim()) return rows; return rows.filter(r => rowMatches(r, tokens)); }

    @track fieldsSort = { key: 'path', dir: 'asc' };
    @track viewsSort = { key: 'path', dir: 'asc' };
    @track actionsSort = { key: 'path', dir: 'asc' };

    sortRows(rows, sort) {
        const copy = [...rows]; if (!sort.key) return copy;
        copy.sort((a, b) => { const va = a[sort.key]; const vb = b[sort.key]; if (va === vb) return 0; if (va === undefined || va === null) return 1; if (vb === undefined || vb === null) return -1; return String(va).localeCompare(String(vb), undefined, { numeric: true }) * (sort.dir === 'asc' ? 1 : -1); });
        return copy;
    }
    toggleSort(state, key) { if (state.key === key) { state.dir = state.dir === 'asc' ? 'desc' : 'asc'; } else { state.key = key; state.dir = 'asc'; } return { ...state }; }
    // Legacy click-sort handlers removed (tables migrated to lightning-datatable)
    // Datatable sort handlers
    onSortFieldsDt = (event) => {
        const { fieldName, sortDirection } = event.detail || {};
        this.fieldsSort = { key: fieldName, dir: sortDirection };
    }
    onSortViewsDt = (event) => {
        const { fieldName, sortDirection } = event.detail || {};
        this.viewsSort = { key: fieldName, dir: sortDirection };
    }
    onSortActionsDt = (event) => {
        const { fieldName, sortDirection } = event.detail || {};
        this.actionsSort = { key: fieldName, dir: sortDirection };
    }

    get visibleFields() { const data = this.filteredOnly ? this.applyFilter(this.fields) : this.fields; return this.sortRows(data, this.fieldsSort); }
    get visibleViews() { const data = this.filteredOnly ? this.applyFilter(this.views) : this.views; return this.sortRows(data, this.viewsSort); }
    get visibleActions() { const data = this.filteredOnly ? this.applyFilter(this.actions) : this.actions; return this.sortRows(data, this.actionsSort); }
    get visibleParagraphs() { return this.applyFilter(this.paragraphs); }
    get isFieldsEmpty() { return this.visibleFields.length === 0; }
    get isViewsEmpty() { return this.visibleViews.length === 0; }
    get isActionsEmpty() { return this.visibleActions.length === 0; }
    get isParagraphsEmpty() { return this.visibleParagraphs.length === 0; }

    // Table indicator getters removed (not used with lightning-datatable)

    @track selectedRow = null;
    get selectedRowJson() { return this.selectedRow ? JSON.stringify(this.selectedRow, null, 2) : null; }
    get selectedRowPath() { return this.selectedRow ? this.selectedRow.path : ''; }
    get selectedRowHasCustom() { return this.selectedRow && this.selectedRow.customAttributes !== undefined; }
    get selectedRowCustom() { return this.selectedRow && this.selectedRow._customParsed ? JSON.stringify(this.selectedRow._customParsed, null, 2) : '{}'; }

    // Use LightningModal for inspector
    openInspector = async () => {
        if (!this.selectedRow) return;
        try {
            await DxPegaInspectorModal.open({ size: 'large', label: 'Inspector', description: 'Dettagli riga', row: this.selectedRow });
        } catch {}
    }

    // Action bar state
    get hasSelection() { return !!this.selectedRow; }
    get inspectDisabled() { return !this.selectedRow; }
    inspectSelected = () => { this.openInspector(); }

    doJump = () => { const p = (this.jumpPath || '').trim(); if (!p) return; const idx = this.index; const hit = idx.fields.find(r => r.path.includes(p)) || idx.views.find(r => r.path.includes(p)) || idx.actions.find(r => r.path.includes(p)); if (hit) { if (this.index.fields.includes(hit)) this.activeTab = 'fields'; else if (this.index.views.includes(hit)) this.activeTab = 'views'; else this.activeTab = 'actions'; this.selectedRow = hit; } }

    get issues() {
        const fields = this.fields; const actions = this.actions; const views = this.views; const issues = [];
        views.forEach(v => { if (String(v.visible) === 'false' && v.viewID) { issues.push({ severity: 'info', kind: 'ViewHidden', msg: `View ${v.viewID} visibile=false`, path: v.path }); } });
        const testIdSeen = new Map();
        fields.forEach(f => {
            const attrs = f._customParsed || {};
            const v = String((attrs.validation || '')).toLowerCase();
            if (v === 'email' && f.value) { if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(String(f.value))) { issues.push({ severity: 'warn', kind: 'EmailFormat', msg: `Email non valida: ${f.value}`, path: f.path }); } }
            if (v === 'mobilephone' && f.value) { if (!/^[0-9]{6,15}$/.test(String(f.value))) { issues.push({ severity: 'warn', kind: 'MobileFormat', msg: `Cellulare non valido: ${f.value}`, path: f.path }); } }
            if (f.required && (f.value === undefined || f.value === '')) { issues.push({ severity: 'warn', kind: 'RequiredEmpty', msg: `Campo richiesto senza valore: ${f.label || f.fieldID}`, path: f.path }); }
            if (f.testID) { if (testIdSeen.has(f.testID)) { issues.push({ severity: 'warn', kind: 'DuplicateTestID', msg: `testID duplicato: ${f.testID}`, path: `${testIdSeen.get(f.testID)} | ${f.path}` }); } else { testIdSeen.set(f.testID, f.path); } }
            if (f.controlType === 'pxButton') { const submit = attrs.submitButton === 'true' || attrs.submitButton === true; if (submit) { const hasFinish = actions.some(a => (a.path || '').startsWith(f.path) && a.action === 'finishAssignment'); if (!hasFinish) { issues.push({ severity: 'warn', kind: 'SubmitWithoutFinish', msg: 'Button submit senza finishAssignment', path: f.path }); } } if (attrs.linkType && !attrs.link) { issues.push({ severity: 'warn', kind: 'ButtonMissingLink', msg: 'pxButton linkType presente ma link mancante', path: f.path }); } }
            if (String(f.visible) === 'false' && String(f.value ?? '') !== '') { issues.push({ severity: 'info', kind: 'HiddenHasValue', msg: 'Campo nascosto con value', path: f.path }); }
        });
        return issues.map(i => ({ ...i, cls: `slds-box slds-theme_alert-texture ${i.severity==='warn'?'slds-theme_warning':'slds-theme_info'} slds-m-vertical_x-small slds-p-around_small` }));
    }
    get hasIssues() { return (this.issues || []).length > 0; }

    exportFieldsCsv = () => { const rows = this.filteredOnly ? this.applyFilter(this.fields) : this.fields; const cols = ['path','label','reference','fieldID','type','controlType','required','readOnly','visible','value','testID','customAttributes']; download('fields.csv', toCSV(rows, cols), 'text/csv'); }
    exportViewsCsv = () => { const rows = this.filteredOnly ? this.applyFilter(this.views) : this.views; const cols = ['path','viewID','name','appliesTo','visible','reference','title']; download('views.csv', toCSV(rows, cols), 'text/csv'); }
    exportActionsCsv = () => { const rows = this.filteredOnly ? this.applyFilter(this.actions) : this.actions; const cols = ['path','controlType','label','event','action','actionName']; download('actions.csv', toCSV(rows, cols), 'text/csv'); }
    downloadJson = () => { download('normalized.json', JSON.stringify(parseJSON(this.raw), null, 2), 'application/json'); }

    get metaSummary() {
        const d = this.data || {};
        const obj = { actionID: d?.pegaBodyResponse?.actionID, caseID: d?.pegaBodyResponse?.caseID, name: d?.pegaBodyResponse?.name, assignmentId: d?.metaBodyResponse?.assignmentId, actionId: d?.metaBodyResponse?.actionId, caseId: d?.metaBodyResponse?.caseId, };
        try { return JSON.stringify(obj, null, 2); } catch { return '{}'; }
    }

    get diff() {
        if (!this.isDiff) return { fields: { added: [], removed: [], changed: [] } };
        const idx1 = this.index; const idx2 = indexPayload(this.data2);
        return { fields: diffByKey(idx1.fields, idx2.fields, keyField), views: diffByKey(idx1.views, idx2.views, keyView), actions: diffByKey(idx1.actions, idx2.actions, keyAction), };
    }
    get isEmptyDiffFieldsAdded() { return !this.diff.fields.added.length; }
    get isEmptyDiffFieldsRemoved() { return !this.diff.fields.removed.length; }
    get isEmptyDiffFieldsChanged() { return !this.diff.fields.changed.length; }

    // lightning-datatable columns
    get fieldsColumns() {
        return [
            { label: 'Path', fieldName: 'path', type: 'text', sortable: true, initialWidth: 520, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Label', fieldName: 'label', type: 'text', sortable: true, initialWidth: 220, wrapText: this.wrapLongText },
            { label: 'Reference', fieldName: 'reference', type: 'text', sortable: true, initialWidth: 420, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'FieldID', fieldName: 'fieldID', type: 'text', sortable: true, initialWidth: 180, cellAttributes: { class: 'mono' } },
            { label: 'Type', fieldName: 'type', type: 'text', sortable: true, initialWidth: 140, cellAttributes: { class: 'mono' } },
            { label: 'Control', fieldName: 'controlType', type: 'text', sortable: true, initialWidth: 160, cellAttributes: { class: 'mono' } },
            { label: 'Req', fieldName: 'required', type: 'boolean', sortable: true, initialWidth: 90 },
            { label: 'RO', fieldName: 'readOnly', type: 'boolean', sortable: true, initialWidth: 90 },
            { label: 'Vis', fieldName: 'visible', type: 'boolean', sortable: true, initialWidth: 90 },
            { label: 'Value', fieldName: 'value', type: 'text', sortable: true, initialWidth: 360, wrapText: this.wrapLongText },
            { label: 'TestID', fieldName: 'testID', type: 'text', sortable: true, initialWidth: 160, cellAttributes: { class: 'mono' } },
            { label: 'customAttributes', fieldName: 'customAttributes', type: 'text', sortable: true, initialWidth: 420, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { type: 'action', typeAttributes: { rowActions: this.rowActions } },
        ];
    }
    get viewsColumns() {
        return [
            { label: 'Path', fieldName: 'path', type: 'text', sortable: true, initialWidth: 400, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'ViewID', fieldName: 'viewID', type: 'text', sortable: true, initialWidth: 200, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Name', fieldName: 'name', type: 'text', sortable: true, initialWidth: 220, wrapText: this.wrapLongText },
            { label: 'AppliesTo', fieldName: 'appliesTo', type: 'text', sortable: true, initialWidth: 260, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Vis', fieldName: 'visible', type: 'boolean', sortable: true },
            { label: 'Reference', fieldName: 'reference', type: 'text', sortable: true, initialWidth: 280, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Title', fieldName: 'title', type: 'text', sortable: true, initialWidth: 240, wrapText: this.wrapLongText },
            { type: 'action', typeAttributes: { rowActions: this.rowActions } },
        ];
    }
    get actionsColumns() {
        return [
            { label: 'Path', fieldName: 'path', type: 'text', sortable: true, initialWidth: 400, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Control', fieldName: 'controlType', type: 'text', sortable: true, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Label', fieldName: 'label', type: 'text', sortable: true, wrapText: this.wrapLongText },
            { label: 'Event', fieldName: 'event', type: 'text', sortable: true, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Action', fieldName: 'action', type: 'text', sortable: true, initialWidth: 220, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'ActionName', fieldName: 'actionName', type: 'text', sortable: true, initialWidth: 240, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { type: 'action', typeAttributes: { rowActions: this.rowActions } },
        ];
    }
    get paragraphsColumns() {
        return [
            { label: 'Path', fieldName: 'path', type: 'text', initialWidth: 400, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'ParagraphID', fieldName: 'id', type: 'text', initialWidth: 200, wrapText: this.wrapLongText, cellAttributes: { class: 'mono' } },
            { label: 'Text', fieldName: 'text', type: 'text', wrapText: this.wrapLongText, initialWidth: 480 },
            { type: 'action', typeAttributes: { rowActions: this.rowActions } },
        ];
    }

    // Row actions (menu at end of row)
    get rowActions() {
        return [
            { label: 'Ispeziona', name: 'inspect' },
            { label: 'Copia Path', name: 'copy_path' },
            { label: 'Copia JSON', name: 'copy_json' },
        ];
    }
    onRowAction = (event) => {
        const actionName = event.detail?.action?.name;
        const row = event.detail?.row;
        if (!actionName || !row) return;
        this.selectedRow = row;
        switch (actionName) {
            case 'inspect':
                this.openInspector();
                break;
            case 'copy_path':
                this.copyToClipboard(String(row.path || ''));
                break;
            case 'copy_json':
                this.copyToClipboard(JSON.stringify(row, null, 2));
                break;
        }
    }
    copyToClipboard(text) {
        try {
            if (navigator?.clipboard?.writeText) {
                navigator.clipboard.writeText(text);
            } else {
                const area = document.createElement('textarea');
                area.value = text;
                area.style.position = 'fixed';
                area.style.opacity = '0';
                document.body.appendChild(area);
                area.focus();
                area.select();
                document.execCommand('copy');
                document.body.removeChild(area);
            }
        } catch {}
    }
    copySelectedPath = () => { if (this.selectedRow?.path) this.copyToClipboard(String(this.selectedRow.path)); }
    copySelectedJson = () => { if (this.selectedRow) this.copyToClipboard(JSON.stringify(this.selectedRow, null, 2)); }

    // Row selection handlers for datatable
    setSelectedFromRows(rows) {
        this.selectedRow = rows && rows.length ? rows[0] : null;
        this.openInspector();
    }
    onSelectFields = (event) => { this.setSelectedFromRows(event.detail?.selectedRows || []); }
    onSelectViews = (event) => { this.setSelectedFromRows(event.detail?.selectedRows || []); }
    onSelectActions = (event) => { this.setSelectedFromRows(event.detail?.selectedRows || []); }
    onSelectParagraphs = (event) => { this.setSelectedFromRows(event.detail?.selectedRows || []); }
}
