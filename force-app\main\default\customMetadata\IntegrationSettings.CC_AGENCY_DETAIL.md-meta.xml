<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>CC_AGENCY_DETAIL</label>
    <protected>false</protected>
    <values>
        <field>BodyType__c</field>
        <value xsi:type="xsd:string">JSON</value>
    </values>
    <values>
        <field>CallbackUrl__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>DelayMs__c</field>
        <value xsi:type="xsd:double">1000.0</value>
    </values>
    <values>
        <field>Endpoint__c</field>
        <value xsi:type="xsd:string">/api/pub/poiLocator/v1/poi/%agency%</value>
    </values>
    <values>
        <field>Header__c</field>
        <value xsi:type="xsd:string">{&quot;content-type&quot;: &quot;application/json&quot;}</value>
    </values>
    <values>
        <field>IntegrationId__c</field>
        <value xsi:type="xsd:string">CC_AGENCY_DETAIL</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>Method__c</field>
        <value xsi:type="xsd:string">GET</value>
    </values>
    <values>
        <field>NamedCredential__c</field>
        <value xsi:type="xsd:string">MockServer</value>
    </values>
    <values>
        <field>Protocol__c</field>
        <value xsi:type="xsd:string">REST</value>
    </values>
    <values>
        <field>TemplateJSONBody__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>TimeoutMs__c</field>
        <value xsi:type="xsd:double">20000.0</value>
    </values>
    <values>
        <field>Type__c</field>
        <value xsi:type="xsd:string">Synch</value>
    </values>
    <values>
        <field>Version__c</field>
        <value xsi:type="xsd:double">1.0</value>
    </values>
</CustomMetadata>
