// Shared utilities for dxPegaInspector

export function parseJSON(input) {
    try {
        if (typeof input === 'string') return JSON.parse(input);
        return input;
    } catch (e) {
        return { __error: String(e) };
    }
}
export function isObject(x) { return x && typeof x === 'object' && !Array.isArray(x); }
export function csvEscape(v) {
    const s = v === undefined || v === null ? '' : String(v);
    if (s.includes(',') || s.includes('\n') || s.includes('"')) {
        return '"' + s.replaceAll('"', '""') + '"';
    }
    return s;
}
export function toCSV(rows, headers) {
    const cols = headers || Object.keys(rows[0] || {});
    const head = cols.join(',');
    const body = rows.map(r => cols.map(c => csvEscape(r[c])).join(',')).join('\n');
    return head + '\n' + body;
}
export function download(filename, content, type = 'text/plain') {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
}
export function deepGet(obj, path) {
    if (!path) return undefined;
    const parts = path.replace(/\[(\d+)\]/g, '.$1').split('.');
    let cur = obj;
    for (const p of parts) {
        if (!p) continue;
        if (!isObject(cur) && !Array.isArray(cur)) return undefined;
        cur = cur[p];
        if (cur === undefined) return undefined;
    }
    return cur;
}
export function indexPayload(root) {
    const fields = [];
    const views = [];
    const actions = [];
    const paragraphs = [];
    const nodes = [];
    const stack = [{ node: root, path: '$' }];
    const pushNode = (type, value, path) => { const id = nodes.length + 1; nodes.push({ id, type, path, value }); return id; };
    const pushField = (field, path) => {
        const ctrl = field.control || {};
        const modes = Array.isArray(ctrl.modes) ? ctrl.modes.map(m => m.modeType).join('|') : '';
        const customStr = JSON.stringify(field.customAttributes || {});
        fields.push({
            path,
            label: field.label ?? '',
            showLabel: field.showLabel ?? '',
            type: field.type ?? '',
            reference: field.reference ?? '',
            fieldID: field.fieldID ?? '',
            required: !!field.required,
            readOnly: !!field.readOnly,
            visible: !!field.visible,
            controlType: ctrl.type ?? '',
            controlModes: modes,
            labelFormat: field.labelFormat ?? '',
            value: field.value ?? '',
            testID: field.testID ?? '',
            customAttributes: customStr,
            _customParsed: (() => { try { return JSON.parse(customStr || '{}'); } catch { return {}; } })(),
        });
    };
    const pushView = (view, path) => { views.push({ path, viewID: view.viewID ?? '', name: view.name ?? '', appliesTo: view.appliesTo ?? '', visible: !!view.visible, title: view.title ?? '', reference: view.reference ?? '', }); };
    const pushActions = (control, path) => {
        const sets = control.actionSets || [];
        sets.forEach((set, idx) => {
            const evs = (set.events || []).map(e => e.event).join('|');
            (set.actions || []).forEach((a, j) => {
                actions.push({ path: path + `.actionSets[${idx}].actions[${j}]`, controlType: control.type || '', label: control.label || '', event: evs, action: a.action || '', actionName: (a.actionProcess && (a.actionProcess.actionName || a.actionProcess.name)) || '', });
            });
        });
    };
    while (stack.length) {
        const { node, path } = stack.pop();
        if (!isObject(node)) continue;
        if (node.view && isObject(node.view)) { pushNode('viewWrapper', node, path); pushView(node.view, `${path}.view`); stack.push({ node: node.view, path: `${path}.view` }); continue; }
        if (node.layout && isObject(node.layout)) { pushNode('layoutWrapper', node, path); stack.push({ node: node.layout, path: `${path}.layout` }); continue; }
        if (node.field && isObject(node.field)) { pushNode('fieldWrapper', node, path); pushField(node.field, `${path}.field`); if (isObject(node.field.control)) { pushActions(node.field.control, `${path}.field.control`); } }
        if (node.caption && isObject(node.caption)) { pushNode('caption', node.caption, `${path}.caption`); }
        if (node.paragraph && isObject(node.paragraph)) { pushNode('paragraph', node.paragraph, `${path}.paragraph`); const raw = node.paragraph.value || ''; const text = String(raw).replace(/<[^>]*>/g, '').trim(); paragraphs.push({ path: `${path}.paragraph`, id: node.paragraph.paragraphID || '', text }); }
        for (const [k, v] of Object.entries(node)) {
            if (k === 'groups' && Array.isArray(v)) { v.forEach((item, i) => stack.push({ node: item, path: `${path}.groups[${i}]` })); continue; }
            if (['modes', 'actionSets', 'actions', 'events'].includes(k) && Array.isArray(v)) { continue; }
            if (isObject(v)) stack.push({ node: v, path: `${path}.${k}` });
            if (Array.isArray(v)) v.forEach((item, i) => isObject(item) && stack.push({ node: item, path: `${path}.${k}[${i}]` }));
        }
    }
    return { fields, views, actions, paragraphs, nodes };
}
export function parseQuery(q) {
    if (!q || !q.trim()) return [];
    const tokens = [];
    const parts = q.match(/\/.*?\/[imuy]*|\S+/g) || [];
    for (const p of parts) {
        const m = p.match(/^([^:]+):(.*)$/);
        if (m) {
            const key = m[1];
            const rawVal = m[2];
            if (/^\/.*\/[imuy]*$/.test(rawVal)) { const r = rawVal.lastIndexOf('/'); const body = rawVal.slice(1, r); const flags = rawVal.slice(r + 1); tokens.push({ type: 'kv_regex', key, re: new RegExp(body, flags) }); }
            else { let v = rawVal; if (v === 'true') v = true; if (v === 'false') v = false; tokens.push({ type: 'kv', key, value: v }); }
        } else if (/^\/.*\/[imuy]*$/.test(p)) { const r = p.lastIndexOf('/'); const body = p.slice(1, r); const flags = p.slice(r + 1); tokens.push({ type: 'regex', re: new RegExp(body, flags) }); }
        else { tokens.push({ type: 'text', value: p.toLowerCase() }); }
    }
    return tokens;
}
export function rowMatches(row, tokens) {
    if (!tokens.length) return true;
    const allText = Object.values(row).map(v => String(v ?? '')).join(' ').toLowerCase();
    for (const t of tokens) {
        if (t.type === 'text') { if (!allText.includes(t.value)) return false; }
        else if (t.type === 'regex') { if (!t.re.test(allText)) return false; }
        else if (t.type === 'kv' || t.type === 'kv_regex') {
            const key = t.key;
            if (key.startsWith('attr.')) { const path = key.slice(5); const val = deepGet(row._customParsed || {}, path); if (t.type === 'kv_regex') { if (!t.re.test(String(val ?? ''))) return false; } else { if (String(val ?? '').toLowerCase().indexOf(String(t.value).toLowerCase()) === -1) return false; } }
            else { const val = row[key]; if (t.type === 'kv_regex') { if (!t.re.test(String(val ?? ''))) return false; } else { if (String(val ?? '').toLowerCase().indexOf(String(t.value).toLowerCase()) === -1) return false; } }
        }
    }
    return true;
}
export function keyField(f) { return f.path || f.testID || f.fieldID || ''; }
export function keyView(v) { return v.path || v.viewID || v.name || ''; }
export function keyAction(a) { return a.path || `${a.controlType}:${a.label}:${a.event}:${a.action}:${a.actionName}`; }
export function diffByKey(a, b, keyFn) {
    const A = new Map(a.map(x => [keyFn(x), x]));
    const B = new Map(b.map(x => [keyFn(x), x]));
    const added = []; const removed = []; const changed = [];
    for (const [k, v] of B.entries()) if (!A.has(k)) added.push(v);
    for (const [k, v] of A.entries()) if (!B.has(k)) removed.push(v);
    for (const [k, va] of A.entries()) { const vb = B.get(k); if (!vb) continue; if (JSON.stringify(va) !== JSON.stringify(vb)) { changed.push({ before: va, after: vb, key: k }); } }
    return { added, removed, changed };
}
