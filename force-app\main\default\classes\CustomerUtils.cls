public with sharing class CustomerUtils {
  // ===== Input =====
  public class CustomerInput {
    @AuraEnabled
    public Id caseId;
    @AuraEnabled
    public Id accountId;
  }

  // ===== Instance API (riusabile in Apex, contiene le query) =====

  public CustomerWrapper buildByCaseId(Id caseId) {
    if (caseId == null) {
      throw new AuraHandledException('caseId obbligatorio.');
    }

    // Case -> AccountId (+ PersonContactId via relazione)
    List<Case> caseRecords = [
      SELECT Id, AccountId, Account.PersonContactId
      FROM Case
      WHERE Id = :caseId
      LIMIT 1
    ];
    caseRecords = (List<Case>) Security.stripInaccessible(
        AccessType.READABLE,
        caseRecords
      )
      .getRecords();
    if (caseRecords.isEmpty()) {
      throw new AuraHandledException('Case non trovato o non accessibile.');
    }

    Case caseRecord = caseRecords[0];
    if (caseRecord.AccountId == null) {
      throw new AuraHandledException(
        'Il Case indicato non ha un Account associato (AccountId nullo).'
      );
    }

    // Account
    Account account = CustomerQuery.queryAccountById(caseRecord.AccountId);

    // Contact da PersonContactId
    Contact contact = CustomerQuery.queryContactById(
      (account != null) ? account.PersonContactId : null
    );

    // Opportunities dell'account
    List<Opportunity> opportunities = CustomerQuery.queryOpportunitiesByAccountId(
      caseRecord.AccountId
    );

    // Quotes dalle opportunities
    List<Quote> quotes = CustomerQuery.queryQuotesByOpportunities(
      opportunities
    );
    // Opportunity Coverages dalle quotes
    List<OpportunityCoverage__c> opportunityCoverages = CustomerQuery.queryOpportunityCoveragesByQuotes(
      quotes
    );

    CustomerWrapper customerWrapper = new CustomerWrapper();
    customerWrapper.account = account;
    customerWrapper.contact = contact;
    customerWrapper.opportunity = opportunities;
    customerWrapper.quote = quotes;
    customerWrapper.opportunityCoverages = opportunityCoverages;
    // Assets
    List<Asset> assets = CustomerQuery.queryAssetsByAccountId(
      caseRecord.AccountId
    );
    customerWrapper.assets = assets;
    customerWrapper.assetsData = CustomerBuild.buildAssetsData(assets);
    // Financial relations (FinServ__AccountAccountRelation__c)
    List<FinServ__AccountAccountRelation__c> accountFinancials = CustomerQuery.queryAccountFinancialsByAccountId(
      caseRecord.AccountId
    );
    customerWrapper.accountFinancials = accountFinancials;
    // Account details (AccountDetails__c)
    List<AccountDetails__c> accountDetails = CustomerQuery.queryAccountDetailsByAccountId(
      caseRecord.AccountId
    );
    customerWrapper.accountDetails = accountDetails;
    // Account details NPI (AccountDetailsNPI__c)
    List<AccountDetailsNPI__c> accountDetailsNPI = CustomerQuery.queryAccountDetailsNPIByAccountId(
      caseRecord.AccountId
    );
    customerWrapper.accountDetailsNPI = accountDetailsNPI;
    customerWrapper.accountFinancialData = CustomerBuild.buildAccountFinancialData(
      accountFinancials,
      accountDetails,
      accountDetailsNPI
    );

    // Attach assets to societies/agencies from root assetsData codes
    enrichFinancialDataWithAssets(customerWrapper);

    customerWrapper.opportunitiesData = CustomerBuild.buildOpportunitiesData(
      opportunities,
      quotes,
      opportunityCoverages
    );
    return customerWrapper;
  }

  public CustomerWrapper buildByAccountId(Id accountId) {
    if (accountId == null) {
      throw new AuraHandledException('accountId obbligatorio.');
    }

    Account account = CustomerQuery.queryAccountById(accountId);
    Contact contact = CustomerQuery.queryContactById(
      (account != null) ? account.PersonContactId : null
    );
    List<Opportunity> opportunities = CustomerQuery.queryOpportunitiesByAccountId(
      accountId
    );
    List<Quote> quotes = CustomerQuery.queryQuotesByOpportunities(
      opportunities
    );
    List<OpportunityCoverage__c> opportunityCoverages = CustomerQuery.queryOpportunityCoveragesByQuotes(
      quotes
    );

    CustomerWrapper customerWrapper = new CustomerWrapper();
    customerWrapper.account = account;
    customerWrapper.contact = contact;
    customerWrapper.opportunity = opportunities;
    customerWrapper.quote = quotes;
    customerWrapper.opportunityCoverages = opportunityCoverages;
    // Assets
    List<Asset> assets = CustomerQuery.queryAssetsByAccountId(accountId);
    customerWrapper.assets = assets;
    customerWrapper.assetsData = CustomerBuild.buildAssetsData(assets);
    // Financial relations (FinServ__AccountAccountRelation__c)
    List<FinServ__AccountAccountRelation__c> accountFinancials = CustomerQuery.queryAccountFinancialsByAccountId(
      accountId
    );
    customerWrapper.accountFinancials = accountFinancials;
    // Account details (AccountDetails__c)
    List<AccountDetails__c> accountDetails = CustomerQuery.queryAccountDetailsByAccountId(
      accountId
    );
    customerWrapper.accountDetails = accountDetails;
    // Account details NPI (AccountDetailsNPI__c)
    List<AccountDetailsNPI__c> accountDetailsNPI = CustomerQuery.queryAccountDetailsNPIByAccountId(
      accountId
    );
    customerWrapper.accountDetailsNPI = accountDetailsNPI;
    customerWrapper.accountFinancialData = CustomerBuild.buildAccountFinancialData(
      accountFinancials,
      accountDetails,
      accountDetailsNPI
    );

    // Attach assets to societies/agencies from root assetsData codes
    enrichFinancialDataWithAssets(customerWrapper);

    customerWrapper.opportunitiesData = CustomerBuild.buildOpportunitiesData(
      opportunities,
      quotes,
      opportunityCoverages
    );
    return customerWrapper;
  }

  // ===== Private query helpers (non statici) =====

  private Account queryAccountById(Id accountId) {
    return CustomerQuery.queryAccountById(accountId);
  }

  private Contact queryContactById(Id contactId) {
    return CustomerQuery.queryContactById(contactId);
  }

  private List<Opportunity> queryOpportunitiesByAccountId(Id accountId) {
    return CustomerQuery.queryOpportunitiesByAccountId(accountId);
  }

  private List<Quote> queryQuotesByOpportunities(
    List<Opportunity> opportunities
  ) {
    return CustomerQuery.queryQuotesByOpportunities(opportunities);
  }

  private List<Quote> queryQuotesByOpportunityIds(Set<Id> opportunityIds) {
    return CustomerQuery.queryQuotesByOpportunityIds(opportunityIds);
  }

  // ===== Additional query: OpportunityCoverages by Quote Ids =====
  private List<OpportunityCoverage__c> queryOpportunityCoveragesByQuotes(
    List<Quote> quotes
  ) {
    return CustomerQuery.queryOpportunityCoveragesByQuotes(quotes);
  }

  private List<OpportunityCoverage__c> queryOpportunityCoveragesByQuoteIds(
    Set<Id> quoteIds
  ) {
    return CustomerQuery.queryOpportunityCoveragesByQuoteIds(quoteIds);
  }

  private List<Asset> queryAssetsByAccountId(Id accountId) {
    return CustomerQuery.queryAssetsByAccountId(accountId);
  }

  // Map root assetsData to AccountFinancialData.societies[*].assets and agencies[*].assets
  private void enrichFinancialDataWithAssets(CustomerWrapper wrapper) {
    if (wrapper == null || wrapper.accountFinancialData == null)
      return;
    Map<String, List<Asset>> bySociety = new Map<String, List<Asset>>();
    Map<String, List<Asset>> byAgency = new Map<String, List<Asset>>();

    for (
      CustomerWrapper.AssetData ad : (wrapper.assetsData == null
        ? new List<CustomerWrapper.AssetData>()
        : wrapper.assetsData)
    ) {
      if (ad == null || ad.asset == null)
        continue;
      if (String.isNotBlank(ad.societyCode)) {
        if (!bySociety.containsKey(ad.societyCode))
          bySociety.put(ad.societyCode, new List<Asset>());
        bySociety.get(ad.societyCode).add(ad.asset);
      } else if (String.isNotBlank(ad.agencyCode)) {
        if (!byAgency.containsKey(ad.agencyCode))
          byAgency.put(ad.agencyCode, new List<Asset>());
        byAgency.get(ad.agencyCode).add(ad.asset);
      }
    }

    if (wrapper.accountFinancialData.societies != null) {
      for (String code : wrapper.accountFinancialData.societies.keySet()) {
        CustomerWrapper.SocietyData s = wrapper.accountFinancialData.societies.get(
          code
        );
        if (s == null)
          continue;
        s.assets = bySociety.containsKey(code)
          ? bySociety.get(code)
          : new List<Asset>();
      }
    }
    if (wrapper.accountFinancialData.agencies != null) {
      for (String code : wrapper.accountFinancialData.agencies.keySet()) {
        CustomerWrapper.AgencyData a = wrapper.accountFinancialData.agencies.get(
          code
        );
        if (a == null)
          continue;
        a.assets = byAgency.containsKey(code)
          ? byAgency.get(code)
          : new List<Asset>();
      }
    }
  }

  // ===== Additional query: Account Financial Relations by Account Id =====
  private List<FinServ__AccountAccountRelation__c> queryAccountFinancialsByAccountId(
    Id accountId
  ) {
    return CustomerQuery.queryAccountFinancialsByAccountId(accountId);
  }

  // ===== Additional query: AccountDetails by Account Id =====
  private List<AccountDetails__c> queryAccountDetailsByAccountId(Id accountId) {
    return CustomerQuery.queryAccountDetailsByAccountId(accountId);
  }

  // ===== Additional query: AccountDetailsNPI by Account Id =====
  private List<AccountDetailsNPI__c> queryAccountDetailsNPIByAccountId(
    Id accountId
  ) {
    return CustomerQuery.queryAccountDetailsNPIByAccountId(accountId);
  }

  // ===== Builder for nested structure (opportunitiesData) =====
  private List<CustomerWrapper.OpportunityData> buildOpportunitiesData(
    List<Opportunity> opportunities,
    List<Quote> quotes,
    List<OpportunityCoverage__c> opportunityCoverages
  ) {
    return CustomerBuild.buildOpportunitiesData(
      opportunities,
      quotes,
      opportunityCoverages
    );
  }

  // ===== Builder for nested structure (AccountFinancialData) =====
  private CustomerWrapper.AccountFinancialData buildAccountFinancialData(
    List<FinServ__AccountAccountRelation__c> accountFinancials,
    List<AccountDetails__c> accountDetails,
    List<AccountDetailsNPI__c> accountDetailsNPI
  ) {
    return CustomerBuild.buildAccountFinancialData(
      accountFinancials,
      accountDetails,
      accountDetailsNPI
    );
  }

  // ===== Static AuraEnabled wrappers (delegano, non contengono query) =====

  @AuraEnabled(cacheable=true)
  public static CustomerWrapper getCustomerByCaseId(Id caseId) {
    return new CustomerUtils().buildByCaseId(caseId);
  }

  @AuraEnabled(cacheable=true)
  public static CustomerWrapper getCustomerByAccountId(Id accountId) {
    return new CustomerUtils().buildByAccountId(accountId);
  }

  @AuraEnabled(cacheable=true)
  public static CustomerWrapper getCustomerByInput(String input) {
    if (input == null) {
      throw new AuraHandledException('Input JSON non può essere nullo.');
    }
    CustomerInput customerInput = (CustomerInput) JSON.deserialize(
      input,
      CustomerInput.class
    );
    if (
      customerInput == null ||
      (customerInput.caseId == null &&
      customerInput.accountId == null)
    ) {
      throw new AuraHandledException('Specificare almeno caseId o accountId.');
    }
    return (customerInput.caseId != null)
      ? new CustomerUtils().buildByCaseId(customerInput.caseId)
      : new CustomerUtils().buildByAccountId(customerInput.accountId);
  }

  // ===== Public static utility methods (riusabili in Apex; no SOQL qui) =====

  public static Account getAccount(Id accountId) {
    return new CustomerUtils().queryAccountById(accountId);
  }

  public static Contact getContact(Id contactId) {
    return new CustomerUtils().queryContactById(contactId);
  }

  public static List<Opportunity> getOpportunitiesByAccount(Id accountId) {
    return new CustomerUtils().queryOpportunitiesByAccountId(accountId);
  }

  public static List<Quote> getQuotesByAccount(Id accountId) {
    CustomerUtils customerUtils = new CustomerUtils();
    List<Opportunity> opportunities = customerUtils.queryOpportunitiesByAccountId(
      accountId
    );
    return customerUtils.queryQuotesByOpportunities(opportunities);
  }

  public static List<Quote> getQuotesByOpportunityIds(Set<Id> opportunityIds) {
    return new CustomerUtils().queryQuotesByOpportunityIds(opportunityIds);
  }

  // New utility wrappers for new queries
  public static List<Quote> getQuotesByOpportunities(
    List<Opportunity> opportunities
  ) {
    return new CustomerUtils().queryQuotesByOpportunities(opportunities);
  }

  public static List<OpportunityCoverage__c> getOpportunityCoveragesByQuotes(
    List<Quote> quotes
  ) {
    return new CustomerUtils().queryOpportunityCoveragesByQuotes(quotes);
  }

  public static List<OpportunityCoverage__c> getOpportunityCoveragesByQuoteIds(
    Set<Id> quoteIds
  ) {
    return new CustomerUtils().queryOpportunityCoveragesByQuoteIds(quoteIds);
  }

  public static List<Asset> getAssetsByAccountId(Id accountId) {
    return new CustomerUtils().queryAssetsByAccountId(accountId);
  }
}
