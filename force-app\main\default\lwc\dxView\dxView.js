import { utils } from "c/dxUtils";
import { LightningElement, api } from "lwc";

export default class View extends LightningElement {
  _view = { visible: false };
  index;

  @api debug;

  @api
  get view() {
    return this._view;
  }

  set view(value) {
    this._view = value;
    this.handleViewChange();
    //console.log('Passed value:', value); // Log dell'intero valore
    //console.log('ViewID:', value?.viewID);
  }
 

  get hasGroups() {
    return this.view && this.view.groups;
  }

  get isVisible() {
    return !!this.view && !(this.view.visible === false);
  }

  get key() {
    return utils.generateKey("v");
  }

  get componentClass() {
    return `view ${this.debug ? "debug" : ""}`.trim();
  }

  handleViewChange() {
    // console.log("[TEST] view", utils.printObject(this.view));
  }
}
