public class CustomerBuild {
  // Builders extracted from CustomerUtils for readability and reuse.

  public class OpportunitiesInput {
    public List<Opportunity> opportunities;
    public List<Quote> quotes;
    public List<OpportunityCoverage__c> opportunityCoverages;
  }

  public static List<CustomerWrapper.OpportunityData> buildOpportunitiesData(
    List<Opportunity> opportunities,
    List<Quote> quotes,
    List<OpportunityCoverage__c> opportunityCoverages
  ) {
    List<CustomerWrapper.OpportunityData> result = new List<CustomerWrapper.OpportunityData>();
    if (opportunities == null)
      opportunities = new List<Opportunity>();
    if (quotes == null)
      quotes = new List<Quote>();
    if (opportunityCoverages == null)
      opportunityCoverages = new List<OpportunityCoverage__c>();

    Map<Id, List<Quote>> quotesByOpportunityId = new Map<Id, List<Quote>>();
    for (Quote quoteRecord : quotes) {
      if (quoteRecord == null || quoteRecord.OpportunityId == null)
        continue;
      if (!quotesByOpportunityId.containsKey(quoteRecord.OpportunityId)) {
        quotesByOpportunityId.put(quoteRecord.OpportunityId, new List<Quote>());
      }
      quotesByOpportunityId.get(quoteRecord.OpportunityId).add(quoteRecord);
    }

    Map<Id, List<OpportunityCoverage__c>> coveragesByQuoteId = new Map<Id, List<OpportunityCoverage__c>>();
    for (OpportunityCoverage__c coverageRecord : opportunityCoverages) {
      if (coverageRecord == null || coverageRecord.Quote__c == null)
        continue;
      if (!coveragesByQuoteId.containsKey(coverageRecord.Quote__c)) {
        coveragesByQuoteId.put(
          coverageRecord.Quote__c,
          new List<OpportunityCoverage__c>()
        );
      }
      coveragesByQuoteId.get(coverageRecord.Quote__c).add(coverageRecord);
    }

    for (Opportunity opportunityRecord : opportunities) {
      if (opportunityRecord == null)
        continue;
      CustomerWrapper.OpportunityData opportunityData = new CustomerWrapper.OpportunityData();
      opportunityData.opportunityId = opportunityRecord.Id;
      opportunityData.opportunity = opportunityRecord;
      opportunityData.quotes = quotesByOpportunityId.containsKey(
          opportunityRecord.Id
        )
        ? quotesByOpportunityId.get(opportunityRecord.Id)
        : new List<Quote>();
      for (Quote quoteRecord : opportunityData.quotes) {
        CustomerWrapper.QuoteData quoteData = new CustomerWrapper.QuoteData();
        quoteData.quoteId = quoteRecord.Id;
        quoteData.quote = quoteRecord;
        quoteData.opportunityCoverages = coveragesByQuoteId.containsKey(
            quoteRecord.Id
          )
          ? coveragesByQuoteId.get(quoteRecord.Id)
          : new List<OpportunityCoverage__c>();
        opportunityData.quotesData.add(quoteData);
      }
      result.add(opportunityData);
    }
    return result;
  }

  public static CustomerWrapper.AccountFinancialData buildAccountFinancialData(
    List<FinServ__AccountAccountRelation__c> accountFinancials,
    List<AccountDetails__c> accountDetails,
    List<AccountDetailsNPI__c> accountDetailsNPI
  ) {
    // Delegate to existing implementation in CustomerUtils to avoid duplication if desired.
    // For now, copy the logic to keep CustomerUtils lean.
    CustomerWrapper.AccountFinancialData accountFinancialData = new CustomerWrapper.AccountFinancialData();
    if (accountFinancials == null)
      accountFinancials = new List<FinServ__AccountAccountRelation__c>();
    if (accountDetails == null)
      accountDetails = new List<AccountDetails__c>();
    if (accountDetailsNPI == null)
      accountDetailsNPI = new List<AccountDetailsNPI__c>();

    for (
      FinServ__AccountAccountRelation__c accountAccountRelation : accountFinancials
    ) {
      if (accountAccountRelation == null)
        continue;
      String recordTypeDeveloperName = (accountAccountRelation.RecordType !=
        null)
        ? accountAccountRelation.RecordType.DeveloperName
        : null;
      String externalId = (String) accountAccountRelation.get(
        'FinServ__ExternalId__c'
      );
      if (String.isBlank(recordTypeDeveloperName) || String.isBlank(externalId))
        continue;

      String[] externalIdParts = externalId.split('_');
      if (externalIdParts.size() != 3)
        continue;
      String entityCode = externalIdParts[1] + '_' + externalIdParts[2];

      if (recordTypeDeveloperName == 'AccountSociety') {
        CustomerWrapper.SocietyData societyData = new CustomerWrapper.SocietyData();
        societyData.societyCode = entityCode;
        societyData.accountFinancialId = accountAccountRelation.Id;
        societyData.accountFinancial = accountAccountRelation;
        societyData.accountDetails = new List<AccountDetails__c>();
        societyData.accountDetailsData = new Map<String, CustomerWrapper.AccountDetailData>();

        Map<String, AccountDetailsNPI__c> accountDetailsNPIByRecordType = new Map<String, AccountDetailsNPI__c>();
        for (AccountDetailsNPI__c accountDetailsNPIRecord : accountDetailsNPI) {
          if (accountDetailsNPIRecord == null)
            continue;
          Id accountDetailsNPIRelationId = (Id) accountDetailsNPIRecord.get(
            'Relation__c'
          );
          if (accountDetailsNPIRelationId == accountAccountRelation.Id) {
            String accountDetailsNPIRecordTypeDeveloperName = (accountDetailsNPIRecord.RecordType !=
              null)
              ? accountDetailsNPIRecord.RecordType.DeveloperName
              : null;
            if (
              String.isNotBlank(accountDetailsNPIRecordTypeDeveloperName) &&
              !accountDetailsNPIByRecordType.containsKey(
                accountDetailsNPIRecordTypeDeveloperName
              )
            ) {
              accountDetailsNPIByRecordType.put(
                accountDetailsNPIRecordTypeDeveloperName,
                accountDetailsNPIRecord
              );
            }
          }
        }

        for (AccountDetails__c accountDetailsRecord : accountDetails) {
          if (accountDetailsRecord == null)
            continue;
          Id relationId = (Id) accountDetailsRecord.get('Relation__c');
          if (relationId == accountAccountRelation.Id) {
            societyData.accountDetails.add(accountDetailsRecord);
            String recordTypeDeveloperNameDetail = (accountDetailsRecord.RecordType !=
              null)
              ? accountDetailsRecord.RecordType.DeveloperName
              : null;
            if (String.isNotBlank(recordTypeDeveloperNameDetail)) {
              CustomerWrapper.AccountDetailData accountDetailData = new CustomerWrapper.AccountDetailData();
              accountDetailData.accountDetailId = accountDetailsRecord.Id;
              accountDetailData.accountDetail = accountDetailsRecord;
              accountDetailData.accountDetailNPI = accountDetailsNPIByRecordType.containsKey(
                  recordTypeDeveloperNameDetail
                )
                ? accountDetailsNPIByRecordType.get(
                    recordTypeDeveloperNameDetail
                  )
                : null;
              societyData.accountDetailsData.put(
                recordTypeDeveloperNameDetail,
                accountDetailData
              );
            }
          }
        }
        accountFinancialData.societies.put(entityCode, societyData);
      } else if (recordTypeDeveloperName == 'AccountAgency') {
        CustomerWrapper.AgencyData agencyData = new CustomerWrapper.AgencyData();
        agencyData.agencyCode = entityCode;
        agencyData.accountFinancialId = accountAccountRelation.Id;
        agencyData.accountFinancial = accountAccountRelation;
        accountFinancialData.agencies.put(entityCode, agencyData);
      }
    }
    return accountFinancialData;
  }

  // Build assetsData from assets list per docs/customer-structure.md
  public static List<CustomerWrapper.AssetData> buildAssetsData(
    List<Asset> assets
  ) {
    List<CustomerWrapper.AssetData> result = new List<CustomerWrapper.AssetData>();
    if (assets == null)
      return result;

    // Regex: (\w+)_([A-Za-z]+)_(\d+)_(\w+)
    Pattern p = Pattern.compile('^(\\w+)_([A-Za-z]+)_(\\d+)_(\\w+)$');

    for (Asset a : assets) {
      if (a == null)
        continue;
      CustomerWrapper.AssetData ad = new CustomerWrapper.AssetData();
      ad.assetId = a.Id;
      ad.asset = a;

      String ext = (String) a.get('ExternalId__c');
      if (String.isNotBlank(ext)) {
        Matcher m = p.matcher(ext);
        if (m.find()) {
          String group2 = m.group(2);
          String group3 = m.group(3);
          String entityCode = group2 + '_' + group3;
          String assetCode = m.group(4);
          if (group2 != null && group2.toUpperCase().startsWith('SOC')) {
            ad.societyCode = entityCode;
          } else {
            ad.agencyCode = entityCode;
          }
          ad.assetCode = assetCode;
        }
      }
      result.add(ad);
    }
    return result;
  }
}
