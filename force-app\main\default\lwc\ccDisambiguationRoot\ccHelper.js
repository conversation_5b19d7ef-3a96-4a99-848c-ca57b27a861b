/**
 * Reads a value from a nested path in the model.
 * @param {string} path - The path to read from, e.g., "Ambito.Bene.Pet"
 * @param {Object} model - The model object to read from
 * @returns {*} - The value at the specified path, or null if not found
 */
export function readPath(path, model) {
    const keys = path.split(".");
    return keys.reduce((obj, key) => (obj && obj[key] !== undefined ? obj[key] : null), model);
}

/**
 * Writes a value to a nested path in the model.
 * If the path does not exist, it will create the necessary objects.
 * @param {string} path - The path to write to, e.g., "Ambito.Bene.Pet"
 * @param {*} value - The value to write
 * @param {Object} model - The model object to modify
 * @returns {Object} - The modified model object
 */
export function writePath(path, value, model) {
    const keys = path.split(".");
    const lastKey = keys.pop();
    const target = keys.reduce((obj, key) => (obj[key] = obj[key] || {}), model);
    target[lastKey] = value;
    return model;
}

export function buildPayload(obj, prefix = "", result = {}) {
  if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      const newPrefix = `${prefix}(${index + 1})`;
      buildPayload(item, newPrefix, result);
    });
  } else if (obj !== null && typeof obj === "object") {
    Object.keys(obj).forEach((key) => {
      const value = obj[key];
      const newPrefix = prefix ? `${prefix}.${key}` : key;
      buildPayload(value, newPrefix, result);
    });
  } else {
    result[prefix] = obj;
  }

  return result;
}

export default { readPath, writePath, buildPayload };
