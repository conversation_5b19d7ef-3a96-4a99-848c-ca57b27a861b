<template>
  <template if:true={isDocumentationButton}>
    <!-- TODO: implementare documentation button -->
  </template>

  <template if:false={isDocumentationButton}>
    <div class="button-center-wrapper">
      <button disabled={disabled} onclick={handleClick} class={buttonControlClass} style={composedStyle}>
        <span if:false={labelFormatCss} class="button-text">{label}</span>
        <template if:true={labelFormatCss}>
          <c-dx-custom-text-styles content={label} text-css={field.labelFormat}></c-dx-custom-text-styles>
        </template>
      </button>
    </div>
  </template>
</template>