<template>
  <template if:true={debugEnabled}>
    <template if:true={isOpen}>
      <section class="dashboard">
        <div class="container">
          <template if:true={show.userUtils}>
            <c-uu-user-utils></c-uu-user-utils>
          </template>
          <template if:true={show.customerUtils}>
            <c-uu-customer-view></c-uu-customer-view>
          </template>
          <template if:true={show.ipUtils}>
            <c-ip-utils-debugger></c-ip-utils-debugger>
          </template>
          <template if:true={show.fakeDataUtils}>
            <c-uu-fake-data-generator></c-uu-fake-data-generator>
          </template>
          <template if:true={show.logsUtils}>
            <c-uu-json-accordion-list values={logs}></c-uu-json-accordion-list>
          </template>
        </div>
        <div class="cta-bar">
          <lightning-button-group>
            <template for:each={processViews} for:item="view">
              <lightning-button
                key={view.name}
                name={view.name}
                label={view.label}
                variant={view.variant}
                data-type={view.type}
                onclick={handleSelectView}
              ></lightning-button>
            </template>

            <lightning-button-icon icon-name="utility:close" alternative-text="Close" onclick={handleToggle}></lightning-button-icon>
          </lightning-button-group>
        </div>
      </section>
      
    </template>
    <template if:false={isOpen}>
      <div class="launcher">
        <div class="launcher-area">
          <lightning-button label="JPT" onclick={handleToggle}></lightning-button>
        </div>
      </div>
    </template>
  </template>
</template>

