import { api, track } from 'lwc';
import LightningModal from 'lightning/modal';

export default class IpSaveLabelModal extends LightningModal {
  @api defaultLabel = '';
  @track label = '';

  connectedCallback() {
    this.label = this.defaultLabel || '';
  }

  handleLabelChange = (e) => { this.label = e.target.value; }
  handleCancel = () => this.close(null);
  handleSave = () => this.close({ label: this.label });
}
