import { getFormState, markFieldTouched, setFieldValue } from 'c/dxFormState';
import { fireEvent } from 'c/pubsub';
import { utils } from 'c/dxUtils';
import { LightningElement, api } from 'lwc';

export default class FieldDateTime extends LightningElement {
  _field;

  @api
  decodedValue;

  @api parentLayout;
  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    setFieldValue(
      value.reference,
      value.value,
      value.type === 'date'
        ? value?.customAttributes?.validation || 'date'
        : value?.customAttributes?.validation || 'time',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    if(!this.readonly){
      return this.field?.label ? utils.decodeHTML(this.field.label) : '';
    } else { // se è read-only devo mostrare label piuttosto che dateTime
      return this.field?.value ? utils.formatDateByField(this.field.value, this.field) : '';
    }    
  }

  get required() {
    return this.field.required === true;
  }

  get readonly() {
    return this.field.readOnly === true;
  }

  get disabled() {
    return this.field.disabled;
  }

  get formatType() {
    return this.field.type == 'Date' ? 'date' : 'time';
  }

  get componentClass() {
    return `pxDateTime ${this.debug ? 'debug' : ''}`.trim();
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get formattedTime() {
    return this.formatType === 'date'
      ? this.decodedValue
      : this.decodedValue.substring(0, 2) + ':' + this.decodedValue.substring(2, 4);
  }

  get minDate() {
    let startAvailableDate = this.field.customAttributes?.startAvailableDate;
    return this.formatDate(startAvailableDate);
  }

  get maxDate() {
    let endAvailableDate = this.field.customAttributes?.endAvailableDate;
    return this.formatDate(endAvailableDate);
  }

  formatDate(date){
    if(!date) return;
    date = date.replace(/\D/g, ''); //pulizia data
    const availableYear = date.slice(0, 4);
    const availableMonth = date.slice(4, 6);
    const availableDay = date.slice(6, 8);
    return `${availableYear}-${availableMonth}-${availableDay}`; 
  }


  


  handleInputChange(evt) {
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('lightning-input');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    
    

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
    } else {
      container.classList.remove('invalid-input');
    }


    this.validateDateRange(container);
    fireEvent('handleFieldChanged', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout
    });
  }

  validateDateRange(container) {
  if(!container){
    container = this.template.querySelector('lightning-input');
  }

  if(!container) return;
  let inputValue = new Date(this.formatDate(container.value)); // formato yyyy-MM-dd
  let error = false;
  let minDateRange = this.minDate ? new Date(this.minDate) : null;
  let maxDateRange = this.maxDate ? new Date(this.maxDate) : null;

  if (minDateRange && inputValue < minDateRange) {
    container.setCustomValidity('La data selezionata è precedente alla data minima consentita');
    error = true;
  } else {
     if (maxDateRange && inputValue > maxDateRange) {
      container.setCustomValidity('La data selezionata è successiva alla data massima consentita');
      error = true;
    } else {
      container.setCustomValidity('');
    }
  }
  container.reportValidity();
  return error;
}

  renderedCallback() {
    this.validateDateRange();
  }

  
}