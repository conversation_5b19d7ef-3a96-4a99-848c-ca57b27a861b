<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">FIELD PX INTEGER:</p>
    <p if:true={debug} class="temporaryLabel">format: {format}</p>
    <p if:true={debug} class="temporaryLabel">labelFormat: {labelFormatDebug}</p>

    <template if:true={labelFormat}>
  <c-dx-custom-text-styles content={percentageValue} text-css={labelFormat}></c-dx-custom-text-styles>
    </template>

    <template if:false={labelFormat}>
      <span class={format}>{decodedValue}</span>
      <span class={format} if:true={isPercentuale}>%</span>
    </template>
  </div>
</template>