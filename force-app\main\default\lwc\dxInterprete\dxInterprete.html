<template>
  <template if:false={showFailurePage}>
    <div>
      <div class="interprete">
        <c-dx-view view={view} debug={debug}></c-dx-view>

        <c-dx-ui-modal is-open={showModal} modal-content={modalView}></c-dx-ui-modal>

        <div if:true={isLoading} class="spinner">
          <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
        </div>
      </div>
    </div>
  </template>

  <template if:true={showFailurePage}>
    <c-dx-page-error request={request} error-message={errorMessage} reload={reloadVisible}></c-dx-page-error>
  </template>
</template>