<template>
  <lightning-card title="User Utils" class="slds-m-around_medium section">
    <div class="slds-m-around_medium section">
      <lightning-input
        label="Search"
        placeholder="Enter User Id, Fiscal Code or User Code (10XXXXX1)"
        value={searchInput}
        onchange={handleSearchChange}
      ></lightning-input>
      <lightning-button-group>
        <lightning-button
          label="Cerca"
          onclick={handleSubmit}
          class="slds-m-top_small"
        ></lightning-button>
        <lightning-button
          label="Salvati"
          onclick={openHistory}
          class="slds-m-top_small"
        ></lightning-button>
      </lightning-button-group>
    </div>
    <template if:true={userInfo}>
      <c-uu-user-info-section user-info={userInfo}></c-uu-user-info-section>
    </template>
    <div class="slds-m-around_medium section">
      <lightning-button-group>
        <lightning-button
          label="Society Prefered"
          data-section="showSocietyPrefered"
          onclick={toggleSection}
        ></lightning-button>
        <lightning-button
          label="User"
          data-section="showUser"
          onclick={toggleSection}
        ></lightning-button>
        <lightning-button
          label="User Networks"
          data-section="showUserNetworks"
          onclick={toggleSection}
        ></lightning-button>
        <lightning-button
          label="UCA Users"
          data-section="showUcaUsers"
          onclick={toggleSection}
        ></lightning-button>
        <lightning-button
          label="Permission Sets"
          data-section="showPermissionSets"
          onclick={toggleSection}
        ></lightning-button>
      </lightning-button-group>
    </div>
    <template if:true={showSocietyPrefered}>
      <c-uu-network-section
        data={societyPreferedData}
        agency={agencyData}
        preferred-network={preferredNetworkData}
      ></c-uu-network-section>
    </template>
    <template if:true={showUser}>
      <c-uu-user-section user={userData}></c-uu-user-section>
    </template>
    <template if:true={showUserNetworks}>
      <div class="slds-m-around_medium section">
        <lightning-combobox
          name="companies"
          label="Companies"
          value={selectedCompany}
          options={companyOptions}
          onchange={handleCompanyChange}
        ></lightning-combobox>
        <template if:true={selectedNetworkData}>
          <c-uu-network-section
            data={selectedNetworkData}
            agency={selectedNetworkAgency}
            preferred-network={selectedNetworkPreferred}
          ></c-uu-network-section>
        </template>
      </div>
    </template>
    <template if:true={showUcaUsers}>
      <c-uu-uca-users users={ucaUsers}></c-uu-uca-users>
    </template>
    <template if:true={showPermissionSets}>
      <c-uu-permission-sets
        permission-sets={permissionSets}
      ></c-uu-permission-sets>
    </template>
    <template if:true={resultJson}>
      <div class="slds-m-around_medium section">
        <lightning-button-group>
          <lightning-button
            label={resultJsonButtonLabel}
            onclick={toggleResultJson}
          ></lightning-button>
          <lightning-button
            label="Copia"
            onclick={copyResultJson}
          ></lightning-button>
        </lightning-button-group>
        <template if:true={showResultJson}>
          <pre>{resultJson}</pre>
        </template>
      </div>
    </template>
    <template if:true={errorJson}>
      <div class="slds-m-around_medium section slds-text-color_error">
        <lightning-button-group>
          <lightning-button
            label={errorJsonButtonLabel}
            onclick={toggleErrorJson}
          ></lightning-button>
          <lightning-button
            label="Copia"
            onclick={copyErrorJson}
          ></lightning-button>
        </lightning-button-group>
        <template if:true={showErrorJson}>
          <pre>{errorJson}</pre>
        </template>
      </div>
    </template>
    <template if:true={showHistoryModal}>
      <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
        <div class="slds-modal__container">
          <header class="slds-modal__header">
            <h2 class="slds-modal__title slds-hyphenate">Salvati</h2>
          </header>
          <div class="slds-modal__content slds-p-around_medium">
            <template if:true={history.length}>
              <ul>
                <template for:each={history} for:item="item">
                  <li key={item.userId} class="slds-m-vertical_x-small">
                    <lightning-button
                      variant="base"
                      label={item.label}
                      data-id={item.userId}
                      onclick={handleHistorySelect}
                    ></lightning-button>
                  </li>
                </template>
              </ul>
            </template>
            <template if:false={history.length}>
              <p>Nessun elemento salvato</p>
            </template>
          </div>
          <footer class="slds-modal__footer">
            <lightning-button
              variant="neutral"
              label="Chiudi"
              onclick={closeHistory}
            ></lightning-button>
          </footer>
        </div>
      </section>
      <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
  </lightning-card>
</template>
