<template>
  <div class="footer-pu-container">
    <div class="d-flex align-center">

      <template if:true={hasFooterGroups}>
        <div class="containerFooter">
          <template for:each={footerLayout.groups} for:index="index" for:item="group">
            <div key={key}>
              <c-dx-groups groups={group}></c-dx-groups>
            </div>
          </template> 
        </div>     
      <div class="separator visible-tablet"></div>
      </template>

      <template if:true={secondaryButtonVisible}>
  <c-dx-field 
          field={footerSecondaryButton}
          class="visible-tablet">
  </c-dx-field>
      </template>

  <c-dx-field field={footerPositiveButton}></c-dx-field>
    </div>

    <template if:true={secondaryButtonVisible}>
  <c-dx-field 
        field={footerSecondaryButton}
        class="visible-mobile">
  </c-dx-field>
    </template>
  </div>

  <template if:true={showPlaceholder}>
    <div class="footer-pu-placehoder"></div>
  </template>
</template>