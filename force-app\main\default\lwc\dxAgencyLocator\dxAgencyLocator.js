import BffInterprete from "c/dxBffInterprete";
import { LightningElement, api, track } from "lwc";
import { fireEvent } from "c/pubsub";

export default class DxAgencyLocator extends LightningElement {
  static CATEGORY = 'AGENZIA';

  @track stepCorrente = "scelta_agenzia";
  @track areDataSetted = false;
  @track localhostMock = false;
  @track isVisible = true;
  @track isLoading = false;

  _field;
  _groups;
  _lastDataToSend = {};
  _locatorAction = false;

  @track agencies = [];
  @track allAgencies = [];
  @track formattedAgencies = [];

  bff = BffInterprete.getInstance();

  connectedCallback() {
  this.loadAllAgencies();
  }

  async loadAllAgencies() {
    try {
      this.isLoading = true;
      // Usa l'endpoint IP "agencyList"; uniforma il formato di risposta (poi/POIArray)
      const response = await this.bff.agencyList({ category: DxAgencyLocator.CATEGORY });
      const list = response?.poi || response?.POIArray || [];
      this.allAgencies = Array.isArray(list) ? list : [];
      this.areDataSetted = this.allAgencies.length > 0;
    } catch (error) {
      console.error("Errore fetch tutte agenzie:", error);
    } finally {
      this.isLoading = false;
    }
  }

  async loadNearestAgencies(lat, lng) {
    try {
      this.isLoading = true;
      const latNum = parseFloat(lat);
      const lngNum = parseFloat(lng);
      if (!isFinite(latNum) || !isFinite(lngNum)) {
        console.warn('Coordinate non numeriche per loadNearestAgencies', { lat, lng });
        return;
      }

      const response = await this.bff.agencyBound({
        category: DxAgencyLocator.CATEGORY,
        maxItem: '10',
        lat: latNum,
        lng: lngNum,
      });

      const list = response?.poi || response?.POIArray || [];
      this.agencies = Array.isArray(list) ? list : [];
      this.formatAgenciesForDisplay();
      this.areDataSetted = true;
    } catch (error) {
      console.error("Errore durante la fetch delle agenzie:", error);
    } finally {
      this.isLoading = false;
    }
  }

  formatAgenciesForDisplay() {
    this.formattedAgencies = (this.agencies || []).map((agency) => {
      const distance = agency?.distance ? `${agency.distance} km` : "";
      const address = `${agency?.address || ""}, ${agency?.zipCode || ""}, ${agency?.town || ""}`;
      const agencyName = agency?.management || agency?.name || `Agenzia ${agency?.code || ''}`;

      const latitude = agency?.latitude != null ? parseFloat(agency.latitude) : undefined;
      const longitude = agency?.longitude != null ? parseFloat(agency.longitude) : undefined;

      return {
        id: agency?.code,
        name: agencyName,
        address,
        distanceKm: distance,
        latitude: isFinite(latitude) ? latitude : undefined,
        longitude: isFinite(longitude) ? longitude : undefined,
        rawData: agency,
      };
    });
  }

  @api
  set field(value) {
    this._field = value;
    if (value) {
      this._processFieldData(value);
    }
  }

  get field() {
    return this._field;
  }

  @api
  set groups(value) {
    this._groups = value;
  }

  get groups() {
    return this._groups;
  }

  _processFieldData(field) {
    const customType = field.customAttributes?.customType || "";

    if (customType === "WorkshopLocator") {
      this.stepCorrente = "scelta_installatore";
    } else {
      if (field.customAttributes?.AgenziaLocator) {
        sessionStorage.setItem(
          "QUOTAZIONE_PEGA",
          JSON.stringify({
            datiAnagrafici: {
              prodottoLocator: field.customAttributes.AgenziaLocator,
            },
          }),
        );
      }
    }

    this._locatorAction =
      field.customAttributes?.componentID === "locatorSubmitAction";
    this._addFormGroupValue({ testValidatore: "" });
    this.areDataSetted = true;
  }

  handleComponentLoading() {
    this.dispatchEvent(new CustomEvent("componentready"));
  }

  handleLocationChange(event) {
    const { lat, lng } = event.detail;

    if (lat && lng) {
      this.loadNearestAgencies(lat, lng);
    } else {
      console.warn("Coordinate non valide ricevute da locatorWrapper");
    }
  }

  async _arricchisciDati(agency) {
    try {
      if (!agency || !agency.code) throw new Error("Agenzia non valida");

      const detailResponse = await this.bff.agencyDetail({ agency: agency.code });
      const arr = detailResponse?.POIArray || detailResponse?.poi || [];
      return Array.isArray(arr) && arr.length > 0 ? arr[0] : agency;
    } catch (e) {
      console.error("Errore arricchimento dati", String(e));
      return agency;
    }
  }

  _addFormGroupValue(dataToSend) {
    this._lastDataToSend = dataToSend;

    const persistentData = JSON.parse(
      sessionStorage.getItem("INTERPRETE_PERSISTENT_DATA") || "{}",
    );
    console.log("[AGENCY LOCATOR] Dati recuperati:", persistentData);

    const allReferences = {
      ...persistentData,
      ...dataToSend,
    };

    console.log("[AGENCY LOCATOR] Tutti i dati da inviare:", allReferences);

    const fieldWithReferences = {
      ...this._field,
      referencesToUpdate: allReferences,
    };

    const mockEvent = {
      target: {
        value: dataToSend["Agenzia.CodiceAgenzia"],
        dataset: {
          reference: "Agenzia.CodiceAgenzia",
        },
      },
    };

    fireEvent("handleFieldChanged", {
      evt: mockEvent,
      field: fieldWithReferences,
      parentLayout: this.parentLayout,
    });
    this.dispatchEvent(
      new CustomEvent("formchange", {
        detail: { values: dataToSend },
      }),
    );
  }

  async handleSelezioneAgenzia(event) {
    const agency = event.detail;
  const dati = await this._arricchisciDati(agency);

    const dataToSend = {
      "Agenzia.DescrizioneAgenzia": dati.management || dati.name || "-",
      "Agenzia.Telefono": dati.phoneNumber || "-",
      "Agenzia.Email": dati.email || dati.mail || "-",
      "Agenzia.Indirizzo.NomeStrada": dati.address || "-",
      "Agenzia.Indirizzo.Provincia": dati.provinceAcronym || "-",
      "Agenzia.Indirizzo.NumeroCivico": "-",
      "Agenzia.Indirizzo.Cap": dati.zipCode || "-",
      "Agenzia.Indirizzo.Comune": dati.town || "-",
      "Agenzia.CodiceAgenzia": dati.code || "-",
    };

    this._addFormGroupValue(dataToSend);
    this._callNextPage();
  }

  async handleSelezioneInstallatore(event) {
    const agency = event.detail;
    const dati = await this._arricchisciDati(agency);

    const dataToSend = {
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.NomeOfficina":
        dati.code_ext || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.TelefonoOfficina":
        dati.phoneNumber || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.EmailOfficina":
        dati.email || dati.mail || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NomeStrada":
        dati.address || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Provincia":
        dati.provinceAcronym || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NumeroCivico":
        "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Cap":
        dati.zipCode || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Comune":
        dati.town || "-",
      "GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.CodiceOfficina":
        dati.code || "-",
    };

    this._addFormGroupValue(dataToSend);
    this._callNextPage();
  }

  _callNextPage() {
    if (this._locatorAction) {
      this.dispatchEvent(new CustomEvent("nextstep"));
    }
  }

  handleMockAgenzia() {
    this.handleSelezioneAgenzia({ detail: this.mockAgenzia });
  }

  handleMockInstallatore() {
    this.handleSelezioneInstallatore({ detail: this.mockInstallatore });
  }

  disconnectedCallback() {
    if (this._lastDataToSend) {
    }
    sessionStorage.removeItem("QUOTAZIONE_PEGA");
  }
}
