import {
  BASE_ASSET_URL,
  ICON_MAPPER,
  ICON_MAPPER_URLS,
  IMG_DESKTOP,
  IMG_MOBILE,
  IMG_TABLET,
} from 'c/dxIconMapping';
import { fireEvent, registerListener, unregisterAllListeners } from 'c/pubsub';
import { utils } from 'c/dxUtils';
import { LightningElement, api, track } from 'lwc';


export default class PxIcon extends LightningElement {
  _field;
  @track type = 'icon';
  @track imgMobile;
  @track imgTablet;
  @track imgDesktop;

  @track tooltipVisible = false;
  @track tooltipText = '';
  @track overlayData = null;
  @track toolpData = {};
  @api groups;
  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.updateImageUrls();
  }

  get isIcon() {
    return (
      this.field?.customAttributes?.type === 'icon' ||
      (!this.field?.customAttributes?.type && this.field?.control?.type === 'pxIcon')
    );
  }

  get isImage() {
    return this.field?.customAttributes?.type === 'image';
  }

  get isLabelVisible() {
    return this.field.label && this.field.showLabel;
  }

  get resource() {
    return `${ICON_MAPPER[this.field?.customAttributes?.resource]} ${this.webResponsiveSize ? 'webResponsiveSize' : ''}`;
  }

  get componentClass() {
    return `pxIcon ${this.debug ? 'debug' : ''}`.trim();
  }

  get webResponsiveSize() {
    return utils.getCustomAttributeFromFieldUnsensitive(this.field, 'webResponsiveSize', undefined);
  }

  get responsiveSizeFormattedLabel() {
    const resource = this.field?.customAttributes?.resource;
    const iconMapped = ICON_MAPPER[resource];
    const imageUrl = ICON_MAPPER_URLS[iconMapped];

    let esito = `
      background-image: url("${imageUrl}");
      background-size: cover;
    `;

    if (this.webResponsiveSize) {
      const part = this.webResponsiveSize.split(' ');
      if (part.length === 3) {
        for (let i = 0; i < part.length; i++) {
          const size = part[i];
          const dimensione = size.substring(0, size.length - 1);
          const allineamento = size.substring(size.length - 1);

          const mapperTaglia = ['--desktop', '--tablet', '--mobile'];
          const mapperAllineamento = { L: '0 auto 0 0', C: '0 auto', R: '0 0 0 auto' };
          esito += `${mapperTaglia[i]}-size: ${dimensione}px; ${mapperTaglia[i]}-align: ${mapperAllineamento[allineamento]}; `;
        }
      }
    }

    return esito;
  }

  updateImageUrls() {
    if (this.isImage) {
      const resource = this.field?.customAttributes?.resource;

      const base = BASE_ASSET_URL;

      this.imgMobile = IMG_MOBILE[resource];
      this.imgTablet = IMG_TABLET[resource];
      this.imgDesktop = IMG_DESKTOP[resource];
      if (!this.imgMobile) {
        this.imgMobile = `${base}/NextAssets/interprete-pu/immagini-pu/Mobile/${resource}.png`;
      }
      if (!this.imgTablet) {
        this.imgTablet = `${base}/NextAssets/interprete-pu/immagini-pu/Tablet/${resource}.png`;
      }
      if (!this.imgDesktop) {
        this.imgDesktop = `${base}/NextAssets/interprete-pu/immagini-pu/Desktop/${resource}.png`;
      }
    }
  }

  get isTooltipVisible() {
    return this.tooltipVisible;
  }

  handleOverlayAction(data, response) {
    const target = data?.actionData?.target;
    if (target === 'overlay') {
      this.overlayData = {
        actionId: data.actionId,
        referencesToIUpdate: data.referencesToUpdate,
        response,
      };

      const tooltipText = this.extractCaption(response?.pegaBodyResponse?.view?.groups || []);
      this.tooltipText = tooltipText;
      this.tooltipVisible = true;

      fireEvent(this.pageRef, 'showtooltip', {
        text: tooltipText,
        context: this.overlayData,
      });
    }
  }

  handleCloseTooltip() {
    this.tooltipVisible = false;
    this.tooltipText = '';
    this.overlayData = null;
  }

  handleClick(evt) {
    fireEvent('handleFieldClicked', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }
  connectedCallback() {
    registerListener(`overlay-response-${this.field.reference}`, this.handleOverlayResponse, this);
  }

  disconnectedCallback() {
    unregisterAllListeners(this);
  }

  handleOverlayResponse(payload) {
    this.toolpData = payload;
    this.tooltipVisible = true;
  }
}
