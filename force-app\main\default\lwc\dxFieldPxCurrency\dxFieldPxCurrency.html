<template>
  <div class={componentClass}>
  <c-dx-custom-text-styles content={label} text-css={labelFormat}></c-dx-custom-text-styles>
    <div class="px-currency-container">
    </div>
    <p if:true={debug} class="temporaryLabel">PX CURRENCY: {label}</p>
    <template if:false={isReadonly}>
      <div class="input-field-container">
        <input type="number" name="value" data-reference={field.reference} min={minValue} max={maxValue}
          disabled={disabled} class={textAlign} value={decodedValue} onblur={handleInputChange}
          placeholder={paceholder} />
      </div>
      <template if:false={isValid}>
        <div class="error-message">
          <i class="icon-Attenzione-pieno bd-icona"></i>
          <span class="testo-non-valido">{errorMessage}</span>
        </div>
      </template>
    </template>
    <template if:true={isReadonly}>
      <div class={labelFormat}>
  <c-dx-custom-text-styles content={value} text-css={labelFormat}></c-dx-custom-text-styles>
      </div>
    </template>
  </div>
</template>