// declare module "utils" {
//   /** Utility functions. */
//   export const utils: {
//     printObject: typeof printObject;
//     generateKey: typeof generateKey;
//     decodeHTML: typeof decodeHTML;
//     generateRandomRequestId: typeof generateRandomRequestId;
//     apiRequest: typeof apiRequest;
//   };

//   /**
//    * Prints the properties of an object.
//    *
//    * @param obj - The object to print.
//    * @returns The JSON string or null if an error occurs.
//    */
//   function printObject(obj: object): string | null;

//   /**
//    * Generates a unique key with an optional prefix.
//    *
//    * @param prefix - The prefix for the key. Default is "k".
//    * @returns The generated key.
//    */
//   function generateKey(prefix?: string): string;

//   /**
//    * Decodes HTML entities in a string.
//    *
//    * @param value - The string to decode.
//    * @returns The decoded string.
//    */
//   function decodeHTML(value: string): string;

//   /**
//    * Generates a random request ID.
//    *
//    * @returns The generated request ID.
//    */
//   function generateRandomRequestId(): string;

//   /**
//    * Makes an API request.
//    *
//    * @param method - The HTTP method to use.
//    * @param url - The URL to send the request to.
//    * @param headers - The headers to include in the request.
//    * @param body - The body of the request.
//    * @returns A promise that resolves to the response object.
//    */
//   function apiRequest(
//     method: string,
//     url: string,
//     headers: Record<string, string>,
//     body: any
//   ): Promise<any>;

//   /** Field types. */
//   export const FIELD_TYPES: {
//     caption: string;
//     link: string;
//   };
// }
