<template>
    <lightning-card > 
          <div class="autocomplete-wrapper">
            <input
              type="text"
              class="autocomplete-input"
              placeholder="Città, Indirizzo, CAP"
              value={inputValue}
              oninput={handleInputChange}
            />
          
            <template if:true={predictions}>
              <ul class="autocomplete-dropdown">
                <template for:each={predictions} for:item="prediction">
                  <li
                    key={prediction.place_id}
                    class="autocomplete-item"
                    data-place-id={prediction.place_id}
                    onclick={handleSelectPrediction}
                  >
                    {prediction.description}
                  </li>
                </template>
              </ul>
            </template>
          </div>
  
          <!-- Commentary for implementing decision logic for the contact center -->
          <!-- <lightning-map
              map-markers={mapMarkers}
              zoom-level="10"
              center={mapCenter}
              class="map"
              list-view="hidden"
              onmarkerselect={handleAgencyClick}>
          </lightning-map>  -->
        
          <template if:true={agenciesList}>
            <div class="tpd_wrapper_container_list_agencies">
              <div class="tpd_listAgencies">
                <template for:each={agenciesList} for:item="agency">
                  <div key={agency.id} class="tpd_item_agency">
          
                    <div class="tpd_agency_left">
                      <p class="tpd_agency_name tpd_bold">
                        <span>{agency.name}</span>
                      </p>
                      <p class="tpd_agency_address">{agency.address}</p>
                    </div>
          
                    <div class="tpd_agency_wrapper">
                      <div class="tpd_align_center">
                        <p>{agency.distanceKm}</p>
                      </div>
                      <button class="tpd_agency_btn" data-id={agency.id} data-reference="Agenzia.CodiceAgenzia" onclick={handleAgencySelect}>
                        Seleziona
                      </button>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
  </lightning-card>
  </template>