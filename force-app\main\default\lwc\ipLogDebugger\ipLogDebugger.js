import { LightningElement, track, api } from "lwc";
import { loadItems, saveItems } from "c/uuLocalStorageUtils";

export default class IpLogDebugger extends LightningElement {
  @track inputText = "";
  @track parsedLog;
  @track steps = [];
  @track summary = {};
  @track rawSections = [];
  @track errorMessage = "";
  @track activeStepSections = [];
  @track showSummaryJson = false;

  // Local storage key for persisting the last input text
  STORAGE_KEY = "ipLogDebuggerLastInput";

  connectedCallback() {
    // Load last saved input (if any) and parse automatically
    const items = loadItems(this.STORAGE_KEY);
    const last = Array.isArray(items) && items.length ? items[0] : null;
    if (last && typeof last.text === "string" && last.text.trim()) {
      this.inputText = last.text;
      // Best-effort parse; errors will be shown if JSON is invalid
      try {
        this.parse();
      } catch (e) {
        // ignore: parse() already handles errors and sets errorMessage
      }
    }
  }

  // Optional pre-population: you can pass a JSON string or object
  _value;
  @api
  get value() {
    return this._value;
  }
  set value(v) {
    this._value = v;
    if (v) {
      try {
        const obj = typeof v === "string" ? JSON.parse(v) : v;
        this.inputText = JSON.stringify(obj, null, 2);
        this.parse();
      } catch (e) {
        // ignore and let user paste
      }
    }
  }

  handleInputChange(event) {
    this.inputText = event.target.value;
    // Persist raw input so user doesn't lose the draft
    saveItems(this.STORAGE_KEY, [{ text: this.inputText, ts: Date.now() }]);
  }

  clearAll() {
    this.inputText = "";
    this.parsedLog = undefined;
    this.steps = [];
    this.summary = {};
    this.rawSections = [];
    this.errorMessage = "";
    this.activeStepSections = [];
    // Clear persisted value
    saveItems(this.STORAGE_KEY, []);
  }

  toggleSummaryJson() {
    this.showSummaryJson = !this.showSummaryJson;
  }

  parse() {
    this.errorMessage = "";
    try {
      const log = this.inputText
        ? JSON.parse(this.inputText)
        : this._value || {};
      this.parsedLog = log;
      this.computeSummary(log);
      this.computeSteps(log);
      this.computeRawSections(log);
      // Save the last valid JSON entered for convenience
      saveItems(this.STORAGE_KEY, [{ text: this.inputText, ts: Date.now() }]);
    } catch (e) {
      this.errorMessage = `JSON non valido: ${e.message}`;
      this.parsedLog = undefined;
      this.steps = [];
      this.summary = {};
      this.rawSections = [];
    }
  }

  computeSummary(log) {
    const elapsedTimeActual =
      log.elapsedTimeActual ?? log.fullDataJson?.elapsedTimeActual;
    const elapsedTimeCPU =
      log.elapsedTimeCPU ?? log.fullDataJson?.elapsedTimeCPU;
    const request =
      log.originalInput?.request ||
      log.fullDataJson?.request ||
      log["ParamsTransformDebug"]?.Input?.request ||
      log["NormalizeAddressCalloutDebug"]?.Input?.request ||
      undefined;
    const response = log.response ?? log.fullDataJson?.response;
    const debugLog = Array.isArray(log.debugLog) ? log.debugLog : [];

    this.summary = {
      elapsedTimeActual,
      elapsedTimeCPU,
      request,
      response,
      debugLog,
    };
  }

  computeSteps(log) {
    const sequence = Array.isArray(log.executionSequence)
      ? log.executionSequence
      : [];
    const steps = sequence.map((name, idx) => {
      const out = log[name];
      const dbg = log[`${name}Debug`];
      const status = dbg?.Status ?? (out?.error ? false : undefined);
      const type = dbg?.Type;
      const elapsedTime = dbg?.ElapsedTime;
      const elapsedTimeCPU = dbg?.ElapsedTimeCPU;
      const input = dbg?.Input;
      const options = dbg?.Options;
      const hasError = Boolean(
        out?.error || out?.errorCode || status === false,
      );
      const label = `${idx + 1}. ${name}`;
      const headerClass = hasError ? "step-header error" : "step-header";
      const statusLabel =
        status === true ? "OK" : status === false ? "KO" : "N/A";
      return {
        key: `${idx}-${name}`,
        index: idx + 1,
        name,
        label,
        type,
        status,
        statusLabel,
        elapsedTime,
        elapsedTimeCPU,
        input,
        // strings no longer needed; uuJsonView renders objects directly
        options,
        output: out,
        debug: dbg,
        hasError,
        headerClass,
        sectionName: `step-${idx}-${name}`,
      };
    });
    this.steps = steps;
    this.activeStepSections = steps.length ? [steps[0].sectionName] : [];
  }

  computeRawSections(log) {
    const known = new Set([
      "executionSequence",
      "elapsedTimeActual",
      "elapsedTimeCPU",
      "response",
      "originalInput",
      "fullDataJson",
      "debugLog",
    ]);
    (log.executionSequence || []).forEach((n) => {
      known.add(n);
      known.add(`${n}Debug`);
    });
    const sections = [];
    Object.keys(log || {}).forEach((k) => {
      if (!known.has(k)) {
        sections.push({
          name: k,
          value: log[k],
        });
      }
    });
    // Always include these if present, first
    const pinned = ["originalInput", "fullDataJson", "response", "debugLog"];
    const pinnedSections = pinned
      .filter((p) => Object.prototype.hasOwnProperty.call(log, p))
      .map((p) => ({ name: p, value: log[p] }));
    this.rawSections = [...pinnedSections, ...sections];
  }

  get hasParsed() {
    return !!this.parsedLog;
  }

  // Formatting helpers
  format(obj) {
    try {
      return JSON.stringify(obj, null, 2);
    } catch (e) {
      return String(obj);
    }
  }

  get summaryJsonButtonLabel() {
    return this.showSummaryJson ? "Nascondi JSON" : "Mostra JSON";
  }

  // UI helpers
  get hasSteps() {
    return this.steps && this.steps.length > 0;
  }

  copyText(event) {
    const target = event.currentTarget?.dataset?.target;
    let text = "";
    switch (target) {
      case "request":
        text = this.format(this.summary.request);
        break;
      case "response":
        text = this.format(this.summary.response);
        break;
      default:
        break;
    }
    if (!text) return;
    try {
      navigator.clipboard.writeText(text);
    } catch (e) {
      // ignore
    }
  }
}
