public class CustomerWrapper {
  @AuraEnabled
  public Account account;
  @AuraEnabled
  public Contact contact;
  @AuraEnabled
  public List<Opportunity> opportunity;
  @AuraEnabled
  public List<Quote> quote;
  @AuraEnabled
  public List<OpportunityCoverage__c> opportunityCoverages;
  @AuraEnabled
  public List<OpportunityData> opportunitiesData;
  @AuraEnabled
  public List<Asset> assets;
  @AuraEnabled
  public List<AssetData> assetsData;
  @AuraEnabled
  public List<AccountDetails__c> accountDetails;
  @AuraEnabled
  public List<AccountDetailsNPI__c> accountDetailsNPI;
  @AuraEnabled
  public List<FinServ__AccountAccountRelation__c> accountFinancials;
  @AuraEnabled
  public AccountFinancialData AccountFinancialData;

  public CustomerWrapper() {
    this.opportunity = new List<Opportunity>();
    this.quote = new List<Quote>();
    this.opportunityCoverages = new List<OpportunityCoverage__c>();
    this.opportunitiesData = new List<OpportunityData>();
    this.assets = new List<Asset>();
    this.assetsData = new List<AssetData>();
    this.accountDetails = new List<AccountDetails__c>();
    this.accountDetailsNPI = new List<AccountDetailsNPI__c>();
    this.accountFinancials = new List<FinServ__AccountAccountRelation__c>();
    this.AccountFinancialData = new AccountFinancialData();
  }

  // Structure aligned with docs/customer-structure.md
  public class OpportunityData {
    @AuraEnabled
    public Id opportunityId;
    @AuraEnabled
    public Opportunity opportunity;
    @AuraEnabled
    public List<Quote> quotes;
    @AuraEnabled
    public List<QuoteData> quotesData;

    public OpportunityData() {
      this.quotes = new List<Quote>();
      this.quotesData = new List<QuoteData>();
    }
  }

  public class QuoteData {
    @AuraEnabled
    public Id quoteId;
    @AuraEnabled
    public Quote quote;
    @AuraEnabled
    public List<OpportunityCoverage__c> opportunityCoverages;

    public QuoteData() {
      this.opportunityCoverages = new List<OpportunityCoverage__c>();
    }
  }

  // Aligned to docs/customer-structure.md (note: key name intentionally matches spec)
  public class AccountFinancialData {
    @AuraEnabled
    public Map<String, SocietyData> societies;
    @AuraEnabled
    public Map<String, AgencyData> agencies;

    public AccountFinancialData() {
      this.societies = new Map<String, SocietyData>();
      this.agencies = new Map<String, AgencyData>();
    }
  }

  public class SocietyData {
    @AuraEnabled
    public String societyCode;
    @AuraEnabled
    public Id accountFinancialId;
    @AuraEnabled
    public FinServ__AccountAccountRelation__c accountFinancial;
    @AuraEnabled
    public List<AccountDetails__c> accountDetails;
    @AuraEnabled
    public Map<String, AccountDetailData> accountDetailsData; // keyed by RecordType.DeveloperName
    @AuraEnabled
    public List<Asset> assets;
  }

  public class AgencyData {
    @AuraEnabled
    public String agencyCode;
    @AuraEnabled
    public Id accountFinancialId;
    @AuraEnabled
    public FinServ__AccountAccountRelation__c accountFinancial;
    @AuraEnabled
    public List<Asset> assets;
  }

  public class AccountDetailData {
    @AuraEnabled
    public Id accountDetailId;
    @AuraEnabled
    public AccountDetails__c accountDetail;
    @AuraEnabled
    public AccountDetailsNPI__c accountDetailNPI;
  }

  // Assets data structure per docs/customer-structure.md
  public class AssetData {
    @AuraEnabled
    public Id assetId;
    @AuraEnabled
    public Asset asset;
    // Computed codes from Asset.ExternalId__c
    @AuraEnabled
    public String societyCode;
    @AuraEnabled
    public String agencyCode;
    @AuraEnabled
    public String assetCode;
  }
}
