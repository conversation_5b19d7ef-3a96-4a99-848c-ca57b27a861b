import { api } from 'lwc';
import LightningModal from 'lightning/modal';

export default class DxPegaInspectorModal extends LightningModal {
    @api row;

    get selectedRow() { return this.row; }
    get selectedRowJson() { return this.row ? JSON.stringify(this.row, null, 2) : null; }
    get selectedRowPath() { return this.row ? this.row.path : ''; }
    get selectedRowHasCustom() { return this.row && this.row.customAttributes !== undefined; }
    get selectedRowCustom() { return this.row && this.row._customParsed ? JSON.stringify(this.row._customParsed, null, 2) : '{}'; }

    copyToClipboard(text) {
        try {
            if (navigator?.clipboard?.writeText) {
                navigator.clipboard.writeText(text);
            } else {
                const area = document.createElement('textarea');
                area.value = text;
                area.style.position = 'fixed';
                area.style.opacity = '0';
                document.body.appendChild(area);
                area.focus();
                area.select();
                document.execCommand('copy');
                document.body.removeChild(area);
            }
        } catch {}
    }

    copySelectedPath = () => { if (this.row?.path) this.copyToClipboard(String(this.row.path)); }
    copySelectedJson = () => { if (this.row) this.copyToClipboard(JSON.stringify(this.row, null, 2)); }

    handleClose = () => { this.close(); }
}
