<template>
    <div class="CaTelematica">
        <span class="CardTelematicaHeader">
            <template if:true={nomeGaranzia}>
                <c-dx-custom-text-styles content={nomeGaranzia} text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
                </c-dx-custom-text-styles>
            </template>
            <template if:true={checkbox}>
                <c-dx-field-checkbox field={checkbox}>
                </c-dx-field-checkbox>
            </template>
        </span>
        <div class="CardTelematicaFrazionamento">
            <c-dx-custom-text-styles content={premioRataScontato} text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
            </c-dx-custom-text-styles>
            <template if:true={canoneTelematica}>
                <c-dx-field data={canoneTelematica}></c-dx-field>
            </template>
            <template if:true={labelFrazionamentoCanone}>
                <c-dx-custom-text-styles content={labelFrazionamentoCanone}
                    text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
                </c-dx-custom-text-styles>
            </template>
            <!--template if:true={descrizioneTelematica}>
                <c-dx-custom-text-styles
                  content={descrizioneTelematica}
                  text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
                </c-dx-custom-text-styles>
            </template-->
            <template if:true={descrizioneTelematica}>
                <c-dx-field-paragraph field={descrizioneTelematica}
                    public-pega-style="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14"></c-dx-field-paragraph>
            </template>
        </div>
    </div>
</template>