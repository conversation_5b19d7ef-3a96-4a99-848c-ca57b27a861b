export function loadItems(key) {
  try {
    const stored = window.localStorage.getItem(key);
    return stored ? JSON.parse(stored) : [];
  } catch (e) {
    return [];
  }
}

export function saveItems(key, items) {
  window.localStorage.setItem(key, JSON.stringify(items));
}

export function upsertItem(key, item, uniqueField = "id") {
  const items = loadItems(key).filter((h) => h[uniqueField] !== item[uniqueField]);
  const updated = [item, ...items];
  saveItems(key, updated);
  return updated;
}

export default { loadItems, saveItems, upsertItem };
