<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseEditDescrizione</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
    NOT(ISNEW()),
    ISCHANGED(Description),
    OR(
        RecordType.DeveloperName == "ur_CasePQ",
        RecordType.DeveloperName == "ur_CaseSitoWeb",
        RecordType.DeveloperName == "ur_CaseAR",
        RecordType.DeveloperName == "ur_CaseES",
        CASESAFEID($User.Id) != UtAssegnatario__c,
        IsClosed = true
    )
)</errorConditionFormula>
    <errorDisplayField>Description</errorDisplayField>
    <errorMessage>Non è possibile modificare il valore questo campo.</errorMessage>
</ValidationRule>
