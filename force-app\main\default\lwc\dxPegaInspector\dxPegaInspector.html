<template>
    <div class="slds-p-around_medium slds-text-color_default">
        <header class="slds-page-header slds-m-bottom_medium">
            <div class="slds-page-header__row">
                <div class="slds-page-header__col-title">
                    <div class="slds-media">
                        <div class="slds-media__body">
                            <h1 class="slds-page-header__title slds-truncate" title={headerTitle}>{headerTitle}</h1>
                        </div>
                    </div>
                </div>
                <div class="slds-page-header__col-actions">
                    <div class="slds-button-group" role="group">
                        <button class="slds-button slds-button_neutral" onclick={exportFieldsCsv} disabled={noFields}
                            title="Esporta Fields in CSV">Export Fields CSV</button>
                        <button class="slds-button slds-button_neutral" onclick={exportViewsCsv} disabled={noViews}
                            title="Esporta Views in CSV">Export Views CSV</button>
                        <button class="slds-button slds-button_neutral" onclick={exportActionsCsv} disabled={noActions}
                            title="Esporta Actions in CSV">Export Actions CSV</button>
                        <button class="slds-button slds-button_neutral" onclick={downloadJson} disabled={isRawEmpty}
                            title="Scarica JSON normalizzato">Download JSON</button>
                    </div>
                </div>
            </div>
        </header>

        <section class="slds-grid slds-gutters">
            <!-- Sidebar navigation -->
            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-4 slds-large-size_1-of-5">
                <article class="slds-card sticky">
                    <div class="slds-card__header slds-grid slds-p-around_small">
                        <h2 class="slds-card__header-title">Navigazione</h2>
                    </div>
                    <div class="slds-card__body slds-card__body_inner">
                        <nav class="slds-vertical-navigation" aria-label="Navigation">
                            <div class="slds-vertical-navigation__section">
                                <h2 class="slds-vertical-navigation__title slds-text-title_caps">Sezioni</h2>
                                <ul>
                                    <li class={navItemClassSummary}>
                                        <a class="slds-vertical-navigation__action" data-key="summary" onclick={onTabClick}>Summary</a>
                                    </li>
                                    <li class={navItemClassFields}>
                                        <a class="slds-vertical-navigation__action" data-key="fields" onclick={onTabClick}>Fields <span class="slds-badge slds-m-left_x-small">{stats.fields}</span></a>
                                    </li>
                                    <li class={navItemClassViews}>
                                        <a class="slds-vertical-navigation__action" data-key="views" onclick={onTabClick}>Views <span class="slds-badge slds-m-left_x-small">{stats.views}</span></a>
                                    </li>
                                    <li class={navItemClassActions}>
                                        <a class="slds-vertical-navigation__action" data-key="actions" onclick={onTabClick}>Actions <span class="slds-badge slds-m-left_x-small">{stats.actions}</span></a>
                                    </li>
                                    <li class={navItemClassParagraphs}>
                                        <a class="slds-vertical-navigation__action" data-key="paragraphs" onclick={onTabClick}>Paragraphs <span class="slds-badge slds-m-left_x-small">{stats.paragraphs}</span></a>
                                    </li>
                                    <li class={navItemClassTree}>
                                        <a class="slds-vertical-navigation__action" data-key="tree" onclick={onTabClick}>Tree</a>
                                    </li>
                                    <li class={navItemClassDiagnostics}>
                                        <a class="slds-vertical-navigation__action" data-key="diagnostics" onclick={onTabClick}>Diagnostics</a>
                                    </li>
                                    <li class={navItemClassStats}>
                                        <a class="slds-vertical-navigation__action" data-key="stats" onclick={onTabClick}>Stats</a>
                                    </li>
                                    <li class={navItemClassDiff}>
                                        <a class="slds-vertical-navigation__action" data-key="diff" onclick={onTabClick} aria-disabled={diffDisabled}>Diff</a>
                                    </li>
                                </ul>
                            </div>
                        </nav>

                        <div class="slds-m-top_medium">
                            <div class="slds-form-element slds-m-bottom_small">
                                <label class="slds-form-element__label">Filtro avanzato</label>
                                <div class="slds-form-element__control">
                                    <input class="slds-input" placeholder="controlType:pxHidden attr.validation:email /MostraContatti/i required:true" value={filter} oninput={onFilterChange} />
                                </div>
                                <div class="slds-m-top_x-small slds-text-body_small">
                                    <label class="slds-checkbox">
                                        <input type="checkbox" checked={filteredOnly} onchange={onToggleFilteredOnly} />
                                        <span class="slds-checkbox_faux"></span>
                                        <span class="slds-form-element__label">Esporta solo filtrati</span>
                                    </label>
                                </div>
                                <div class="slds-m-top_x-small slds-text-body_small">
                                    <label class="slds-checkbox">
                                        <input type="checkbox" checked={wrapLongText} onchange={onToggleWrap} />
                                        <span class="slds-checkbox_faux"></span>
                                        <span class="slds-form-element__label">Wrap colonne lunghe</span>
                                    </label>
                                </div>
                            </div>

                            <div class="slds-form-element slds-m-bottom_small">
                                <label class="slds-form-element__label">Jump to path</label>
                                <div class="slds-grid slds-gutters">
                                    <div class="slds-col slds-size_2-of-3">
                                        <div class="slds-form-element__control">
                                            <input class="slds-input" placeholder="$.view.groups[0].layout...." value={jumpPath} oninput={onJumpPathChange} />
                                        </div>
                                    </div>
                                    <div class="slds-col slds-size_1-of-3">
                                        <button class="slds-button slds-button_brand" onclick={doJump}>Vai</button>
                                    </div>
                                </div>
                            </div>

                            <div class="slds-form-element">
                                <label class="slds-form-element__label">Diff mode</label>
                                <div class="slds-m-bottom_x-small">
                                    <label class="slds-checkbox">
                                        <input type="checkbox" checked={diffMode} onchange={onToggleDiffMode} />
                                        <span class="slds-checkbox_faux"></span>
                                        <span class="slds-form-element__label slds-text-body_small">Confronta con secondo JSON</span>
                                    </label>
                                </div>
                                <template if:true={diffMode}>
                                    <div class="slds-form-element__control">
                                        <textarea class="slds-textarea small-area" placeholder="Incolla qui il secondo JSON per il diff" value={raw2} oninput={onRaw2Change}></textarea>
                                    </div>
                                    <template if:true={parseError2}>
                                        <div class="slds-text-color_error slds-text-body_small slds-m-top_x-small">Errore parsing secondo JSON: {parseError2}</div>
                                    </template>
                                </template>
                            </div>
                        </div>
                    </div>
                </article>
            </div>

            <!-- Main content -->
            <div class="slds-col slds-size_1-of-1 slds-medium-size_3-of-4 slds-large-size_4-of-5">
                <article class="slds-card slds-m-bottom_medium">
                    <div class="slds-card__header slds-grid">
                        <h2 class="slds-card__header-title">Sorgente JSON</h2>
                    </div>
                    <div class="slds-card__body slds-card__body_inner">
                        <div class="slds-form-element slds-m-bottom_small">
                            <label class="slds-form-element__label">Incolla JSON</label>
                            <div class="slds-form-element__control">
                                <textarea class="slds-textarea" placeholder="Incolla qui il payload JSON" value={raw} oninput={onRawChange}></textarea>
                            </div>
                        </div>
                        <div class="slds-m-bottom_none">
                            <button class="slds-button slds-button_neutral slds-m-right_x-small" onclick={prettyPrint} disabled={isRawEmpty}>Pretty Print</button>
                            <label class="slds-button slds-button_neutral slds-m-right_x-small" title="Carica file JSON">
                                Upload JSON
                                <input style="display:none" type="file" accept=".json,application/json" onchange={onUploadFile} />
                            </label>
                            <button class="slds-button slds-button_neutral" onclick={fetchUrl}>Fetch URL</button>
                        </div>
                        <template if:true={parseError}>
                            <div class="slds-text-color_error slds-text-body_small slds-m-top_x-small">Errore parsing: {parseError}</div>
                        </template>
                    </div>
                </article>

                <!-- Dynamic views -->

        <template if:true={isSummary}>
            <article class="slds-card slds-m-bottom_small">
                <div class="slds-card__body slds-card__body_inner">
                    <div class="slds-text-body_regular slds-m-bottom_small">
                        Indicizzazione di Fields, Views, Actions e Paragraphs. Filtro avanzato: testo libero,
                        chiave:valore, regex /.../i, lookup attributi con attr.&lt;path&gt;:.
                    </div>
                    <div class="slds-grid slds-wrap slds-gutters">
                        <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                            <h3 class="slds-text-title_caps slds-m-bottom_x-small">Esempi di query</h3>
                            <ul class="slds-list_dotted slds-m-left_medium">
                                <li>Campi nascosti: <code>controlType:pxHidden</code></li>
                                <li>Submit button: <code>attr.submitButton:true</code> o
                                    <code>action:finishAssignment</code></li>
                                <li>Stepper: <code>attr.customType:Stepper</code></li>
                                <li>Validazioni: <code>attr.validation:email</code></li>
                                <li>Path specifico: <code>/MostraContatti/</code></li>
                            </ul>
                        </div>
                        <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                            <h3 class="slds-text-title_caps slds-m-bottom_x-small">Metadati principali (se presenti)
                            </h3>
                            <pre class="slds-box slds-theme_shade mono">{metaSummary}</pre>
                        </div>
                    </div>
                </div>
            </article>
        </template>

        <template if:true={isActions}>
            <div class="slds-m-bottom_x-small slds-grid slds-wrap slds-gutters_small">
                <div class="slds-col slds-size_1-of-2 slds-text-color_weak slds-text-body_small">
                    <template if:false={hasSelection}>Seleziona una riga per ispezionare dettagli.</template>
                    <template if:true={hasSelection}>Riga selezionata: {selectedRowPath}</template>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-text-align_right">
                    <lightning-button label="Ispeziona" onclick={inspectSelected}
                        disabled={inspectDisabled}></lightning-button>
                </div>
            </div>
            <div class="table-scroll slds-scrollable_x slds-scrollable_y">
                <lightning-datatable key-field="path" data={visibleActions} columns={actionsColumns}
                    sorted-by={actionsSort.key} sorted-direction={actionsSort.dir} onsort={onSortActionsDt}
                    onrowselection={onSelectActions} onrowaction={onRowAction} max-row-selection="1"
                    hide-checkbox-column="false" show-row-number-column>
                </lightning-datatable>
            </div>
        </template>

        <template if:true={isFields}>
            <section class="slds-grid slds-wrap slds-gutters">
                <div class="slds-col slds-size_1-of-1">
                    <div class="slds-m-bottom_x-small slds-grid slds-wrap slds-gutters_small">
                        <div class="slds-col slds-size_1-of-2 slds-text-color_weak slds-text-body_small">
                            <template if:false={hasSelection}>Seleziona una riga per ispezionare dettagli.</template>
                            <template if:true={hasSelection}>Riga selezionata: {selectedRowPath}</template>
                        </div>
                        <div class="slds-col slds-size_1-of-2 slds-text-align_right">
                            <lightning-button label="Ispeziona" onclick={inspectSelected}
                                disabled={inspectDisabled}></lightning-button>
                        </div>
                    </div>
                    <div class="table-scroll slds-scrollable_x slds-scrollable_y">
                        <lightning-datatable key-field="path" data={visibleFields} columns={fieldsColumns}
                            sorted-by={fieldsSort.key} sorted-direction={fieldsSort.dir} onsort={onSortFieldsDt}
                            onrowselection={onSelectFields} onrowaction={onRowAction} max-row-selection="1"
                            hide-checkbox-column="false" show-row-number-column>
                        </lightning-datatable>
                        <template if:true={isFieldsEmpty}>
                            <div class="slds-align_absolute-center slds-text-color_weak slds-p-vertical_small">Nessun
                                risultato</div>
                        </template>
                    </div>
                </div>
            </section>
        </template>

        <template if:true={isViews}>
            <section class="slds-grid slds-wrap slds-gutters">
                <div class="slds-col slds-size_1-of-1">
                    <div class="slds-m-bottom_x-small slds-grid slds-wrap slds-gutters_small">
                        <div class="slds-col slds-size_1-of-2 slds-text-color_weak slds-text-body_small">
                            <template if:false={hasSelection}>Seleziona una riga per ispezionare dettagli.</template>
                            <template if:true={hasSelection}>Riga selezionata: {selectedRowPath}</template>
                        </div>
                        <div class="slds-col slds-size_1-of-2 slds-text-align_right">
                            <lightning-button label="Ispeziona" onclick={inspectSelected}
                                disabled={inspectDisabled}></lightning-button>
                        </div>
                    </div>
                    <div class="table-scroll slds-scrollable_x slds-scrollable_y">
                        <lightning-datatable key-field="path" data={visibleViews} columns={viewsColumns}
                            sorted-by={viewsSort.key} sorted-direction={viewsSort.dir} onsort={onSortViewsDt}
                            onrowselection={onSelectViews} onrowaction={onRowAction} max-row-selection="1"
                            hide-checkbox-column="false" show-row-number-column>
                        </lightning-datatable>
                        <template if:true={isViewsEmpty}>
                            <div class="slds-align_absolute-center slds-text-color_weak slds-p-vertical_small">Nessun
                                risultato</div>
                        </template>
                    </div>
                </div>
            </section>
        </template>

        <template if:true={isParagraphs}>
            <div class="slds-m-bottom_x-small slds-grid slds-wrap slds-gutters_small">
                <div class="slds-col slds-size_1-of-2 slds-text-color_weak slds-text-body_small">
                    <template if:false={hasSelection}>Seleziona una riga per ispezionare dettagli.</template>
                    <template if:true={hasSelection}>Riga selezionata: {selectedRowPath}</template>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-text-align_right">
                    <lightning-button label="Ispeziona" onclick={inspectSelected}
                        disabled={inspectDisabled}></lightning-button>
                </div>
            </div>
            <div class="table-scroll slds-scrollable_x slds-scrollable_y">
                <lightning-datatable key-field="path" data={visibleParagraphs} columns={paragraphsColumns}
                    onrowselection={onSelectParagraphs} onrowaction={onRowAction} max-row-selection="1"
                    hide-checkbox-column="false" show-row-number-column>
                </lightning-datatable>
                <template if:true={isParagraphsEmpty}>
                    <div class="slds-align_absolute-center slds-text-color_weak slds-p-vertical_small">Nessun risultato
                    </div>
                </template>
            </div>
        </template>

        <!-- Inspector modal moved to LightningModal (dxPegaInspectorModal) -->

        <template if:true={isTree}>
            <div class="slds-box slds-theme_shade slds-scrollable_y slds-p-around_small">
                <template for:each={nodes} for:item="n">
                    <div key={n.id} class="slds-text-body_small mono"><span
                            class="slds-text-color_weak">{n.type}:</span> {n.path}</div>
                </template>
            </div>
        </template>

        <template if:true={isDiagnostics}>
            <template if:true={hasIssues}>
                <div class="slds-grid slds-wrap slds-gutters_small">
                    <template for:each={issues} for:item="i" for:index="idx">
                        <div key={i.path} class="slds-col slds-size_1-of-1">
                            <div class={i.cls}>
                                <div class="slds-text-color_weak slds-text-body_small">{i.kind}</div>
                                <div>{i.msg}</div>
                                <div class="slds-text-color_weak slds-text-body_small">{i.path}</div>
                            </div>
                        </div>
                    </template>
                </div>
            </template>
            <template if:false={hasIssues}>
                <div class="slds-text-color_weak">Nessuna anomalia rilevata.</div>
            </template>
        </template>

        <template if:true={isStats}>
            <div class="slds-grid slds-wrap slds-gutters">
                <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                    <article class="slds-card slds-m-bottom_small">
                        <div class="slds-card__header slds-grid">
                            <h2 class="slds-card__header-title">By controlType</h2>
                        </div>
                        <div class="slds-card__body slds-card__body_inner">
                            <ul class="slds-list_dotted slds-m-left_medium">
                                <template for:each={byControlList} for:item="e">
                                    <li key={e.key}><span class="mono">{e.key}</span>: {e.value}</li>
                                </template>
                            </ul>
                        </div>
                    </article>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                    <article class="slds-card slds-m-bottom_small">
                        <div class="slds-card__header slds-grid">
                            <h2 class="slds-card__header-title">By field.type</h2>
                        </div>
                        <div class="slds-card__body slds-card__body_inner">
                            <ul class="slds-list_dotted slds-m-left_medium">
                                <template for:each={byTypeList} for:item="e">
                                    <li key={e.key}><span class="mono">{e.key}</span>: {e.value}</li>
                                </template>
                            </ul>
                        </div>
                    </article>
                </div>
                <div class="slds-col slds-size_1-of-1">
                    <article class="slds-card slds-m-bottom_small">
                        <div class="slds-card__header slds-grid">
                            <h2 class="slds-card__header-title">Flags</h2>
                        </div>
                        <div class="slds-card__body slds-card__body_inner">
                            <ul class="slds-list_dotted slds-m-left_medium">
                                <li><span class="mono">required</span>: {stats.required}</li>
                                <li><span class="mono">visible</span>: {stats.visible}</li>
                                <li><span class="mono">readOnly</span>: {stats.readOnly}</li>
                            </ul>
                        </div>
                    </article>
                </div>
            </div>
        </template>

        <template if:true={isDiff}>
            <article class="slds-card">
                <div class="slds-card__header slds-grid">
                    <h2 class="slds-card__header-title">Fields</h2>
                </div>
                <div class="slds-card__body slds-card__body_inner">
                    <div class="slds-grid slds-gutters">
                        <div>
                            <div class="slds-text-title_caps slds-m-bottom_xx-small">Added ({diff.fields.added.length})
                            </div>
                            <div class="slds-box slds-theme_shade slds-scrollable_y">
                                <template for:each={diff.fields.added} for:item="it" for:index="i">
                                    <div key={it.path} class="slds-text-color_success mono">{it.path}</div>
                                </template>
                                <template if:true={isEmptyDiffFieldsAdded}>
                                    <div class="slds-text-color_weak">—</div>
                                </template>
                            </div>
                        </div>
                        <div>
                            <div class="slds-text-title_caps slds-m-bottom_xx-small">Removed
                                ({diff.fields.removed.length})</div>
                            <div class="slds-box slds-theme_shade slds-scrollable_y">
                                <template for:each={diff.fields.removed} for:item="it" for:index="i">
                                    <div key={it.path} class="slds-text-color_error mono">{it.path}</div>
                                </template>
                                <template if:true={isEmptyDiffFieldsRemoved}>
                                    <div class="slds-text-color_weak">—</div>
                                </template>
                            </div>
                        </div>
                        <div>
                            <div class="slds-text-title_caps slds-m-bottom_xx-small">Changed
                                ({diff.fields.changed.length})</div>
                            <div class="slds-box slds-theme_shade slds-scrollable_y">
                                <template for:each={diff.fields.changed} for:item="it" for:index="i">
                                    <div key={it.key}>
                                        <div class="mono slds-text-color_weak">{it.key}</div>
                                    </div>
                                </template>
                                <template if:true={isEmptyDiffFieldsChanged}>
                                    <div class="slds-text-color_weak">—</div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </template>
            </div>
        </section>
    </div>
</template>