import { LightningElement, api, wire } from 'lwc';
import { getRecord } from 'lightning/uiRecordApi';
import ACCOUNT_FIELD from '@salesforce/schema/Case.AccountId';
import PHONE_FIELD from '@salesforce/schema/Account.Phone';

export default class CC_avvioManualeChiamataDial extends LightningElement {
    @api recordId;   

    phoneNumber;
    accountId;

    connectedCallback() {
        console.log('CC_avvioManualeChiamataDial : connectedCallback : ', this.recordId);
    }

    @wire(getRecord, { recordId: '$recordId', fields: [ACCOUNT_FIELD] })
    caseRecord({ data }) {
        if (data) {
            this.accountId = data.fields.AccountId.value;
        }
    }

    @wire(getRecord, { recordId: '$accountId', fields: [PHONE_FIELD] })
    accountRecord({ data }) {
        if (data) {
            this.phoneNumber = data.fields.Phone.value;
        }
    }
}