<template>
  <!-- Debug Information -->
  <template if:true={debug}>
  </template>

  <!-- Analytics Hidden Component -->
  <template if:true={isAnalyticsHidden}>
  </template>

  <!-- Header Component -->
  <template if:true={isHeader}>
    <c-dx-header field={field} groups={groups}></c-dx-header>
  </template>

  <template if:true={isStepper}>
    <c-dx-stepper field={field} groups={groups} decoded-value={decodedValue} disabled={disabled}></c-dx-stepper>
  </template>

  <template if:true={isMultiStepper}>
    <c-dx-multi-stepper field={field} groups={groups}></c-dx-multi-stepper>
  </template>

  <template if:true={isCircularStepper}>
    <c-dx-circular-stepper field={field} groups={groups}></c-dx-circular-stepper>
  </template>

  <template if:true={isToastCard}>
    <c-dx-toast-card field={field}></c-dx-toast-card>
  </template>

  <template if:true={isCardGaranzieHeader}>
    <c-dx-card-garanzie-header field={field} groups={groups}></c-dx-card-garanzie-header>
  </template>

  <template if:true={isAssurancePackage}>
    <c-dx-assurance-package field={field} groups={groups}></c-dx-assurance-package>
  </template>

  <template if:true={isCardProtezioneDettaglioGaranzia}>
    <c-dx-card-protezione-dettaglio-garanzia field={field} groups={groups}></c-dx-card-protezione-dettaglio-garanzia>
  </template>

  <template if:true={isCardSezione}>
    <c-dx-card-sezione field={field} groups={groups}></c-dx-card-sezione>
  </template>

  <template if:true={isStickyFooter}>
    <c-dx-sticky-footer field={field} groups={groups}></c-dx-sticky-footer>
  </template>

  <template if:true={isCarrello}>
    <c-dx-carrello field={field} groups={groups}></c-dx-carrello>
  </template>

  <template if:true={isUnicoProtezione}>
    <c-dx-unico-protezione field={field} groups={groups}></c-dx-unico-protezione>
  </template>

  <template if:true={isCarouselCard}>
    <c-dx-carousel-card field={field} groups={groups}></c-dx-carousel-card>
  </template>

  <template if:true={isBoxPagamento}>
    <c-dx-box-pagamento field={field} groups={groups}></c-dx-box-pagamento>
  </template>

  <template if:true={isCardTelematica}>
    <c-dx-card-telematica field={field} groups={groups}></c-dx-card-telematica>
  </template>

  <template if:true={isCarouselWeb}>
    <c-dx-carousel-web view={view} groups={groups} field={field}></c-dx-carousel-web>
  </template>

  <template if:true={isCardProtezione}>
    <c-dx-card-protezione field={field} groups={groups}></c-dx-card-protezione>
  </template>

  <template if:true={isAgencyLocator}>
    <c-dx-agency-locator field={field} groups={groups}></c-dx-agency-locator>
  </template>

  <template if:true={isBoxIndirizzo}>
    <c-dx-box-indirizzo field={field} groups={groups}></c-dx-box-indirizzo>
  </template>

  <template if:true={isAddressAutocomplete}>
    <c-dx-address-autocomplete field={field} groups={groups}></c-dx-address-autocomplete>
  </template>

  <template if:true={isSeparator}>
    <c-dx-separator field={field} groups={groups}></c-dx-separator>
  </template>

  <template if:true={isRadioCardFrazionamento}>
    <c-dx-radio-card-frazionamento field={field} groups={groups}></c-dx-radio-card-frazionamento>
  </template>

  <!-- Fallback for Unsupported Types -->
  <template if:false={isHeader}>
  </template>
</template>