import { utils } from "c/dxUtils";
import { utilsPegaText } from 'c/dxUtilsPegaText';
import { LightningElement, api } from 'lwc';

export default class CustomTextStyle extends LightningElement {
    @api content = '';
    @api textCss;

    get labelCssFormat() {
        if (this.textCss) {
          const inlineStyle = utilsPegaText.getTextCss(this.textCss);          
    
          if (!inlineStyle) return {};
          return {
            desktopCss: utils.getStyleStringFromObj(inlineStyle.desktopCss),
            tabletCss: utils.getStyleStringFromObj(inlineStyle.tabletCss),
            mobileCss: utils.getStyleStringFromObj(inlineStyle.mobileCss)
          };
        }
    }
}