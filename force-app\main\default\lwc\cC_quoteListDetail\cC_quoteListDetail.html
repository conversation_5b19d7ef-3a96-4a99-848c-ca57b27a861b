<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 08-21-2025
  @last modified by  : <EMAIL>
-->
<template>
    <div class="slds-box slds-card slds-p-around_medium">
        <div class="slds-page-header">
            <div class="slds-page-header__row">
                <div class="slds-page-header__col-7">
                    <div class="slds-media slds-no-space slds-grow">
                        <div class="slds-media__figure">
                            <span class="slds-icon_container slds-icon-standard-opportunity" title="Preventivi">
                                <lightning-icon icon-name="custom:custom13" alternative-text="Preventivi" title="Preventivi"></lightning-icon>
                                <span class="slds-assistive-text">Preventivi</span>
                            </span>
                        </div>
                        <div class="slds-media__body slds-truncate">
                            <h1 class="slds-page-header__title slds-truncate slds-align-middle" title="Registro Preventivi">Registro Preventivi</h1>
                            <p class="slds-text-body_small slds-line-height_reset">
                                <span class="slds-text-body_regular">Preventivi ({data.length})</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slds-card__body slds-card__body_inner slds-m-top_small">
            <template if:true={data}>
                <lightning-datatable
                    key-field="Id"
                    data={data}
                    columns={columns}
                    hide-checkbox-column="true"
                    onrowaction={handleRowAction}>
                </lightning-datatable>
            </template>
            
            <template if:true={expandedDetailData.length}>
                <div class="detail-box slds-box slds-m-top_x-small slds-p-around_medium">
                    <lightning-datatable
                        key-field="Id"
                        data={expandedDetailData}
                        columns={dettaglioColumns}
                        hide-checkbox-column="true">
                    </lightning-datatable>
                </div>
            </template>

            <template if:true={error}>
                <p class="slds-text-color_error">Errore nel recupero dati: {error}</p>
            </template>
        </div>
    </div>

    <c-pdf-view-modal url={documentUrl} show-modal={showPreview} onclose={closePDFPreview}></c-pdf-view-modal>
</template>