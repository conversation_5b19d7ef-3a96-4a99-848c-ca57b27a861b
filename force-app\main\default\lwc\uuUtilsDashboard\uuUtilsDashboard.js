import { LightningElement, api, track } from "lwc";
import DxPegaInspectorHostModal from "c/dxPegaInspectorHostModal";
import IpInvocationHistoryModal from "c/ipInvocationHistoryModal";
import IpLogDebuggerModal from "c/ipLogDebuggerModal";
import UuQuickSearchModal from "c/uuQuickSearchModal";

import { fireEvent } from "c/pubsub";
import { defaultViews } from "c/uuUtils";
export default class UuUtilsDashboard extends LightningElement {
  @api DEM;

  @track logs = [];
  @track config = [...defaultViews];

  selectedView;
  isOpen = false;

  get processViews() {
    return this.config.map((view) => {
      const viewItem = {};

      if (view.type === "pubSub") {
        viewItem.variant = "destructive-text";
      }

      if (this.selectedView === view.name) {
        viewItem.variant = "brand";
      }

      return {
        ...view,
        ...viewItem,
      };
    });
  }

  get show() {
    return this.config.reduce((acc, view) => {
      acc[view.name] = view.name === this.selectedView;
      return acc;
    }, {});
  }

  get debugEnabled() {
    try {
      return (
        typeof window !== "undefined" &&
        window.localStorage &&
        window.localStorage.getItem("debug") === "true"
      );
    } catch (e) {
      return false;
    }
  }

  connectedCallback() {
    LightningElement.prototype.$ = this.DEM;

    this.$?.subscribe(this.$?.EVENTS.LGHT_DEBUG, this.handleDebugEvent);
    this.$?.sync({ target: this.$?.TARGETS.LGHT_DEBUG });

    const debugEnabled =
      typeof window !== "undefined" &&
      window.localStorage &&
      window.localStorage.getItem("debug") === "true";

    if (!debugEnabled) {
      window.localStorage.setItem("debug", "false");
    }

    // Global shortcut: Cmd+K / Ctrl+K to open quick search
    this._onGlobalKeydown = (e) => {
      try {
        const tag = (e.target && e.target.tagName) || "";
        const isEditable =
          (e.target &&
            (e.target.isContentEditable ||
              (e.target.getAttribute &&
                e.target.getAttribute("contenteditable") === "true"))) ||
          tag === "INPUT" ||
          tag === "TEXTAREA" ||
          tag === "SELECT";
        if (isEditable) return;
        const key = (e.key || "").toLowerCase();
        if ((e.metaKey || e.ctrlKey) && key === "k") {
          e.preventDefault();
          e.stopPropagation();
          this.openQuickSearch();
        }
      } catch (err) {
        // no-op
      }
    };
    window.addEventListener("keydown", this._onGlobalKeydown);
  }

  disconnectedCallback() {
    if (this._onGlobalKeydown) {
      window.removeEventListener("keydown", this._onGlobalKeydown);
    }

    this.$?.unsubscribe(this.$?.EVENTS.LGHT_DEBUG, this.handleDebugEvent);
  }

  handleDebugEvent = (event) => {
    Object.entries(event.detail).forEach(([key, value]) => {
      this.$?.log(key, value);
      this[key] = this.$?.destructure(value);
    });
  };

  handleSelectView(event) {
    const { name } = event.target;
    this._executeItemByName(name);
  }

  handleToggle() {
    this.isOpen = !this.isOpen;
  }

  handleClose() {
    this.selectedView = null;
  }

  handlePubSub(event) {
    const { name } = event.target;
    fireEvent(name);
  }

  async openQuickSearch() {
    try {
      const items = (this.config || []).map((v) => ({
        name: v.name,
        label: v.label || v.name,
        description:
          v.type === "pubSub" && v.pubSub && v.pubSub.action
            ? `pubsub: ${v.pubSub.action}`
            : "",
      }));
      const result = await UuQuickSearchModal.open({
        size: "small",
        label: "Quick Search",
        items,
      });
      if (result && result.name) {
        this._executeItemByName(result.name);
      }
    } catch (e) {
      // ignore
    }
  }

  _executeItemByName(name) {
    const view = (this.config || []).find((v) => v.name === name);
    if (!view) return;

    if (view.type === "pubSub") {
      if (view.pubSub && view.pubSub.action) {
        console.log(`Executing pubsub action: ${view.pubSub.action}`);
        fireEvent(view.pubSub.action);
      }
      return;
    }

    // Modal-based tools
    if (name === "dxPegaInspector") {
      DxPegaInspectorHostModal.open({
        size: "large",
        label: "Pega JSON Inspector",
      });
      return;
    }
    if (name === "logsIp") {
      IpInvocationHistoryModal.open({
        size: "large",
        label: "Log Invocazioni",
      });
      return;
    }
    if (name === "ipLogDebugger") {
      IpLogDebuggerModal.open({ size: "large", label: "IP Log Debugger" });
      return;
    }

    // Default behavior: toggle view in dashboard
    this.selectedView = name === this.selectedView ? null : name;
  }
}
