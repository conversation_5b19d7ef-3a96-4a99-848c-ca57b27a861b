import { LightningElement, api } from 'lwc';

export default class BoxPagamento extends LightningElement {
    @api field;
    @api groups;

    // Recupera la vista principale
    get mainView() {
        return this.groups?.find(group => group.view?.reference === 'MostraAmbitoPagamento') || null;
    }

    // Mappa il background in base al productType
    get mappaBackground() {
        const productType = this.field?.customAttributes?.productType;
        if (productType) {
            const capitalize = productType.charAt(0).toUpperCase() + productType.slice(1).toLowerCase().replace('à', 'a');
            return `bg-${capitalize}`;
        }
        return '';
    }

    // Mappa il padding in base al valore fornito
    get mappaPadding() {
        const padding = this.field?.customAttributes?.padding;
        if (padding) {
            return padding
                .split(' ')
                .map(paddingNumber => `${paddingNumber}px`)
                .reverse()
                .join(' ');
        }
        return '0';
    }
}