<template>
  <article class="slds-card">
    <div class="slds-card__header slds-grid">
      <header class="slds-media slds-media_center slds-has-flexi-truncate">
        <div class="slds-media__figure">
          <lightning-icon icon-name="utility:list" size="x-small" alternative-text="Storico"></lightning-icon>
        </div>
        <div class="slds-media__body">
          <h2 class="slds-card__header-title">
            <span class="slds-truncate" title="Storico Invocazioni">Storico Invocazioni</span>
          </h2>
        </div>
      </header>
      <div class="slds-no-flex">
        <lightning-button
          variant="destructive"
          label="Elimina Salvati"
          onclick={clearInvocationHistory}
        ></lightning-button>
      </div>
    </div>

    <div class="slds-card__body slds-card__body_inner">
      <template if:true={invocationHistory.length}>
        <lightning-datatable
          key-field="timestamp"
          data={invocationHistory}
          columns={columns}
          hide-checkbox-column
          max-row-selection="0"
          onrowaction={handleRowAction}
          style="min-height:320px;height:100%;display:block;"
        ></lightning-datatable>
      </template>
      <template if:false={invocationHistory.length}>
        <div class="slds-p-vertical_large slds-align_absolute-center slds-text-align_center">
          <lightning-icon icon-name="utility:open_folder" size="small" alternative-text="Vuoto" class="slds-m-bottom_small"></lightning-icon>
          <h3 class="slds-text-heading_small">Nessuna invocazione disponibile</h3>
          <p class="slds-m-top_x-small slds-text-color_weak">Le richieste invocate verranno mostrate qui.</p>
        </div>
      </template>
    </div>
  </article>

  <!-- Inline modals removed; handled via LightningModal components -->
</template>
