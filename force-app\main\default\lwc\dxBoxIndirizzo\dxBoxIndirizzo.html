<template >
  <div class="card-box box-indirizzo-width">
      <div class="layout-box-indirizzo">
          <div class="layout-text-address">
              <template if:true={customAttributes.cardTitle}>
                  <div class="Text-responsive-medium">
                      {customAttributes.cardTitle}
                  </div>
              </template>
              <template if:true={address}>
                  <div class="address-layout">
                      <div class="Text-responsive-medium">Indirizzo:</div>
                      <div class="Text-responsive">{address}</div>
                  </div>
              </template>
          </div>
          <div class="iconBox">
            <template if:true={icon}>
              <c-dx-field-px-icon field={icon}></c-dx-field-px-icon>
            </template>
        </div>
      </div>
  </div>
</template>