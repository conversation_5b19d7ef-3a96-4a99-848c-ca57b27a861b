import { LightningElement, api } from 'lwc';
import { utils } from 'c/dxUtils';

export default class CarouselCard extends LightningElement {
    _field;
    _groups;

    cardBackgroundClass;
    cardTitle;
    bodyTitle;
    bodyContent;
    iconContent;
    iconStyle;
    button;

    getBeneIcon(beneCode) {
        const beneIcons = {
            'PUCASA': '/assets/icons/bene-icons/PUCASA.svg',
            'PUAUTO': '/assets/icons/bene-icons/PUAUTO.svg',
            'PUINF': '/assets/icons/bene-icons/PUINF.svg',
            'PUSAL': '/assets/icons/bene-icons/PUSAL.svg',
            'PUVIAGGIA': '/assets/icons/bene-icons/PUVIAGGIA.svg',
            'PUFAM': '/assets/icons/bene-icons/PUFAM.svg',
            'PUPET': '/assets/icons/bene-icons/PUPET.svg',
            'PUMOBILITA': '/assets/icons/bene-icons/PUMOBILITA.svg',
        };

        return beneIcons[beneCode] || '/assets/icons/default-icon.svg'; // Icona di default
    }

    @api
    set field(value) {
        this._field = value;
        this._tryInitialize();
    }
    get field() {
        return this._field;
    }

    @api
    set groups(value) {
        this._groups = value;
        this._tryInitialize();
    }
    get groups() {
        return this._groups;
    }

    // get iconStyle() {
    //     return `background-image: url('https://www.unipol.it/NextAssets/icons/icon-${this.iconContent}.svg')`;
    // }    

    get containerClass() {
        return `CarouselCardContainer ${this.cardBackgroundClass || ''}`;
    }

    _tryInitialize() {
        if (!this._field || !this._groups) return;

        const field = this._field;
        const cardShortDescription = field?.customAttributes?.cardShortDescription || ' | | ';
        const parts = cardShortDescription.split('|').map(p => utils.decodeHTML(p.trim()));
        if (parts.length === 3) {
            [this.cardTitle, this.bodyTitle, this.bodyContent] = parts;
        }

        const cardName = field?.customAttributes?.cardName || '';
        this.cardBackgroundClass = `bg-${cardName}`;

        const beneCodeMap = {
            Casa: 'PUCASA',
            Veicoli: 'PUAUTO',
            Infortuni: 'PUINF',
            Salute: 'PUSAL',
            Viaggi: 'PUVIAGGIA',
            Viaggio: 'PUVIAGGIA',
            Famiglia: 'PUFAM',
            Pet: 'PUPET',
            Mobilità: 'PUMOBILITA',
            Mobilita: 'PUMOBILITA'
        };

        this.iconContent = this.getBeneIcon(beneCodeMap[cardName] || '');
        this.iconStyle =  `background-image: url('https://www.unipol.it/NextAssets/icons/icon-${cardName}.svg')`;

        const pxButton = utils.getFirstFieldInGroupsByType(this._groups, 'pxButton', true);
        if (pxButton?.customAttributes?.linkType === 'GO_TO_NEW_QUOTATION') {
            delete pxButton.customAttributes.linkType;
            pxButton.customAttributes.GO_TO_QUOTATION = cardName;
        }
        this.button = pxButton;
    }
}