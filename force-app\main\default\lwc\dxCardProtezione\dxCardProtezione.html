<template>
    <div class="CardProtezioneContainer">
        <span class="CardProtezioneHeader">
            <template if:true={nomeGaranzia}>
                <span class="CardProtezioneTitolo">{nomeGaranzia}</span>
            </template>
            <!--TOOLTIP-->
            <template if:true={icon}>
                <c-dx-field-px-icon field={icon}></c-dx-field-px-icon>
            </template>
            <!--CHECKBOX-->
            <template if:true={checkbox}>
                <c-dx-field-checkbox field={checkbox} style="margin-left: auto">
                </c-dx-field-checkbox>
            </template>
        </span>
        <div if:true={descrizioneGaranzia} class="CardProtezioneBody">
            {descrizioneGaranzia}
        </div>
        <div class="CardProtezioneFooter">
            <div class="CardProtezioneFooterValori">
                <template for:each={attributiGaranzia} for:item="attributo">
                    <span key={attributo.label} class="ValoreElement">{attributo.labelValueString}</span>
                </template>
            </div>
            <div class="CardProtezionePremioLordo">
                <span class="PremioLordo">{premioLordo}</span>
                <span class="Occorrenza">{occorrenzaPrezzoGaranzia}</span>
            </div>
        </div>
    </div>
</template>