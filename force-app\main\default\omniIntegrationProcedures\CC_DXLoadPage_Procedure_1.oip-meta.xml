<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;request&quot;: {
        &quot;action&quot;: &quot;CREATE&quot;,
        &quot;productType&quot;: &quot;PUVIAGGI&quot;,
        &quot;captchaToken&quot;: &quot;03AFcWeA5zkYoXW8cV1Y66Sa-u49eXHkLSDGJXFUh6ChQSEBuedHfzTUmnRbYMsFj4uYH_p2Cfw7mmtjVbQQoUrHY06U7rjyCJxWZRz7YWFEtvQNP-XUl5EZ97OTo6d1cqz1JnhC0SbuAkQt-yVYFvlLgcFJVv1eelu6zc8CNem7qE1Gl8iT0iSBIB1S-9yv7i6O6Ijbf5kF3xaEiyfjrUarlOZevKcmDISor8YY0mBcpJCKltrfwm3DgO5M1HS-Fslxr8nhCGcZQMXOjf09RSNxtqB4s_LZw44ETrAXZtcCKtCIrjlNMdALd83haFxwlu_dAw7MCxApQOkdZ6Y3ByU73VoSLfW-ZxPda3T61KJMRxrNIhk1Sk16rEeVFMph9ZFSKn-8U0Bv5AgKWYiMjG4Tok8LVPqtYfzGIxP3FBAcMiuXBUQT0mejMBYMmUNAUup7qDFp3LEOxAFXayjJcU4tF-0BjC3egNCqCeZRelAfdt1sgTcuoXtCmNLcwmgUzu759WPcbu1bU2hNrWFMARx30FFsbQsIYIjzoUMVMNAy-Mn1yGbecPHaTGpl2oYEHAU0nxdDzKWUAYF7tilU8ieaUu-cIZoHY99fK4Q7KimdBlU8kTF8lOVGPsfmVMQ1-9-_A09KvUHMTkzbhOqxMFi8VfIDSJqwfLSo7GF64Uyqd-0U1Znc5CS75E3r6yFT5j7pzAsCfBHJZZ2g48poL372knQLnLsHS6UCIzR3C3Do_9SS08bw-YzUrM-QEvEfcj8MLIS5tDzp5XIiBB4P9Q-KMe8PVgZbi0eGJfB9BYtaxNOY5_Csp-u1wEnbxPYr_oo_Q_glJksoLzHXBjDVmXdlQbK_mGwGMmFOEHFTi_HXRTiwZkIc3lEfI1lPLzRF6E5IFL7lY1fXm1okiekQ0v4dJ7sZqB2YojvXD7ZHteRcBmy6m1CebrxECDGJRKagLOxFg69caYKsAL52QspyKyb6OOYBOMJ75Tl1QucOyd3DjcYw2s8o6Sp1Wtapfgl8d9oyk7j8-JDMte-wvUrluGhnGNB9jcur_uPMt-UWWHK7jZdUkXTwIMj6OoSpg85q1NTxcqreYxG_RLnzYm-dqVCNxq9h7GDmZ7m3Qy8gqbmsuUaSTJfwTGK5klLcQOZdcl-JD9G59X-6HttqYfk9aWPvUtqDtCitEWnE31uIRROLd4TPJQzdVtqC5HTsusFHfR5nZ2BBbdXnrhAFb7O5DgLtHK0O8wKnBLoz_1i-mXiYIFr-3KAdgRWn_kalZ6Jsu_lBGUbDMPpwCwz2kyoekEqFrMJIGbwfohiQiDPFuwIhSozaSrs_gSm8_x586tALHdM6NRm0qwlHjuxIRIU6bpU6vq4pL298UUYujhHxn36p4-b3krKoBgv_gne24B5VFAZQ1UQSkKdqmtJF4JN0s0WXRjRAdev0Mm0OKot2gR9hvfLBPVE6MFwaUJVwMlAJGbz-tnQWrsTCVgqAb0rBUKCEEQfwPJ9FCQx4rG3OTAjEtPSclr3kqC8p1puL5KxxqlgVClo-hdcVeK_wAcl9zKz4DJZKUDFNcycbS9bDftN7txSc-52RJR2M87vxdX8gfxdXNdEFzSqgDnwbJo-XGuyk5unS4PK2ep1t935jx_6f4HTp-66IJu5j5xMeNo-aHgSmArbzVm71LOqYF3na57CVg90UrPLKxKrQ&quot;,
        &quot;referencesToUpdate&quot;: {
            &quot;Ambito.Bene.Viaggio.PaeseDestinazione&quot;: &quot;1101&quot;,
            &quot;Ambito.Bene.Viaggio.NumeroViaggiatori&quot;: &quot;3&quot;,
            &quot;Ambito.Bene.Viaggio.DataViaggioAndata&quot;: &quot;14/07/2025&quot;,
            &quot;Ambito.Bene.Viaggio.DataViaggioRitorno&quot;: &quot;15/07/2025&quot;,
            &quot;Contraente.Contatti.Email&quot;: &quot;&quot;
        },
        &quot;env&quot;: &quot;UNICO&quot;
    }
}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>CCDXLoadPage</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IntegrationService</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;CC_DX_LOAD_PAGE&quot;,
    &quot;body&quot; : &quot;=SERIALIZE(request)&quot;,
    &quot;params&quot; : &quot;=&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;response&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;SetResponse&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;response&quot; : &quot;=DESERIALIZE(IntegrationService:result)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>CC_DXLoadPage</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>DXLoadPage</subType>
    <type>CC</type>
    <uniqueName>CC_DXLoadPage_Procedure_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <webComponentKey>d9b77636-f064-8c00-8630-1f5c17523c76</webComponentKey>
</OmniIntegrationProcedure>
