import { api } from 'lwc';
import LightningModal from 'lightning/modal';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class IpEditSavedItemModal extends LightningModal {
	@api requestText = '';
	@api responseText = '';

	// uuJsonView bindings
	get requestObject() {
		try {
			return this.requestText ? JSON.parse(this.requestText) : {};
		} catch (e) {
			return {};
		}
	}
	get responseObject() {
		try {
			return this.responseText ? JSON.parse(this.responseText) : undefined;
		} catch (e) {
			return undefined;
		}
	}

	handleRequestJsonChange = (e) => {
		try {
			this.requestText = JSON.stringify(e?.detail?.value ?? {}, null, 2);
		} catch (err) {
			// ignore
		}
	};

	handleResponseJsonChange = (e) => {
		try {
			const val = e?.detail?.value;
			this.responseText = val === undefined ? '' : JSON.stringify(val, null, 2);
		} catch (err) {
			// ignore
		}
	};

	handleCancel = () => this.close();

	handleSave = () => {
		try {
			const normalizedRequest = JSON.stringify(JSON.parse(this.requestText));
			let normalizedResponse = '';
			if (this.responseText && this.responseText.trim()) {
				normalizedResponse = JSON.stringify(JSON.parse(this.responseText));
			}
			this.close({ saved: true, request: normalizedRequest, response: normalizedResponse });
		} catch (e) {
			this.dispatchEvent(new ShowToastEvent({ title: 'Errore', message: e.message || 'JSON non valido', variant: 'error' }));
		}
	};
}
