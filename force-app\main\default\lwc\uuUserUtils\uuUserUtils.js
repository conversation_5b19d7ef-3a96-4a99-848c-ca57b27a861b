import { LightningElement } from "lwc";
import getUserContextByInput from "@salesforce/apex/UserUtils.getUserContextByInput";

export default class UuUserUtils extends LightningElement {
  userId = "";
  fiscalCode = "";
  userCode = "";
  searchInput = "";

  result;
  error;
  userInfo;
  userData;
  societyPreferedData;
  preferredNetworkData;
  agencyData;
  userNetworks;
  ucaUsers;
  permissionSets;
  companyOptions = [];
  selectedCompany;
  selectedNetworkData;
  selectedNetworkPreferred;
  selectedNetworkAgency;
  showSocietyPrefered = false;
  showUser = false;
  showUserNetworks = false;
  showUcaUsers = false;
  showPermissionSets = false;
  showHistoryModal = false;
  history = [];
  historyStorageKey = "uuUserUtilsHistory";
  showResultJson = false;
  showErrorJson = false;

  connectedCallback() {
    this.loadHistory();
    this.handleSubmit();
  }

  handleSearchChange(event) {
    this.searchInput = event.target.value;
  }

  identifyKey(value) {
    const result = {};
    if (/^[A-Za-z0-9]{18}$/.test(value)) {
      result.userId = value;
    } else if (/^[A-Z]{6}\d{2}[A-Z]\d{2}[A-Z]\d{3}[A-Z]$/.test(value)) {
      result.fiscalCode = value;
    } else if (/^[A-Z0-9]+$/.test(value)) {
      result.userCode = value;
    }

    return result;
  }

  async handleSubmit() {
    this.result = undefined;
    this.error = undefined;
    this.userInfo = undefined;
    this.userData = undefined;
    this.societyPreferedData = undefined;
    this.agencyData = undefined;
    this.userNetworks = undefined;
    this.ucaUsers = undefined;
    this.permissionSets = undefined;
    this.companyOptions = [];
    this.selectedCompany = undefined;
    this.selectedNetworkData = undefined;
    this.selectedNetworkPreferred = undefined;
    this.selectedNetworkAgency = undefined;

    const parsed = this.identifyKey(this.searchInput);
    this.userId = parsed.userId || null;
    this.userCode = parsed.userCode || null;
    this.fiscalCode = parsed.fiscalCode || null;

    console.log(
      "UuUserUtils:: handleSubmit",
      this.searchInput,
      this.userId,
      this.fiscalCode,
      this.userCode,
    );

    try {
      const input = {
        userId: this.userId,
        fiscalCode: this.fiscalCode,
        userCode: this.userCode,
        findPermissionSets: true,
        findAgencyUserNetworks: true,
        useCurrentUser: !this.userId && !this.fiscalCode && !this.userCode,
      };

      console.log("UuUserUtils:: handleSubmit:: input", JSON.stringify(input));
      const response = await getUserContextByInput({
        input: JSON.stringify(input),
      });

      this.result = response;
      this.userData = response?.user;
      this.societyPreferedData = response?.societyPrefered;
      this.preferredNetworkData = this.societyPreferedData?.preferredNetwork;
      this.agencyData = this.societyPreferedData?.agency?.agency;
      this.userNetworks = response?.userNetworks;
      this.ucaUsers = response?.ucaUsers?.users;
      this.permissionSets = response?.permissionSets;

      if (this.userNetworks) {
        this.companyOptions = Object.keys(this.userNetworks).map((key) => ({
          label: key,
          value: key,
        }));
      }

      const info = Array.isArray(response) ? response[0] : response;
      if (info) {
        this.userInfo = {
          name: this.userData?.Name,
          userCode: this.societyPreferedData?.userId,
          fiscalCode: this.societyPreferedData?.preferredNetwork?.FiscalCode__c,
          userId: info.userId,
          societyPreferedCode: info.societyPreferedCode,
          profileName: info.profileName,
          roleName: this.userData?.UserRole?.Name,
        };
        if (this.userId || this.fiscalCode || this.userCode) {
          this.addToHistory();
        }
      }
    } catch (err) {
      this.error = err;
    }
  }

  toggleSection(event) {
    const section = event.target.dataset.section;
    const sections = [
      "showSocietyPrefered",
      "showUser",
      "showUserNetworks",
      "showUcaUsers",
      "showPermissionSets",
    ];
    sections.forEach((sec) => {
      this[sec] = sec === section ? !this[sec] : false;
    });
  }

  handleCompanyChange(event) {
    this.selectedCompany = event.detail.value;
    const selected = this.userNetworks?.[this.selectedCompany];
    if (selected) {
      this.selectedNetworkData = selected;
      this.selectedNetworkPreferred = selected.preferredNetwork;
      this.selectedNetworkAgency = selected.agency?.agency;
    } else {
      this.selectedNetworkData = undefined;
      this.selectedNetworkPreferred = undefined;
      this.selectedNetworkAgency = undefined;
    }
  }

  get resultJson() {
    return this.result ? JSON.stringify(this.result, null, 2) : undefined;
  }

  get errorJson() {
    return this.error ? JSON.stringify(this.error, null, 2) : undefined;
  }

  get resultJsonButtonLabel() {
    return this.showResultJson ? "Nascondi JSON" : "Mostra JSON";
  }

  get errorJsonButtonLabel() {
    return this.showErrorJson ? "Nascondi JSON" : "Mostra JSON";
  }

  toggleResultJson() {
    this.showResultJson = !this.showResultJson;
  }

  toggleErrorJson() {
    this.showErrorJson = !this.showErrorJson;
  }

  copyResultJson() {
    if (this.resultJson && typeof navigator !== "undefined") {
      navigator.clipboard.writeText(this.resultJson);
    }
  }

  copyErrorJson() {
    if (this.errorJson && typeof navigator !== "undefined") {
      navigator.clipboard.writeText(this.errorJson);
    }
  }

  loadHistory() {
    try {
      const stored = window.localStorage.getItem(this.historyStorageKey);
      const parsed = stored ? JSON.parse(stored) : [];
      this.history = parsed
        .map((item) => ({
          ...item,
          label:
            item.label ||
            [item.name, item.fiscalCode, item.userCode]
              .filter((part) => part)
              .join(" - "),
          timestamp: item.timestamp || 0,
        }))
        .sort((a, b) => b.timestamp - a.timestamp);
    } catch (e) {
      this.history = [];
    }
  }

  saveHistory() {
    window.localStorage.setItem(
      this.historyStorageKey,
      JSON.stringify(this.history),
    );
  }

  addToHistory() {
    const item = {
      name: this.userData?.Name,
      userId: this.userInfo?.userId,
      userCode: this.societyPreferedData?.userId,
      fiscalCode: this.userData?.FederationIdentifier,
      label: [
        this.userData?.Name,
        this.userData?.FederationIdentifier,
        this.societyPreferedData?.userId,
      ]
        .filter((part) => part)
        .join(" - "),
    };
    if (!item.userId) {
      return;
    }
    // Remove existing entry and place the latest at the top
    this.history = this.history.filter((h) => h.userId !== item.userId);
    item.timestamp = Date.now();
    this.history = [item, ...this.history];
    this.saveHistory();
  }

  openHistory() {
    this.loadHistory();
    this.showHistoryModal = true;
  }

  closeHistory() {
    this.showHistoryModal = false;
  }

  handleHistorySelect(event) {
    const id = event.currentTarget.dataset.id;
    const index = this.history.findIndex((h) => h.userId === id);
    if (index > -1) {
      const [item] = this.history.splice(index, 1);
      item.timestamp = Date.now();
      this.history = [item, ...this.history];
      this.saveHistory();
      this.searchInput = item.userId;
      this.showHistoryModal = false;
      this.handleSubmit();
    }
  }
}
