<template>
  <div class={componentClass}>
    <template if:true={isVisible}>
      <p if:true={debug} class="temporaryLabel">VIEW: {view.name}</p>
      <template if:true={hasGroups}>
        <template for:each={view.groups} for:index="index" for:item="group">
          <c-dx-groups key={key} groups={group} debug={debug}></c-dx-groups>
        </template>
      </template>
    </template>
  </div>
</template>