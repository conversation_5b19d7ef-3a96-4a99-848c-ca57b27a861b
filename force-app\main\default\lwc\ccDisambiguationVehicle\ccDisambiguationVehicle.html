<template>
    <lightning-card>
        <div class="slds-p-around_medium">
            <lightning-combobox
                name={mergeData.tipology.name}
                label={mergeData.tipology.label}
                value={mergeData.tipology.value}
                options={mergeData.tipology.options}
                data-path={mergeData.tipology.path}
                onchange={handleChange}>
            </lightning-combobox>
            <lightning-input
                type="text"
                name={mergeData.plate.name}
                label={mergeData.plate.label}
                value={mergeData.plate.value}
                disabled={mergeData.notPlate.value}
                data-path={mergeData.plate.path}
                onchange={mergeData.plate.validation}>
            </lightning-input>
            <lightning-input
                type="checkbox"
                name={mergeData.notPlate.name}
                label={mergeData.notPlate.label}
                checked={mergeData.notPlate.value}
                data-path={mergeData.notPlate.path}
                onchange={handleNotPlateChange}>
            </lightning-input>
            <c-cc-disambiguation-birthdate
                form-data={mergeData}
                onfieldchange={handleChildChange}>
            </c-cc-disambiguation-birthdate>
            <c-cc-disambiguation-address
                form-data={mergeData}
                onfieldchange={handleChildChange}>
            </c-cc-disambiguation-address>
            <c-cc-disambiguation-effective-date
                form-data={mergeData}
                onfieldchange={handleChildChange}>
            </c-cc-disambiguation-effective-date>
            <lightning-input
                type="checkbox"
                name={mergeData.transferClassMerit.name}
                label={mergeData.transferClassMerit.label}
                checked={mergeData.transferClassMerit.value}
                data-path={mergeData.transferClassMerit.path}
                onchange={handleChange}>
            </lightning-input>
        </div>
    </lightning-card>
</template>