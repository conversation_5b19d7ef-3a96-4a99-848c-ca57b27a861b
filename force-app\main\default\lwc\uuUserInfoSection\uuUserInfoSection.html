<template>
  <div class="slds-m-around_medium section">
    <h3 class="slds-text-heading_small slds-m-bottom_small">User Info</h3>
    <lightning-layout multiple-rows>
      <lightning-layout-item size="12" medium-device-size="3">
        <lightning-input
          label="Name"
          value={userInfo.name}
          readonly
        ></lightning-input>
      </lightning-layout-item>
      <lightning-layout-item size="12" medium-device-size="3">
        <div
          class="slds-grid slds-grid_vertical-align-center slds-p-right_medium"
        >
          <div class="slds-col slds-grow">
            <lightning-input
              label="User Id"
              value={userInfo.userId}
              readonly
            ></lightning-input>
          </div>
          <div class="slds-col slds-no-flex button-clipboard-container">
            <lightning-button-icon
              icon-name="utility:copy_to_clipboard"
              alternative-text="Copy User Id"
              title="Copy User Id"
              onclick={handleCopy}
              data-value={userInfo.userId}
            ></lightning-button-icon>
          </div>
          <div class="slds-col slds-no-flex button-clipboard-container">
            <lightning-button-icon
              icon-name="utility:new_window"
              alternative-text="Open User"
              title="Open User"
              onclick={handleOpenUser}
              data-value={userInfo.userId}
            ></lightning-button-icon>
          </div>
        </div>
      </lightning-layout-item>
      <lightning-layout-item size="12" medium-device-size="3">
        <div
          class="slds-grid slds-grid_vertical-align-center slds-p-right_medium"
        >
          <div class="slds-col slds-grow">
            <lightning-input
              label="User Code"
              value={userInfo.userCode}
              readonly
            ></lightning-input>
          </div>
          <div class="slds-col slds-no-flex button-clipboard-container">
            <lightning-button-icon
              icon-name="utility:copy_to_clipboard"
              alternative-text="Copy User Code"
              title="Copy User Code"
              onclick={handleCopy}
              data-value={userInfo.userCode}
            ></lightning-button-icon>
          </div>
        </div>
      </lightning-layout-item>
      <lightning-layout-item size="12" medium-device-size="3">
        <div
          class="slds-grid slds-grid_vertical-align-center slds-p-right_medium"
        >
          <div class="slds-col slds-grow">
            <lightning-input
              label="Fiscal Code"
              value={userInfo.fiscalCode}
              readonly
            ></lightning-input>
          </div>
          <div class="slds-col slds-no-flex button-clipboard-container">
            <lightning-button-icon
              icon-name="utility:copy_to_clipboard"
              alternative-text="Copy Fiscal Code"
              title="Copy Fiscal Code"
              onclick={handleCopy}
              data-value={userInfo.fiscalCode}
            ></lightning-button-icon>
          </div>
        </div>
      </lightning-layout-item>
      <lightning-layout-item size="12" medium-device-size="3">
        <lightning-input
          label="Society Prefered Code"
          value={userInfo.societyPreferedCode}
          readonly
        ></lightning-input>
      </lightning-layout-item>
      <lightning-layout-item size="12" medium-device-size="3">
        <lightning-input
          label="Profile Name"
          value={userInfo.profileName}
          readonly
        ></lightning-input>
      </lightning-layout-item>
      <lightning-layout-item size="12" medium-device-size="3">
        <lightning-input
          label="Role Name"
          value={userInfo.roleName}
          readonly
        ></lightning-input>
      </lightning-layout-item>
    </lightning-layout>
  </div>
</template>
