<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Categoria__c</fullName>
    <label>Categoria</label>
    <required>false</required>
    <trackFeedHistory>false</trackFeedHistory>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <controllingField>Origin</controllingField>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Art. 94</fullName>
                <default>false</default>
                <label>Art. 94</label>
            </value>
            <value>
                <fullName>Assicurazione</fullName>
                <default>false</default>
                <label>Assicurazione</label>
            </value>
            <value>
                <fullName>Auto in preassegnazione</fullName>
                <default>false</default>
                <label>Auto in preassegnazione</label>
            </value>
            <value>
                <fullName>Auto sostitutiva</fullName>
                <default>false</default>
                <label>Auto sostitutiva</label>
            </value>
            <value>
                <fullName>Circolazione all&apos;estero</fullName>
                <default>false</default>
                <label>Circolazione all&apos;estero</label>
            </value>
            <value>
                <fullName>Contratto</fullName>
                <default>false</default>
                <label>Contratto</label>
            </value>
            <value>
                <fullName>Fatturazione</fullName>
                <default>false</default>
                <label>Fatturazione</label>
            </value>
            <value>
                <fullName>Gestione interventi</fullName>
                <default>false</default>
                <label>Gestione interventi</label>
            </value>
            <value>
                <fullName>Gestione sinistri</fullName>
                <default>false</default>
                <label>Gestione sinistri</label>
            </value>
            <value>
                <fullName>Logistica veicolo</fullName>
                <default>false</default>
                <label>Logistica veicolo</label>
            </value>
            <value>
                <fullName>Richiesta info per acquisto del mezzo</fullName>
                <default>false</default>
                <label>Richiesta info per acquisto del mezzo</label>
            </value>
            <value>
                <fullName>Servizi amministrativi</fullName>
                <default>false</default>
                <label>Servizi amministrativi</label>
            </value>
            <value>
                <fullName>Soccorso e recupero</fullName>
                <default>false</default>
                <label>Soccorso e recupero</label>
            </value>
            <value>
                <fullName>Supporto Area Riservata</fullName>
                <default>false</default>
                <label>Supporto Area Riservata</label>
            </value>
            <value>
                <fullName>Variazioni contrattuali</fullName>
                <default>false</default>
                <label>Variazioni contrattuali</label>
            </value>
        </valueSetDefinition>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <valueName>Art. 94</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Assicurazione</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Auto in preassegnazione</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Auto sostitutiva</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Circolazione all&apos;estero</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <valueName>Contratto</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <valueName>Fatturazione</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Gestione interventi</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Gestione sinistri</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Logistica veicolo</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <valueName>Richiesta info per acquisto del mezzo</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Servizi amministrativi</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Soccorso e recupero</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>Web</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <controllingFieldValue>Portale Quotazioni</controllingFieldValue>
            <valueName>Supporto Area Riservata</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Phone</controllingFieldValue>
            <controllingFieldValue>Email</controllingFieldValue>
            <controllingFieldValue>PEC</controllingFieldValue>
            <controllingFieldValue>Social</controllingFieldValue>
            <controllingFieldValue>Area Riservata</controllingFieldValue>
            <valueName>Variazioni contrattuali</valueName>
        </valueSettings>
    </valueSet>
</CustomField>
