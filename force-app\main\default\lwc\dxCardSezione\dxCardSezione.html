<template>
      <div class={containerClass}>
      
  <c-dx-custom-text-styles
          class="deselectedText"
          text-css={deselectedTextStyle}
          content={deselectedText}>
  </c-dx-custom-text-styles>
        
        <template if:true={renderGroups}>
          <template for:each={renderGroups} for:index="index" for:item="group">
            <div key={key}>
              <c-dx-groups groups={group} key={key}></c-dx-groups>
            </div>
          </template>
        </template>

    </div>
  </template>