import CcDisambiguationCore from "c/ccDisambiguationCore";

export default class CcDisambiguationHouse extends CcDisambiguationCore {
  get formData() {
    return {
      tipology: {
        name: "tipology",
        value: "",
        label: "Tipologia di Abitazione",
        options: this.entityDomains?.TIPOABITAZ || [],
        path: "Ambito.Bene.Casa.TipologiaAbitazione",
      },
      effectiveDate: CcDisambiguationCore.FIELD_EFFECTIVE_DATE,
      address: {
        ...CcDisambiguationCore.FIELD_ADDRESS,
        label: "Indirizzo di residenza del capofamiglia",
      },
    };
  }
}
