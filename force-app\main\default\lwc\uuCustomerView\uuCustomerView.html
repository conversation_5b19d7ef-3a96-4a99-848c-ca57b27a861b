<template>
    <lightning-card title="uuCustomerView" icon-name="standard:account">
        <div class="s-section s-controls">
            <lightning-input
                type="text"
                label="Account Id"
                name="accountId"
                value={accountId}
                placeholder="001XXXXXXXXXXXXXXX"
                message-when-bad-input="Inserire un Id Salesforce valido (15/18 char)"
                onchange={handleInputChange}
            ></lightning-input>
            <lightning-button
                label="Carica contesto"
                variant="brand"
                class="s-cta"
                onclick={handleLoad}
                disabled={isLoadDisabled}
            ></lightning-button>
        </div>

        <template if:true={loading}>
            <div class="s-spinner">
                <lightning-spinner alternative-text="Caricamento contesto" size="medium"></lightning-spinner>
            </div>
        </template>

        <template if:true={context}>
            <div class="s-section s-card s-assets-data">
                <h3 class="s-title">Assets Data</h3>
                <template if:true={hasAssetsData}>
                    <lightning-datatable key-field="assetId" hide-checkbox-column data={assetsDataRows} columns={assetsDataColumns} onrowaction={handleAssetsRowAction}></lightning-datatable>
                    <lightning-accordion allow-multiple-sections-open class="s-m-top">
                        <template for:each={assetsDataRows} for:item="row">
                            <lightning-accordion-section key={row.assetId} name={row.assetId} label={row.label}>
                                <div class="s-subcard">
                                    <div class="form-grid">
                                        <lightning-input label="AssetId" value={row.assetId} readonly></lightning-input>
                                        <lightning-input label="Society" value={row.societyCode} readonly></lightning-input>
                                        <lightning-input label="Agency" value={row.agencyCode} readonly></lightning-input>
                                        <lightning-input label="Code" value={row.assetCode} readonly></lightning-input>
                                        <lightning-input label="Key" value={row.key} readonly></lightning-input>
                                        <lightning-input label="Value" value={row.value} readonly></lightning-input>
                                    </div>
                                </div>
                                <template if:true={row.assetRaw}>
                                    <div class="s-subtitle s-m-top-xsmall">Asset (raw)</div>
                                    <c-uu-json-view value={row.assetRaw} height="12rem"></c-uu-json-view>
                                </template>
                            </lightning-accordion-section>
                        </template>
                    </lightning-accordion>
                </template>
                <template if:false={hasAssetsData}>
                    <div class="s-empty">Nessun asset disponibile.</div>
                </template>
            </div>
            <div class="s-section s-card s-summary">
                <h3 class="s-title">Riepilogo</h3>
                <div class="s-subcard">
                    <div class="form-grid">
                        <lightning-input label="Account" value={accountName} readonly></lightning-input>
                        <lightning-input label="Tipo" value={accountType} readonly></lightning-input>
                        <lightning-input type="email" label="Email" value={accountEmail} readonly></lightning-input>
                        <lightning-input type="tel" label="Telefono" value={accountPhone} readonly></lightning-input>
                    </div>
                </div>
            </div>

            <div class="s-section s-card s-metrics">
                <h3 class="s-title">Metriche</h3>
                <div class="s-grid">
                    <div class="s-metric">
                        <span class="s-metric-num">{opportunitiesCount}</span>
                        <span class="s-metric-label">Opportunità</span>
                    </div>
                    <div class="s-metric">
                        <span class="s-metric-num">{quotesCount}</span>
                        <span class="s-metric-label">Preventivi</span>
                    </div>
                    <div class="s-metric">
                        <span class="s-metric-num">{assetsCount}</span>
                        <span class="s-metric-label">Asset</span>
                    </div>
                    <div class="s-metric">
                        <span class="s-metric-num">{societiesCount}</span>
                        <span class="s-metric-label">Società</span>
                    </div>
                    <div class="s-metric">
                        <span class="s-metric-num">{agenciesCount}</span>
                        <span class="s-metric-label">Agenzie</span>
                    </div>
                </div>
            </div>

            <div class="s-section s-card s-details">
                <h3 class="s-title">Account Financial Data</h3>
                <template if:true={hasSocieties}>
                    <h4 class="s-subtitle">Società</h4>
                    <ul class="s-list">
                        <template for:each={societies} for:item="soc">
                            <li key={soc.code} class="s-list-item">
                                <div class="s-subcard">
                                    <div class="form-grid">
                                        <lightning-input label="Code" value={soc.code} readonly></lightning-input>
                                        <lightning-input label="Name" value={soc.name} readonly></lightning-input>
                                        <lightning-input label="Dettagli (count)" value={soc.detailCount} readonly></lightning-input>
                                        <lightning-input label="Dettagli (types)" value={soc.detailTypes} readonly></lightning-input>
                                        <lightning-input label="Assets" value={soc.assetsCount} readonly></lightning-input>
                                    </div>
                                </div>
                                <template if:true={soc.details.length}>
                                    <div class="s-m-top">
                                        <lightning-accordion allow-multiple-sections-open>
                                            <template for:each={soc.details} for:item="det">
                                                <lightning-accordion-section key={det.type} name={det.type} label={det.type}>
                                                    <div class="s-subcard">
                                                        <div class="form-grid">
                                                            <lightning-input label="AccountDetailId" value={det.accountDetailId} readonly></lightning-input>
                                                            <lightning-input type="checkbox" label="NPI presente" checked={det.hasNPI} disabled></lightning-input>
                                                        </div>
                                                    </div>
                                                    <div class="s-subtitle s-m-top-xsmall">Account Detail</div>
                                                    <c-uu-json-view value={det.accountDetail} height="12rem"></c-uu-json-view>
                                                    <template if:true={det.accountDetailNPI}>
                                                        <div class="s-subtitle s-m-top-xsmall">NPI</div>
                                                        <c-uu-json-view value={det.accountDetailNPI} height="12rem"></c-uu-json-view>
                                                    </template>
                                                </lightning-accordion-section>
                                            </template>
                                        </lightning-accordion>
                                    </div>
                                </template>
                            </li>
                        </template>
                    </ul>
                </template>
                <template if:true={hasAgencies}>
                    <h4 class="s-subtitle">Agenzie</h4>
                    <ul class="s-list">
                        <template for:each={agencies} for:item="ag">
                            <li key={ag.code} class="s-list-item">
                                <span class="s-key">{ag.code}</span>
                                <span class="s-val">{ag.name}</span>
                                <span class="s-val">Asset: {ag.assetsCount}</span>
                            </li>
                        </template>
                    </ul>
                </template>
                <template if:false={hasSocieties}>
                    <div class="s-empty">Nessuna società collegata.</div>
                </template>
                <template if:false={hasAgencies}>
                    <div class="s-empty">Nessuna agenzia collegata.</div>
                </template>
            </div>

            <div class="s-section s-card s-opps-data">
                <h3 class="s-title">Opportunities Data</h3>
                <template if:true={hasOpportunitiesData}>
                    <lightning-datatable key-field="id" hide-checkbox-column data={opportunitiesDataRows} columns={opportunitiesDataColumns} onrowaction={handleOppRowAction}></lightning-datatable>
                    <lightning-accordion allow-multiple-sections-open class="s-m-top">
                        <template for:each={opportunitiesDataRows} for:item="opp">
                            <lightning-accordion-section key={opp.id} name={opp.id} label={opp.name}>
                                <div class="s-subcard">
                                    <div class="form-grid">
                                        <lightning-input label="StageName" value={opp.stageName} readonly></lightning-input>
                                        <lightning-input label="EngagementPoint__c" value={opp.engagementPoint} readonly></lightning-input>
                                        <lightning-input label="RecordTypeName__c" value={opp.recordTypeName} readonly></lightning-input>
                                        <lightning-input label="Amount" value={opp.amountFormatted} readonly></lightning-input>
                                        <lightning-input label="Probability" value={opp.probabilityFormatted} readonly></lightning-input>
                                        <lightning-input label="DomainType__c" value={opp.domainType} readonly></lightning-input>
                                        <lightning-input label="AgencyFormula__c" value={opp.agencyFormula} readonly></lightning-input>
                                        <lightning-input label="ContactChannel__c" value={opp.contactChannel} readonly></lightning-input>
                                        <lightning-input label="Quotes" value={opp.quotesCount} readonly></lightning-input>
                                        <lightning-input label="Coverages (tot)" value={opp.coveragesCount} readonly></lightning-input>
                                    </div>
                                </div>
                                <template if:true={opp.quotes.length}>
                                    <h4 class="s-subtitle s-m-top">Quotes</h4>
                                    <lightning-datatable key-field="id" hide-checkbox-column data={opp.quotesTableRows} columns={quotesDataColumns} onrowaction={handleQuoteRowAction}></lightning-datatable>
                                    <lightning-accordion allow-multiple-sections-open class="s-m-top">
                                        <template for:each={opp.quotes} for:item="q">
                                            <lightning-accordion-section key={q.id} name={q.id} label={q.name}>
                                                <div class="s-subcard">
                                                    <div class="form-grid">
                                                        <lightning-input label="Numero" value={q.number} readonly></lightning-input>
                                                        <lightning-input label="Stato" value={q.status} readonly></lightning-input>
                                                        <lightning-input type="email" label="Email" value={q.email} readonly></lightning-input>
                                                        <lightning-input type="tel" label="Phone" value={q.phone} readonly></lightning-input>
                                                        <lightning-input label="AreasOfNeed__c" value={q.areasOfNeed} readonly></lightning-input>
                                                        <lightning-input label="TotalPrice" value={q.totalPrice} readonly></lightning-input>
                                                        <lightning-input label="PolicyChannel__c" value={q.policyChannel} readonly></lightning-input>
                                                        <lightning-input label="DomainType__c" value={q.domainType} readonly></lightning-input>
                                                        <lightning-input label="EngagementPoint__c" value={q.engagementPoint} readonly></lightning-input>
                                                        <lightning-input label="ExternalId__c" value={q.externalId} readonly></lightning-input>
                                                    </div>
                                                </div>
                                                <template if:true={q.coverages.length}>
                                                    <h4 class="s-subtitle s-m-top">Coperture</h4>
                                                    <lightning-datatable key-field="id" hide-checkbox-column data={q.coveragesTableRows} columns={coveragesColumns} onrowaction={handleCoverageRowAction}></lightning-datatable>
                                                </template>
                                            </lightning-accordion-section>
                                        </template>
                                    </lightning-accordion>
                                </template>
                            </lightning-accordion-section>
                        </template>
                    </lightning-accordion>
                </template>
                <template if:false={hasOpportunitiesData}>
                    <div class="s-empty">Nessuna opportunità disponibile.</div>
                </template>
            </div>

            <div class="s-section s-card s-raw">
                <h3 class="s-title">Payload completo (debug)</h3>
                <c-uu-json-view value={context} height="20rem"></c-uu-json-view>
            </div>
        </template>

        <template if:true={error}>
            <div class="s-error">{errorMessage}</div>
        </template>
    </lightning-card>
</template>
