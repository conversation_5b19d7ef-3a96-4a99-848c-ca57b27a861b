import { track } from "lwc";

import CcDisambiguationAddress from "c/ccDisambiguationAddress";

export default class CcDisambiguationAddressHouse extends CcDisambiguationAddress {
  get overrideData() {
    return {
      ...this.formData,
     address: {
        name: "address",
        value: "",
        label: "Indirizzo", 
        type: "text",
        path: "Ambito.Bene.Casa.Indirizzo",
      },
    };
  }
}
