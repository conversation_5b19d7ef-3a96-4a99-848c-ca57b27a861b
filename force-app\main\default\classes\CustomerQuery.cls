public without sharing class CustomerQuery {
  // Queries extracted from CustomerUtils for readability and reuse.

  public static Account queryAccountById(Id accountId) {
    if (accountId == null)
      return null;
    List<Account> accounts = [
      SELECT
        Id,
        IsDeleted,
        MasterRecordId,
        Name,
        LastName,
        FirstName,
        Salutation,
        MiddleName,
        Suffix,
        Type,
        RecordTypeId,
        ParentId,
        BillingStreet,
        BillingCity,
        BillingState,
        BillingPostalCode,
        BillingCountry,
        BillingLatitude,
        BillingLongitude,
        BillingGeocodeAccuracy,
        BillingAddress,
        ShippingStreet,
        ShippingCity,
        ShippingState,
        ShippingPostalCode,
        ShippingCountry,
        ShippingLatitude,
        ShippingLongitude,
        ShippingGeocodeAccuracy,
        ShippingAddress,
        Phone,
        Fax,
        AccountNumber,
        Website,
        PhotoUrl,
        Sic,
        Industry,
        AnnualRevenue,
        NumberOfEmployees,
        Ownership,
        TickerSymbol,
        Description,
        Rating,
        Site,
        OwnerId,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        LastViewedDate,
        LastReferencedDate,
        SourceSystemIdentifier,
        PersonContactId,
        IsPersonAccount,
        PersonMailingStreet,
        PersonMailingCity,
        PersonMailingState,
        PersonMailingPostalCode,
        PersonMailingCountry,
        PersonMailingLatitude,
        PersonMailingLongitude,
        PersonMailingGeocodeAccuracy,
        PersonMailingAddress,
        PersonOtherStreet,
        PersonOtherCity,
        PersonOtherState,
        PersonOtherPostalCode,
        PersonOtherCountry,
        PersonOtherLatitude,
        PersonOtherLongitude,
        PersonOtherGeocodeAccuracy,
        PersonOtherAddress,
        PersonMobilePhone,
        PersonHomePhone,
        PersonOtherPhone,
        PersonAssistantPhone,
        PersonEmail,
        PersonTitle,
        PersonDepartment,
        PersonAssistantName,
        PersonLeadSource,
        PersonBirthdate,
        PersonHasOptedOutOfEmail,
        PersonHasOptedOutOfFax,
        PersonDoNotCall,
        PersonLastCURequestDate,
        PersonLastCUUpdateDate,
        PersonEmailBouncedReason,
        PersonEmailBouncedDate,
        PersonIndividualId,
        Jigsaw,
        JigsawCompanyId,
        AccountSource,
        SicDesc,
        OperatingHoursId,
        IsPriorityRecord,
        Age__c,
        BillingAddressFormula__c,
        Capacitadispesa__c,
        ClienteVip__c,
        Cluster__c,
        Codice_Agenzia_Madre__c,
        Comportamento_Omnicanale__c,
        District__c,
        Etichetta__c,
        Forma_Giuridica__c,
        Paese_di_Nascita__c,
        PrimaryContactFormula__c,
        Privacy__c,
        Provincia_di_nascita__c,
        Punto_Vendita__c,
        Qualifica__c,
        RecordTypeFormula__c,
        Size__c,
        Stato_CF__c,
        SubType__c,
        VisibilitContattiClienteFormula__c,
        InsPoliciesNum__c,
        AgencyCodeGeneral__c,
        AgencyCodeUniSalute__c,
        AgencyLoad__c,
        Agency__c,
        Area__c,
        BestEmail__c,
        BestMobile__c,
        BlacklistPrevidenza__c,
        Blacklist__c,
        BusinessHours__c,
        BusinessName__c,
        CAPActivity__c,
        CF__c,
        CIU__c,
        Channel__c,
        Classificazione__c,
        Cliente_Dal__c,
        CodiceAnagrafica__c,
        CodiceAteco__c,
        Codice_ABICED__c,
        Codici_Attivit__c,
        Companies__c,
        ContId__c,
        DataCostituzione__c,
        Dati_Fatca__c,
        DescrizioneAteco__c,
        DiscretionalityEnterprise__c,
        DiscretionalityMotor__c,
        DiscretionalityProperty__c,
        DiscretionalityWelfare__c,
        DownloadAPP__c,
        Email__c,
        EndDate__c,
        ExternalId__c,
        ExtraCAP__c,
        FEA__c,
        FormaGiuridica__c,
        FullName__c,
        GeneralAgencyCode__c,
        GruppoInternazionale__c,
        Identifier__c,
        Indicazione_cliente_intermediato_da_Brok__c,
        IsFamilyMember__c,
        IsLocatorEnabled__c,
        IsRolledOut__c,
        IsSearchLocatorEnabled__c,
        IsServiceProximityLocatorEnabled__c,
        KPI__c,
        LastUpdateSystem__c,
        Mandato__c,
        MargineContribuzione__c,
        Margini__c,
        OmnichannelAgreement__c,
        OmnichannelResponsible__c,
        PEC__c,
        ParentIdentifier__c,
        PartyId__c,
        PrivateArea__c,
        Professione__c,
        ReferenteAziendale__c,
        Responsabile_Omnicanale__c,
        Ricavi__c,
        SalesUnit__c,
        SourceSocietyDescription__c,
        SourceSystemCreateDate__c,
        SourceSystemDescription__c,
        StartDate__c,
        SystemLastUpdateDt__c,
        Titolarit__c,
        UtileLastYear__c,
        Valus__c,
        VatCode__c,
        VatNumber__c,
        ZoneResponsible__c,
        Zone__c,
        Zones__c,
        AgencyCIU__c,
        AgencyCode__c,
        AgencyRecTypeId__c,
        CheckCAPAssignedContactActivities__c,
        Comune_di_nascita__c,
        Discretionality__c,
        BlackListBPER__c,
        BlackListUnica__c,
        Subject_Name_Case__c,
        ManagementGroup__c,
        ManagementTypeCode__c,
        SalesUnitType__c,
        Type__c,
        FlagAssicoop__c,
        ManagegmentCode__c,
        Dipartimento__c,
        IdBaseAnagDriv__c,
        IdClienteLeo__c,
        IdDriver__c,
        NumPat__c,
        ProvNascAlt__c,
        ProvNasc__c,
        TipoSoggetto__c,
        UtDisabAR__c,
        Vip__c,
        dtEmissPat__c,
        dtScadPat__c,
        tsCreaz__c,
        tsElim__c,
        tsUtCreazAR__c,
        tsUtUltAccAR__c,
        MKCloudSynch__pc,
        User__pc,
        RecordType.DeveloperName
      FROM Account
      WHERE Id = :accountId
      LIMIT 1
    ];
    accounts = (List<Account>) Security.stripInaccessible(
        AccessType.READABLE,
        accounts
      )
      .getRecords();
    return accounts.isEmpty() ? null : accounts[0];
  }

  public static Contact queryContactById(Id contactId) {
    if (contactId == null)
      return null;
    List<Contact> contacts = [
      SELECT
        Id,
        IsDeleted,
        MasterRecordId,
        AccountId,
        IsPersonAccount,
        LastName,
        FirstName,
        Salutation,
        MiddleName,
        Suffix,
        Name,
        RecordTypeId,
        OtherStreet,
        OtherCity,
        OtherState,
        OtherPostalCode,
        OtherCountry,
        OtherLatitude,
        OtherLongitude,
        OtherGeocodeAccuracy,
        OtherAddress,
        MailingStreet,
        MailingCity,
        MailingState,
        MailingPostalCode,
        MailingCountry,
        MailingLatitude,
        MailingLongitude,
        MailingGeocodeAccuracy,
        MailingAddress,
        Phone,
        Fax,
        MobilePhone,
        HomePhone,
        OtherPhone,
        AssistantPhone,
        ReportsToId,
        Email,
        Title,
        Department,
        AssistantName,
        LeadSource,
        Birthdate,
        Description,
        OwnerId,
        HasOptedOutOfEmail,
        HasOptedOutOfFax,
        DoNotCall,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        LastCURequestDate,
        LastCUUpdateDate,
        LastViewedDate,
        LastReferencedDate,
        EmailBouncedReason,
        EmailBouncedDate,
        IsEmailBounced,
        PhotoUrl,
        Jigsaw,
        JigsawContactId,
        IndividualId,
        IsPriorityRecord,
        ContactSource,
        TitleType,
        DepartmentGroup,
        BuyerAttributes,
        MKCloudSynch__c,
        User__c
      FROM Contact
      WHERE Id = :contactId
      LIMIT 1
    ];
    contacts = (List<Contact>) Security.stripInaccessible(
        AccessType.READABLE,
        contacts
      )
      .getRecords();
    return contacts.isEmpty() ? null : contacts[0];
  }

  public static List<Opportunity> queryOpportunitiesByAccountId(Id accountId) {
    if (accountId == null)
      return new List<Opportunity>();
    List<Opportunity> opportunities = [
      SELECT
        Id,
        IsDeleted,
        AccountId,
        RecordTypeId,
        IsPrivate,
        Name,
        Description,
        StageName,
        Amount,
        Probability,
        ExpectedRevenue,
        TotalOpportunityQuantity,
        CloseDate,
        Type,
        NextStep,
        LeadSource,
        IsClosed,
        IsWon,
        ForecastCategory,
        ForecastCategoryName,
        CampaignId,
        HasOpportunityLineItem,
        Pricebook2Id,
        OwnerId,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        PushCount,
        LastStageChangeDate,
        FiscalQuarter,
        FiscalYear,
        Fiscal,
        ContactId,
        SourceId,
        LastViewedDate,
        LastReferencedDate,
        SyncedQuoteId,
        ContractId,
        HasOpenActivity,
        HasOverdueTask,
        LastAmountChangedHistoryId,
        LastCloseDateChangedHistoryId,
        AccountNameFormula__c,
        AgencySelectedByLocator__c,
        AssignmentDestination__c,
        AssignmentMode__c,
        Channel__c,
        Company__c,
        ContactCenterOutcome__c,
        ContactChannel__c,
        DomainType__c,
        IsPersonClient__c,
        JobPosition__c,
        JourneyStep__c,
        OwnerNameFormula__c,
        PolicyChannel__c,
        PromotionStatus__c,
        Rating__c,
        RecordTypeName__c,
        Record_Type_Name__c,
        SingleAreaOfNeed__c,
        Agency__c,
        AreaOfNeed__c,
        AssignedGroup__c,
        AssignedTo__c,
        AssignmentCounter__c,
        BankBranch__c,
        CIP__c,
        CanBeClosed__c,
        CanBeParent__c,
        ClosingDate__c,
        ContactCenterNotes__c,
        ContactPersonOfSubsidiary__c,
        CorporateProductofInterest__c,
        CreationDate__c,
        DirectionOwner__c,
        EngagementPoint__c,
        ErrorMessage__c,
        ExternalId__c,
        GlobalResearchInfo__c,
        HasCallMeBack__c,
        HasChildren__c,
        InternalCounter__c,
        IsSetRef__c,
        Luogo_di_Firma__c,
        OverallAreasOfNeed__c,
        Parent__c,
        PersonProductofInterest__c,
        ProductofInterest__c,
        PromotionId__c,
        ProposalId__c,
        ProposalIssueDate__c,
        Salespoint__c,
        TakenInChargeDate__c,
        TakenInChargeSLAExpiryDate__c,
        TakenInChargeSLAStartDate__c,
        ToBeReassigned__c,
        WorkingSLAExpiryDate__c,
        WorkingSLAStartDate__c,
        AccountPrimaryContact__c,
        AccountValusFormula__c,
        AgencyFormula__c,
        AllAreasofNeedFormula__c,
        AreasOfNeedFormula__c,
        AssignedGroupName__c,
        AssigneeGroupId__c,
        ButtonWrapperData__c,
        ClosureSubstatus__c,
        DirectionOwnerFormula__c,
        HasCallMeBackFormula__c,
        HasMultipleAreasFormula__c,
        LossReason__c,
        NameAndChannel__c,
        OverallAreasOfNeedFormula__c,
        PromotionDeadline__c,
        PromotionNeedsFormula__c,
        SalespointName__c,
        Substatus__c,
        TICSLARemainingDaysAdvanced__c,
        TakenInChargeSLARemainingDays__c,
        TakenInChargeSLARemainingHours__c,
        TemperatureFormula__c,
        WorkingSLARemainingDays__c,
        WorkingSLARemainingHours__c,
        HighestOldQuote__c,
        Assignee__c,
        MultiValueProducts__c,
        arrayValueProducts__c,
        Product__c,
        AccountHyperlink__c,
        DueDateFormula__c,
        Tipologia_soggetto_Unipol__c,
        RecordType.DeveloperName,
        Agency__r.Name,
        Agency__r.ExternalId__c
      FROM Opportunity
      WHERE AccountId = :accountId
      ORDER BY CloseDate DESC NULLS LAST
    ];
    opportunities = (List<Opportunity>) Security.stripInaccessible(
        AccessType.READABLE,
        opportunities
      )
      .getRecords();
    return opportunities;
  }

  public static List<Quote> queryQuotesByOpportunities(
    List<Opportunity> opportunities
  ) {
    if (opportunities == null || opportunities.isEmpty())
      return new List<Quote>();
    Set<Id> opportunityIds = new Set<Id>();
    for (Opportunity opportunityRecord : opportunities)
      if (opportunityRecord != null && opportunityRecord.Id != null)
        opportunityIds.add(opportunityRecord.Id);
    if (opportunityIds.isEmpty())
      return new List<Quote>();
    return queryQuotesByOpportunityIds(opportunityIds);
  }

  public static List<Quote> queryQuotesByOpportunityIds(
    Set<Id> opportunityIds
  ) {
    if (opportunityIds == null || opportunityIds.isEmpty())
      return new List<Quote>();
    List<Quote> quotes = [
      SELECT
        Id,
        OwnerId,
        IsDeleted,
        Name,
        RecordTypeId,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastViewedDate,
        LastReferencedDate,
        OpportunityId,
        Pricebook2Id,
        ContactId,
        QuoteNumber,
        IsSyncing,
        ShippingHandling,
        Tax,
        Status,
        ExpirationDate,
        Description,
        Subtotal,
        TotalPrice,
        LineItemCount,
        AdditionalState,
        AdditionalPostalCode,
        AdditionalCountry,
        AdditionalLatitude,
        AdditionalLongitude,
        AdditionalGeocodeAccuracy,
        AdditionalAddress,
        BillingName,
        ShippingName,
        QuoteToName,
        AdditionalName,
        Email,
        Phone,
        Fax,
        ContractId,
        AccountId,
        QuoteAccountId,
        Discount,
        GrandTotal,
        CanCreateQuoteLineItems,
        RatingDate,
        DomainType__c,
        ExpirationDateFormula__c,
        Rating__c,
        AreasOfNeed__c,
        ContractIdUnica__c,
        CreatedDateTPD__c,
        DocumentURL__c,
        EngagementPoint__c,
        ExtQuoteId__c,
        ExternalId__c,
        FolderId__c,
        IsStored__c,
        MonthlyContribution__c,
        QuoteAmount__c,
        AllAreasofNeedFormula__c,
        AreasOfNeedFormula__c,
        CreatedDateFormula__c,
        TemperatureFormula__c,
        CommercialStatus__c,
        isClosed__c,
        CIP__c,
        Channel__c,
        PolicyChannel__c
      FROM Quote
      WHERE OpportunityId IN :opportunityIds
      ORDER BY CreatedDate DESC
    ];
    quotes = (List<Quote>) Security.stripInaccessible(
        AccessType.READABLE,
        quotes
      )
      .getRecords();
    return quotes;
  }

  public static List<OpportunityCoverage__c> queryOpportunityCoveragesByQuoteIds(
    Set<Id> quoteIds
  ) {
    if (quoteIds == null || quoteIds.isEmpty())
      return new List<OpportunityCoverage__c>();
    List<OpportunityCoverage__c> coverageRecords = [
      SELECT
        Id,
        IsDeleted,
        Name,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        Quote__c,
        AreaOfNeed__c,
        Rating__c,
        StageName__c,
        Amount__c,
        Asset__c,
        BirthDate__c,
        Conventions__c,
        Description__c,
        Email__c,
        EngagementPoint__c,
        ExpectedYearlyGrowth__c,
        ExternalId__c,
        FirstName__c,
        FiscalCode__c,
        Fractionation__c,
        LastName__c,
        MobilePhone__c,
        NumberOfChildren__c,
        PrevidentialGap__c,
        ProductOfInterest__c,
        RAL__c,
        Sector__c,
        YearOfRetirement__c,
        AreasOfNeedFormula__c,
        TemperatureFormula__c
      FROM OpportunityCoverage__c
      WHERE Quote__c IN :quoteIds
      ORDER BY CreatedDate DESC
    ];
    coverageRecords = (List<OpportunityCoverage__c>) Security.stripInaccessible(
        AccessType.READABLE,
        coverageRecords
      )
      .getRecords();
    return coverageRecords;
  }

  public static List<OpportunityCoverage__c> queryOpportunityCoveragesByQuotes(
    List<Quote> quotes
  ) {
    if (quotes == null || quotes.isEmpty())
      return new List<OpportunityCoverage__c>();
    Set<Id> quoteIds = new Set<Id>();
    for (Quote quoteRecord : quotes)
      if (quoteRecord != null && quoteRecord.Id != null)
        quoteIds.add(quoteRecord.Id);
    return queryOpportunityCoveragesByQuoteIds(quoteIds);
  }

  public static List<FinServ__AccountAccountRelation__c> queryAccountFinancialsByAccountId(
    Id accountId
  ) {
    if (accountId == null)
      return new List<FinServ__AccountAccountRelation__c>();
    List<FinServ__AccountAccountRelation__c> accountAccountRelations = [
      SELECT
        Id,
        OwnerId,
        IsDeleted,
        Name,
        RecordTypeId,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        FinServ__Account__c,
        FinServ__Active__c,
        FinServ__EndDate__c,
        FinServ__ExternalId__c,
        FinServ__InverseRelationship__c,
        FinServ__RelatedAccount__c,
        FinServ__Role__c,
        FinServ__SourceSystemId__c,
        FinServ__StartDate__c,
        FinServ__AssociationType__c,
        FinServ__DeclarationType__c,
        FinServ__IsShareholder__c,
        FinServ__ShareholderHierarchyLevel__c,
        FinServ__ShareholderHierarchyRole__c,
        FinServ__ShareholderType__c,
        FinServ__TotalSharePercent__c,
        Cauzioni__c,
        Company__c,
        Subagenzia__c,
        Vip__c,
        Identifier__c,
        RelatedAccount_ExternalId__c,
        RecordType.DeveloperName
      FROM FinServ__AccountAccountRelation__c
      WHERE FinServ__Account__c = :accountId
    ];
    accountAccountRelations = (List<FinServ__AccountAccountRelation__c>) Security.stripInaccessible(
        AccessType.READABLE,
        accountAccountRelations
      )
      .getRecords();
    return accountAccountRelations;
  }

  public static List<AccountDetails__c> queryAccountDetailsByAccountId(
    Id accountId
  ) {
    if (accountId == null)
      return new List<AccountDetails__c>();
    List<AccountDetails__c> details = [
      SELECT
        Id,
        IsDeleted,
        Name,
        RecordTypeId,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        LastViewedDate,
        LastReferencedDate,
        Relation__c,
        AccountSource__c,
        AddressNormalizationType__c,
        Company__c,
        EmailPurposeType__c,
        EmailStatus__c,
        EmailUsageType__c,
        Etichetta__c,
        FaxPurposeType__c,
        FaxUsageType__c,
        FiscalCodeStatus__c,
        Gender__c,
        GeocodeAccuracy__c,
        MobilePurposeType__c,
        MobileStatus__c,
        MobileUsageType__c,
        OmnichannelBehaviour__c,
        OtherAddressNormalizationType__c,
        OtherEmailPurposeType__c,
        OtherEmailStatus__c,
        OtherEmailUsageType__c,
        OtherGeocodeAccuracy__c,
        OtherMobilePurposeType__c,
        OtherMobileStatus__c,
        OtherMobileUsageType__c,
        PhonePurposeType__c,
        PhoneStatus__c,
        PhoneUsageType__c,
        SourceSystemStatus__c,
        SpendingCapacity__c,
        VatNumberStatus__c,
        VoC__c,
        Account__c,
        AddressSourceSystemIdentifier__c,
        AddressType__c,
        Address__Street__s,
        Address__City__s,
        Address__PostalCode__s,
        Address__StateCode__s,
        Address__CountryCode__s,
        Address__Latitude__s,
        Address__Longitude__s,
        Address__GeocodeAccuracy__s,
        Address__c,
        AssurBanking__c,
        At__c,
        AtecoActivityType__c,
        AtecoCode__c,
        AtecoFlagConnection__c,
        AtecoFlagSociety__c,
        AtecoRAE__c,
        AtecoSAE__c,
        AtecoShortCode__c,
        AtecoShort__c,
        AtecoTypeCode__c,
        BPERProtectionEndDate__c,
        BPERProtectionStartDate__c,
        BillingAddress__Street__s,
        BillingAddress__City__s,
        BillingAddress__PostalCode__s,
        BillingAddress__StateCode__s,
        BillingAddress__CountryCode__s,
        BillingAddress__Latitude__s,
        BillingAddress__Longitude__s,
        BillingAddress__GeocodeAccuracy__s,
        BillingAddress__c,
        BillingCity__c,
        BillingCountry__c,
        BillingLatitude__c,
        BillingLongitude__c,
        BillingPostalCode__c,
        BillingState__c,
        BillingStreet__c,
        BirthCountry__c,
        BirthDate__c,
        BirthPlace__c,
        BirthProvince__c,
        CadastralCode__c,
        CadestralCountryCode__c,
        CensusDistrict__c,
        CertifiedEmail__c,
        City__c,
        CommercialConsent__c,
        CompanyType__c,
        ConsentDescription__c,
        ConsentVersion__c,
        Conventions__c,
        Country__c,
        DeathDate__c,
        Dug__c,
        Dus__c,
        Education__c,
        EmailFlagPreferred__c,
        EmailSourceSystemIdentifier__c,
        EmailSource__c,
        EmailType__c,
        Email__c,
        ExternalId__c,
        FEA__c,
        FaxFlagPreferred__c,
        FaxSourceSystemIdentifier__c,
        FaxSource__c,
        Fax__c,
        FeaEffectiveDate__c,
        FeaEndDate__c,
        FeaMail__c,
        FeaMobile__c,
        FeaOtherEmail__c,
        FeaOtherMobile__c,
        FeaOutEndDate__c,
        FeaVersionType__c,
        FirstName__c,
        FiscalCode__c,
        FullName__c,
        GeneralAgency__c,
        GroupConsent__c,
        HasOptedOutGroupShareConsent__c,
        IsContactable__c,
        IsCorporate__c,
        IsGroupMember__c,
        IsPublicBody__c,
        IstatCodeAnag1__c,
        IstatCode__c,
        LastName__c,
        LastPortfolioCancellationDate__c,
        Latitude__c,
        LifeStyle__c,
        Longitude__c,
        MDMCreateDate__c,
        MDMLastUpdateDate__c,
        MDMLastUpdateSystem__c,
        MobileFlagPreferred__c,
        MobileSourceSystemIdentifier__c,
        MobileSource__c,
        MobileType__c,
        Mobile__c,
        Occupation__c,
        OptInInformation__c,
        OptOutEffectiveDate__c,
        OptOutEndApplication__c,
        OptOutEndDate__c,
        OptOutStartApplication__c,
        OptOutType__c,
        OtherAddressSourceSystemIdentifier__c,
        OtherAddressType__c,
        OtherAt__c,
        OtherCadastralCode__c,
        OtherCadestralCountryCode__c,
        OtherCensusDistrict__c,
        OtherCity__c,
        OtherCountry__c,
        OtherDug__c,
        OtherDus__c,
        OtherEmailFlagPreferred__c,
        OtherEmailSourceSystemIdentifier__c,
        OtherEmailType__c,
        OtherEmail__c,
        OtherIstatCodeAnag1__c,
        OtherIstatCode__c,
        OtherLatitude__c,
        OtherLongitude__c,
        OtherMobileFlagPreferred__c,
        OtherMobileSourceSystemIdentifier__c,
        OtherMobileType__c,
        OtherMobile__c,
        OtherPostalCode__c,
        OtherShortCity__c,
        OtherShortStreet__c,
        OtherState__c,
        OtherStreetNumber__c,
        OtherStreet__c,
        PEC__c,
        PartyId__c,
        PaymentMethods__c,
        PhoneFlagPreferred__c,
        PhoneSourceSystemIdentifier__c,
        PhoneSource__c,
        PhoneType__c,
        Phone__c,
        PostalCode__c,
        PrivacyAreaLastAccessDate__c,
        ProfessionCode__c,
        ProfessionEmploymentType__c,
        ProfessionLegalEntityType__c,
        ProfessionSectorType__c,
        ProfessionSpecializationType__c,
        ProfilingConsent__c,
        ReportToBanks__c,
        ShortCity__c,
        ShortStreet__c,
        SourceSystemConsentCode__c,
        SourceSystemConsentEffectiveDate__c,
        SourceSystemConsentEndDate__c,
        SourceSystemCreatedDate__c,
        SourceSystemDescription__c,
        SourceSystemIdentifier__c,
        SourceSystemLastModifiedDate__c,
        SourceSystemOrigin__c,
        SourceSystemRegistryType__c,
        SourceSystemStatusDate__c,
        SourceSystemType__c,
        State__c,
        StreetNumber__c,
        Street__c,
        VatNumber__c,
        AccountDetailsNPI__c,
        RecordType.DeveloperName
      FROM AccountDetails__c
      WHERE Relation__r.FinServ__Account__c = :accountId
    ];
    details = (List<AccountDetails__c>) Security.stripInaccessible(
        AccessType.READABLE,
        details
      )
      .getRecords();
    return details;
  }

  public static List<AccountDetailsNPI__c> queryAccountDetailsNPIByAccountId(
    Id accountId
  ) {
    if (accountId == null)
      return new List<AccountDetailsNPI__c>();
    List<AccountDetailsNPI__c> details = [
      SELECT
        Id,
        OwnerId,
        IsDeleted,
        Name,
        RecordTypeId,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        AddressNormalizationType__c,
        AddressSourceSystemIdentifier__c,
        AddressType__c,
        AssurBanking__c,
        At__c,
        AtecoActivityType__c,
        AtecoCode__c,
        AtecoFlagConnection__c,
        AtecoFlagSociety__c,
        AtecoRAE__c,
        AtecoSAE__c,
        AtecoShortCode__c,
        AtecoShort__c,
        AtecoTypeCode__c,
        BPERProtectionEndDate__c,
        BPERProtectionStartDate__c,
        BirthCountry__c,
        BirthDate__c,
        BirthPlace__c,
        BirthProvince__c,
        CadastralCode__c,
        CadestralCountryCode__c,
        CensusDistrict__c,
        City__c,
        CompanyType__c,
        Company__c,
        ConsentDescription__c,
        ConsentVersion__c,
        Conventions__c,
        Country__c,
        DeathDate__c,
        Dug__c,
        Dus__c,
        EmailFlagPreferred__c,
        EmailPurposeType__c,
        EmailSourceSystemIdentifier__c,
        EmailSource__c,
        EmailStatus__c,
        EmailType__c,
        EmailUsageType__c,
        Email__c,
        ExternalId__c,
        FeaEffectiveDate__c,
        FeaEndDate__c,
        FeaVersionType__c,
        FirstName__c,
        FiscalCodeStatus__c,
        FiscalCode__c,
        FullName__c,
        Gender__c,
        GeocodeAccuracy__c,
        GroupConsent__c,
        IsCorporate__c,
        IsPublicBody__c,
        IstatCodeAnag1__c,
        IstatCode__c,
        LastName__c,
        LastPortfolioCancellationDate__c,
        Latitude__c,
        Longitude__c,
        MobileFlagPreferred__c,
        MobilePurposeType__c,
        MobileSourceSystemIdentifier__c,
        MobileSource__c,
        MobileStatus__c,
        MobileType__c,
        MobileUsageType__c,
        Mobile__c,
        OptInInformation__c,
        OptOutEffectiveDate__c,
        OptOutEndApplication__c,
        OptOutEndDate__c,
        OptOutStartApplication__c,
        OptOutType__c,
        OtherAddressNormalizationType__c,
        OtherAddressSourceSystemIdentifier__c,
        OtherAddressType__c,
        OtherAt__c,
        OtherCadastralCode__c,
        OtherCadestralCountryCode__c,
        OtherCensusDistrict__c,
        OtherCity__c,
        OtherCountry__c,
        OtherDug__c,
        OtherDus__c,
        OtherEmailFlagPreferred__c,
        OtherEmailPurposeType__c,
        OtherEmailSourceSystemIdentifier__c,
        OtherEmailStatus__c,
        OtherEmailType__c,
        OtherEmailUsageType__c,
        OtherEmail__c,
        OtherGeocodeAccuracy__c,
        OtherIstatCodeAnag1__c,
        OtherIstatCode__c,
        OtherLatitude__c,
        OtherLongitude__c,
        OtherMobileFlagPreferred__c,
        OtherMobilePurposeType__c,
        OtherMobileSourceSystemIdentifier__c,
        OtherMobileStatus__c,
        OtherMobileType__c,
        OtherMobileUsageType__c,
        OtherMobile__c,
        OtherPostalCode__c,
        OtherShortCity__c,
        OtherShortStreet__c,
        OtherState__c,
        OtherStreetNumber__c,
        OtherStreet__c,
        PhoneFlagPreferred__c,
        PhonePurposeType__c,
        PhoneSourceSystemIdentifier__c,
        PhoneSource__c,
        PhoneStatus__c,
        PhoneType__c,
        PhoneUsageType__c,
        Phone__c,
        PostalCode__c,
        PrivacyAreaLastAccessDate__c,
        ProfessionCode__c,
        ProfessionEmploymentType__c,
        ProfessionLegalEntityType__c,
        ProfessionSectorType__c,
        ProfessionSpecializationType__c,
        ProfilingConsent__c,
        ShortCity__c,
        ShortStreet__c,
        Society__c,
        SourceSystemConsentCode__c,
        SourceSystemConsentEffectiveDate__c,
        SourceSystemConsentEndDate__c,
        SourceSystemCreatedDate__c,
        SourceSystemLastModifiedDate__c,
        SourceSystemOrigin__c,
        SourceSystemRegistryType__c,
        SourceSystemStatusDate__c,
        SourceSystemStatus__c,
        SourceSystemType__c,
        State__c,
        StreetNumber__c,
        Street__c,
        VatNumberStatus__c,
        VatNumber__c,
        Relation__c,
        RecordType.DeveloperName
      FROM AccountDetailsNPI__c
      WHERE Relation__r.FinServ__Account__c = :accountId
    ];
    details = (List<AccountDetailsNPI__c>) Security.stripInaccessible(
        AccessType.READABLE,
        details
      )
      .getRecords();
    return details;
  }

  // Assets by Account (per docs/customer-structure.md)
  public static List<Asset> queryAssetsByAccountId(Id accountId) {
    if (accountId == null)
      return new List<Asset>();
    // Selecting essential fields used downstream
    List<Asset> assets = [
      SELECT
        Id,
        Name,
        CreatedDate,
        LastModifiedDate,
        ExternalId__c,
        Key__c,
        Value__c
      FROM Asset
      WHERE
        MasterRecordId__r.FinServ__Account__c = :accountId
        AND Key__c IN ('CRM_CLIENTE_VIP', 'CRMA_CAPACITADISPESA')
      ORDER BY CreatedDate DESC
    ];
    assets = (List<Asset>) Security.stripInaccessible(
        AccessType.READABLE,
        assets
      )
      .getRecords();
    return assets;
  }
}
