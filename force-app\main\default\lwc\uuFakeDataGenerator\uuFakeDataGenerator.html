<template>
  <div class="slds-m-around_medium section">
    <h3 class="slds-text-heading_small slds-m-bottom_small">Fake Data Generator</h3>
    <div class="slds-grid slds-gutters slds-m-bottom_small">
      <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-4">
        <lightning-combobox
          name="gender"
          label="Sesso"
          value={selectedGender}
          placeholder="Seleziona"
          options={genderOptions}
          onchange={handleGenderChange}
        ></lightning-combobox>
      </div>
      <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-4">
        <lightning-input
          type="text"
          name="seed"
          label="Seed"
          value={seed}
          placeholder="es. 1234 o abc"
          onchange={handleSeedChange}
        ></lightning-input>
      </div>
      <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2 slds-align_absolute-center">
        <lightning-button label="Generate User" onclick={generateUser}></lightning-button>
      </div>
    </div>
    <template if:true={user}>
      <lightning-layout multiple-rows class="slds-m-top_medium">
        <template for:each={userFields} for:item="field">
          <lightning-layout-item size="12" medium-device-size="4" key={field.label}>
            <div class="slds-grid slds-grid_vertical-align-center slds-p-right_medium">
              <div class="slds-col slds-grow">
                <lightning-input label={field.label} value={field.value} readonly></lightning-input>
              </div>
              <div class="slds-col slds-no-flex button-clipboard-container">
                <lightning-button-icon icon-name="utility:copy_to_clipboard" alternative-text={field.label} title={field.label} onclick={handleCopy} data-value={field.value}></lightning-button-icon>
              </div>
            </div>
          </lightning-layout-item>
        </template>
      </lightning-layout>
      <div class="slds-m-top_small">
        <lightning-button variant="neutral" label="Copia tutto (JSON)" onclick={handleCopyAllUser}></lightning-button>
      </div>
    </template>
  </div>
</template>
