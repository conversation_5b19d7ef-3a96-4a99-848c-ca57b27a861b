import { LightningElement, api } from "lwc";

export default class UuUserInfoSection extends LightningElement {
  @api userInfo;

  handleCopy(event) {
    const { value } = event.currentTarget.dataset;
    if (value) {
      navigator.clipboard.writeText(value);
    }
  }

  handleOpenUser(event) {
    const { value } = event.currentTarget.dataset;
    if (value) {
      window.open(`/lightning/r/User/${value}/view`, "_blank");
    }
  }
}
