<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <choices>
        <name>Unica</name>
        <choiceText>Unica</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Unica</stringValue>
        </value>
    </choices>
    <choices>
        <name>Unipol_Move</name>
        <choiceText>Unipol Move</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Unipol Move</stringValue>
        </value>
    </choices>
    <environments>Default</environments>
    <interviewLabel>cc Nuovo Prodotto {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Nuovo Prodotto</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Product_Selection</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>OpenInNewTab</name>
        <label>OpenInNewTab</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ccOpenSubtabFromFlow</name>
            <extensionName>c:ccOpenSubtabFromFlow</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>recordId</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>componentName</name>
                <value>
                    <stringValue>c__ccDisambiguationRootWrapper</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Product_Selection</name>
        <label>Product Selection</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>OpenInNewTab</targetReference>
        </connector>
        <fields>
            <name>Prodottii</name>
            <choiceReferences>Unica</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Prodotto</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Unica_logo</name>
            <extensionName>flowruntime:image</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>imageName</name>
                <value>
                    <stringValue>UnicaLogo</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>descrizione_unica</name>
            <fieldText>&lt;p&gt;Qui puoi trovare l&apos;offerta multiambito di Unipol: un unico prodotto che comprende Veicoli, Mobilità, Casa, Famiglia, Viaggio; Cane e Gatto &lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Prodotto_unipol</name>
            <choiceReferences>Unipol_Move</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Prodotto</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Unipol_move_logo</name>
            <extensionName>flowruntime:image</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>imageName</name>
                <value>
                    <stringValue>UnipolMoveLogo</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Descrizione_Unipol</name>
            <fieldText>&lt;p&gt;Qui puoi trovare il servizio di Telepedaggio per saltare la coda al casello che fa risparmiare tempo ai tuoi clienti. In più possono risparmiare anche sul canone Tech&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <pauseButtonLabel>Annulla</pauseButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>varEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varFirstName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varIban</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varLastName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varPhone</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varTarga</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
