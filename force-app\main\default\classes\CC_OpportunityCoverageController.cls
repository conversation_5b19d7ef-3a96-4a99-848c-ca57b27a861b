/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 08-21-2025
 * @last modified by  : <EMAIL>
**/
public with sharing class CC_OpportunityCoverageController {
    
    // FUNZIONA BENE MA NON SI PUO USARE
    // @AuraEnabled(cacheable=true)
    // public static List<OpportunityCoverage__c> getOpportunityCoverages(Id caseId) {
    //     system.debug('Provo a prendere le opportunity coverage');
    //     if (caseId == null) {
    //         throw new AuraHandledException('caseId obbligatorio.');
    //     }
    //     // CustomerWrapper cw = CustomerUtils.getCustomerByCaseId(caseId);
    //     // return (cw != null && cw.opportunityCoverages != null)
    //     //     ? cw.opportunityCoverages
    //     //     : new List<OpportunityCoverage__c>();

    //     // Quersto solo per test : 
    //     CustomerWrapper cw = CustomerUtils.getCustomerByAccountId('0019X00001NTQpNQAX');
    //     system.debug('OPP COV PRESE : '+cw.opportunityCoverages);
    //     if(cw.opportunityCoverages.size() > 0){
    //         system.debug('RITORNO OPP COV : '+cw.opportunityCoverages);
    //         return cw.opportunityCoverages;
    //     }else {
    //         system.debug('NIENTE RITORNO : ');
    //         return new List<OpportunityCoverage__c>();
    //     }
    // }

    @AuraEnabled(cacheable=true)
    public static List<OpportunityCoverage__c> getOpportunityCoverages(Id caseId) {
        system.debug('Provo a prendere le opportunity coverage');
        
        if (caseId == null) {
            throw new AuraHandledException('caseId obbligatorio.');
        }

        // 1. Recupera l'AccountId dal Case
        List<Case> caseRecords = [
            SELECT AccountId 
            FROM Case 
            WHERE Id = :caseId
        ];
        if (caseRecords.isEmpty() || caseRecords[0].AccountId == null) {
            throw new AuraHandledException('Account non trovato per il Case.');
        }
        Id accountId = '0019X00001NTQpNQAX'; //caseRecords[0].AccountId;

        List<OpportunityCoverage__c> opportunityCoverages = [
            SELECT 
                Id, 
                Name, 
                ProductOfInterest__c, 
                Description__c, 
                Fractionation__c, 
                Conventions__c, 
                //Assicurato__c, 
                Quote__c, 
                AreaOfNeed__c, 
                Amount__c, 
                StageName__c, 
                CreatedDate,
                Quote__r.Id,
                Quote__r.Name,
                Quote__r.Status,
                Quote__r.ExpirationDate,
                Quote__r.DocumentURL__c
            FROM OpportunityCoverage__c
            WHERE Quote__r.Opportunity.AccountId = :accountId
            ORDER BY CreatedDate DESC
        ];

        return (List<OpportunityCoverage__c>) Security.stripInaccessible(
            AccessType.READABLE,
            opportunityCoverages
        ).getRecords();
    }
}