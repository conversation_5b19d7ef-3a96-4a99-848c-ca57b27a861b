public without sharing class VoiceCallUpdater {

    public class VoiceCallUpdateRequest {
        @InvocableVariable(required=true)
        public Id voiceCallId;
        
        @InvocableVariable(required=false)
        public String CustomFieldApiName;
        
        @InvocableVariable(required=false)
        public String CustomFieldValue;
    }

    @InvocableMethod(label='Update Voice Call' description='Aggiorna campi su un record Voice Call')
    public static void updateVoiceCall(List<VoiceCallUpdateRequest> requests) {
        List<VoiceCall> voiceCallsToUpdate = new List<VoiceCall>();

        for (VoiceCallUpdateRequest req : requests) {
            VoiceCall vc = [SELECT Id FROM VoiceCall WHERE Id = :req.voiceCallId LIMIT 1];

            try {
                if (req.CustomFieldApiName != null) {
                    vc.put(req.CustomFieldApiName, req.CustomFieldValue);
                }
            } catch (Exception e) {
                System.debug('Errore aggiornamento campo: ' + req.CustomFieldApiName + ' - ' + e.getMessage());
            }

            voiceCallsToUpdate.add(vc);
        }

        if (!voiceCallsToUpdate.isEmpty()) {
            update voiceCallsToUpdate;
        }else {
            System.debug('nessun record da aggiornare');
        }
    }
}