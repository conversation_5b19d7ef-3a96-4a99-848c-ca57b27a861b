import { LightningElement, api } from 'lwc';
import { loadItems, upsertItem } from 'c/uuLocalStorageUtils';
import FIRST_NAMES from './firstNames';
import LAST_NAMES from './lastNames';
import CITIES from './cities';
import { MONTH_CODES, ODD_CODES, EVEN_CODES } from './fiscalCodeTables';

export default class UuFakeDataGenerator extends LightningElement {
  @api user;
  selectedGender = 'R';
  seed = '';

  HISTORY_KEY = 'uuFakeDataGeneratorHistory';

  connectedCallback() {
    // Load last generated data if present
    const [last] = loadItems(this.HISTORY_KEY);
    if (last) {
      this.user = last.user || null;
      this.selectedGender = last.selectedGender || 'R';
      this.seed = last.seed || '';
    }
  }

  get genderOptions() {
    return [
      { label: 'Random', value: 'R' },
      { label: '<PERSON><PERSON><PERSON>', value: 'M' },
      { label: '<PERSON><PERSON><PERSON>', value: 'F' }
    ];
  }

  get userFields() {
    return this.user
      ? [
          { label: 'Nome', value: this.user.firstName },
          { label: 'Cognome', value: this.user.lastName },
          { label: 'Data di Nascita', value: this.user.birthDate },
          { label: 'Sesso', value: this.user.gender },
          { label: 'Luogo di Nascita', value: this.user.birthPlace.name },
          { label: 'Codice Fiscale', value: this.user.fiscalCode },
          { label: 'Email', value: this.user.email },
          { label: 'Telefono', value: this.user.phone },
          { label: 'IBAN', value: this.user.iban },
          { label: 'Partita IVA', value: this.user.piva }
        ]
      : [];
  }

  handleCopy(event) {
    const { value } = event.currentTarget.dataset;
    if (value) {
      navigator.clipboard.writeText(value);
    }
  }

  handleCopyAllUser() {
    if (this.user) {
      navigator.clipboard.writeText(JSON.stringify(this.user, null, 2));
    }
  }

  handleGenderChange(event) {
    this.selectedGender = event.detail.value;
  }

  handleSeedChange(event) {
    this.seed = event.detail.value;
  }

  generateUser() {
    const rng = this.createRng(this.seed);
    const firstName = this.randomItem(FIRST_NAMES, rng);
    const lastName = this.randomItem(LAST_NAMES, rng);
    const birthDateObj = this.randomBirthDate(rng);
    const gender = this.resolveGender(rng);
    const birthPlace = this.randomItem(CITIES, rng);
    const fiscalCode = this.computeFiscalCode(
      firstName,
      lastName,
      birthDateObj,
      gender,
      birthPlace.code
    );
    const birthDate = birthDateObj.toISOString().slice(0, 10);
    const email = this.buildEmail(firstName, lastName, rng);
    const phone = this.buildPhone(rng);
    const iban = this.buildIban(rng);
    const piva = this.buildPiva(rng);
    this.user = {
      firstName,
      lastName,
      birthDate,
      gender,
      birthPlace,
      fiscalCode,
      email,
      phone,
      iban,
      piva
    };
    this.persist();
  }

  randomItem(arr, rng = Math.random) {
    return arr[Math.floor(rng() * arr.length)];
  }

  randomBirthDate(rng = Math.random) {
    const start = new Date(1960, 0, 1);
    const end = new Date(2005, 11, 31);
    const date = new Date(start.getTime() + rng() * (end.getTime() - start.getTime()));
    return date;
  }

  resolveGender(rng = Math.random) {
    // R = random, otherwise force M/F
    if (this.selectedGender === 'M' || this.selectedGender === 'F') return this.selectedGender;
    return rng() > 0.5 ? 'M' : 'F';
  }

  buildEmail(firstName, lastName, rng = Math.random) {
    const domains = ['example.com', 'mail.com', 'test.it', 'demo.org'];
    const domain = this.randomItem(domains, rng);
    const slug = `${firstName}.${lastName}`.toLowerCase().replace(/[^a-z]/g, '');
    return `${slug}@${domain}`;
  }

  buildPhone(rng = Math.random) {
    // Italian mobile-like: +39 3xx xxx xxxx
    const prefix = this.randomItem(['320', '327', '328', '329', '330', '331', '333', '334', '335', '336', '337', '338', '339', '340', '347', '348', '349'], rng);
    const a = String(Math.floor(100 + rng() * 900));
    const b = String(Math.floor(1000 + rng() * 9000));
    return `+39 ${prefix} ${a} ${b}`;
  }

  buildIban(rng = Math.random) {
    // Fake IBAN ITkk xzzz zzzz zzzz zzzz zzzz zzz (27 chars)
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const check = String(Math.floor(rng() * 90) + 10);
    const pool = letters + '0123456789';
    const bban = Array.from({ length: 23 }, () => this.randomItem(pool, rng)).join('');
    return `IT${check}${bban}`;
  }

  buildPiva(rng = Math.random) {
    // 11 digits with simple checksum (not official) just for testing
    let nums = '';
    for (let i = 0; i < 10; i++) nums += Math.floor(rng() * 10);
    const check = this.simpleChecksum(nums);
    return nums + check;
  }

  simpleChecksum(nums) {
    // sum digits mod 10
    const sum = nums.split('').reduce((acc, n) => acc + parseInt(n, 10), 0);
    return String(sum % 10);
  }

  computeFiscalCode(firstName, lastName, birthDate, gender, placeCode) {
    const surnameCode = this.codeSurname(lastName);
    const nameCode = this.codeName(firstName);
    const year = birthDate.getFullYear().toString().slice(-2);
    const monthCode = MONTH_CODES[birthDate.getMonth()];
    let day = birthDate.getDate();
    if (gender === 'F') {
      day += 40;
    }
    const dayCode = day.toString().padStart(2, '0');
    const partial = `${surnameCode}${nameCode}${year}${monthCode}${dayCode}${placeCode}`;
    const controlCode = this.controlChar(partial);
    return `${partial}${controlCode}`;
  }

  extractConsonants(str) {
    return str
      .toUpperCase()
      .replace(/[^BCDFGHJKLMNPQRSTVWXYZ]/g, '');
  }

  extractVowels(str) {
    return str
      .toUpperCase()
      .replace(/[^AEIOU]/g, '');
  }

  codeSurname(surname) {
    const cons = this.extractConsonants(surname);
    const vows = this.extractVowels(surname);
    return (cons + vows + 'XXX').slice(0, 3);
  }

  codeName(name) {
    const cons = this.extractConsonants(name);
    if (cons.length > 3) {
      return cons[0] + cons[2] + cons[3];
    }
    const vows = this.extractVowels(name);
    return (cons + vows + 'XXX').slice(0, 3);
  }

  controlChar(code) {
    const chars = code.toUpperCase().split('');
    let sum = 0;
    chars.forEach((ch, index) => {
      sum += index % 2 === 0 ? ODD_CODES[ch] : EVEN_CODES[ch];
    });
    return String.fromCharCode((sum % 26) + 65);
  }

  persist() {
    const record = {
      id: 'last',
      user: this.user,
      selectedGender: this.selectedGender,
      seed: this.seed,
      ts: Date.now()
    };
    upsertItem(this.HISTORY_KEY, record);
  }

  createRng(seedInput) {
    // If no seed provided, return Math.random
    if (!seedInput) return Math.random;
    // Convert string to 32-bit seed
    let seed = 0;
    const str = String(seedInput);
    for (let i = 0; i < str.length; i++) {
      seed = (seed * 31 + str.charCodeAt(i)) | 0;
    }
    if (seed === 0) seed = 123456789;
    // Mulberry32 PRNG
    let a = seed >>> 0;
    return function () {
      a |= 0;
      a = (a + 0x6D2B79F5) | 0;
      let t = Math.imul(a ^ (a >>> 15), 1 | a);
      t = (t + Math.imul(t ^ (t >>> 7), 61 | t)) ^ t;
      return ((t ^ (t >>> 14)) >>> 0) / 4294967296;
    };
  }
}
