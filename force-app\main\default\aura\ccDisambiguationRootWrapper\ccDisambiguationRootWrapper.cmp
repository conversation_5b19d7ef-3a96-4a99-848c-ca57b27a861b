<aura:component implements="flexipage:availableForAllPageTypes,lightning:isUrlAddressable,force:hasRecordId" access="global">
    <!-- URL-addressable page reference injected by nav service -->
    <aura:attribute name="pageReference" type="Object" />
    <aura:attribute name="caseId" type="String" />

    <aura:handler name="init" value="{! this }" action="{! c.doInit }"/>

    <!-- Console Workspace API to set tab label/icon -->
    <lightning:workspaceAPI aura:id="workspace"/>

    <!-- Render the LWC, passing down the Case Id -->
    <c:ccDisambiguationRoot caseId="{!v.caseId}" />
</aura:component>
