<template>
    <lightning-card>
        <div class="slds-p-around_medium">
            <template if:true={showSteps.step1}>
                <lightning-combobox
                    name={mergeData.country.name}
                    label={mergeData.country.label}
                    value={mergeData.country.value}
                    options={mergeData.country.options}
                    data-path={mergeData.country.path}
                    onchange={handleChange}>
                </lightning-combobox>
            </template>
            <template if:true={showSteps.step2}>
                <lightning-input
                    type={mergeData.travelDateStart.type}
                    name={mergeData.travelDateStart.name}
                    label={mergeData.travelDateStart.label}
                    value={mergeData.travelDateStart.value}
                    data-path={mergeData.travelDateStart.path}
                    onchange={mergeData.travelDateStart.validation}>
                </lightning-input>
                <lightning-input
                    type={mergeData.travelDateEnd.type}
                    name={mergeData.travelDateEnd.name}
                    label={mergeData.travelDateEnd.label}
                    value={mergeData.travelDateEnd.value}
                    data-path={mergeData.travelDateEnd.path}
                    onchange={mergeData.travelDateEnd.validation}>
                </lightning-input>
                <lightning-input
                    name={mergeData.numberOfPeople.name}
                    type={mergeData.numberOfPeople.type}
                    label={mergeData.numberOfPeople.label}
                    min={mergeData.numberOfPeople.min}
                    max={mergeData.numberOfPeople.max}
                    step="1"
                    value={mergeData.numberOfPeople.value}
                    data-path={mergeData.numberOfPeople.path}
                    onchange={handleChange}>
                </lightning-input>
            </template>
        </div>
    </lightning-card>
</template>