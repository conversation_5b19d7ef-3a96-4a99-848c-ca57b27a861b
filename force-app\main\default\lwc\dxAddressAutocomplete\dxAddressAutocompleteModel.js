/**
 * Struttura delle informazioni di Google Place
 */
export const StrutturaInformazioniGooglePlace = [
  'street_number',
  'route',
  'administrative_area_level_2',
  'administrative_area_level_3',
  'country',
  'political',
  'postal_code',
];

/**
 * Creates Google Place information structure
 * @returns {Object} Data structure for extraction
 */
export const CreaStrutturaInformazioniGooglePlace = () => {
  return {
    street_number: { format: 'short_name', value: '' },
    route: { format: 'long_name', value: '' },
    administrative_area_level_2: { format: 'short_name', value: '' },
    administrative_area_level_3: { format: 'short_name', value: '' },
    country: { format: 'long_name', value: '' },
    political: { format: 'short_name', value: '' },
    postal_code: { format: 'short_name', value: '' },
  };
};

/**
 * Interfaccia per le informazioni dell'indirizzo
 */
export class InformazioniIndirizzo {
  nomeStrada = '';
  civico = '';
  provincia = '';
  comune = '';
  cap = '';
  nazione = '';
  lat = null;
  lng = null;
  indirizzoFormattato = '';
}
