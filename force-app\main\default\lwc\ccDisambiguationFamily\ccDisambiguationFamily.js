import CcDisambiguationCore from "c/ccDisambiguationCore";

export default class CcDisambiguationFamily extends CcDisambiguationCore {
  get formData() {
    return {
      tipology: {
        name: "tipology",
        value: "",
        label: "Tipologia di Abitazione",
        options: this.entityDomains?.TIPOABITAZ || [],
        path: "Ambito.Bene.Famiglia.Immobile.TipologiaAbitazione",
      },
      address: {
        ...CcDisambiguationCore.FIELD_ADDRESS,
        label: "Indirizzo di residenza del capofamiglia",
      },
      effectiveDate: CcDisambiguationCore.FIELD_EFFECTIVE_DATE,
    };
  }
}
