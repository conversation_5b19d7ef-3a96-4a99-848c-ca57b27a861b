/* eslint-disable import/no-unresolved */
import { OmniscriptActionCommonUtil } from "omnistudio/omniscriptActionUtils";
import { getNamespaceDotNotation } from "omnistudio/omniscriptInternalUtils";

export default class IPUtils {
  static get invocationHistory() {
    if (!this._invocationHistory) {
      this._invocationHistory = [];
    }
    return this._invocationHistory;
  }

  static getInvocationHistory() {
    return this.invocationHistory;
  }

  static async invokeIntegrationProcedure(
    sMethodName,
    input = {},
    callback = null
  ) {
    const params = this.buildParams(sMethodName, input);
    const debugEnabled = this.isDebugEnabled();
    const start = new Date();

    // try to return mock if available
    const mockResponse = await this.tryReturnMock(
      sMethodName,
      input,
      debugEnabled,
      start,
      callback
    );
    if (mockResponse !== undefined) return mockResponse;

    try {
      const response = await this.executeRealCall(sMethodName, params);
      this.logInvocation(start, sMethodName, params.input?.request || params.input, response, "SUCCESS", debugEnabled);
      if (callback) {
        await callback(response);
      }
      return response;
    } catch (error) {
      this.logInvocation(start, sMethodName, params.input, error, "ERROR", debugEnabled);
      throw error;
    }
  }

  // helpers
  static buildParams(sMethodName, input) {
    const _ns = getNamespaceDotNotation();
    return {
      input,
      sMethodName,
      sClassName: `${_ns}IntegrationProcedureService`,
      options: {},
    };
  }

  static isDebugEnabled() {
    return (
      typeof window !== "undefined" &&
      window.localStorage &&
      window.localStorage.getItem("debug") === "true"
    );
  }

  static async tryReturnMock(sMethodName, input, debugEnabled, start, callback) {
    if (!debugEnabled || typeof window === "undefined" || !window.localStorage) return undefined;
    try {
      const raw = window.localStorage.getItem("iPUtilsDebuggerMockMap");
      if (raw) {
        const map = JSON.parse(raw) || {};
        if (Object.prototype.hasOwnProperty.call(map, sMethodName)) {
          const response = map[sMethodName];
          this.logInvocation(start, sMethodName, input?.request || input, response, "MOCK", true);
          if (callback) {
            await callback(response);
          }
          return response;
        }
      }
    } catch (e) {
      // ignore parsing errors and proceed
    }
    return undefined;
  }

  static async executeRealCall(sMethodName, params) {
    const _actionUtilClass = new OmniscriptActionCommonUtil();
    const {
      result: { IPResult: response },
    } = await _actionUtilClass.executeAction(params, null, this, null, null);
    console.log(
      "invokeIntegrationProcedure.payload",
      JSON.stringify({ name: sMethodName, request: params, response })
    );
    return response;
  }

  static logInvocation(start, sMethodName, request, response, status, debugEnabled) {
    if (!debugEnabled) return;
    this.invocationHistory.push({
      timestamp: start.getTime(),
      dateTime: start.toISOString(),
      procedureName: sMethodName,
      request,
      response,
      status,
    });
  }
}
