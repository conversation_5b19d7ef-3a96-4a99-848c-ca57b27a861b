<template>
  <template if:true={localhostMock}>
    <div style="padding: 10px; background: #f0f0f0; margin: 10px;">
      <h3>Mock Testing</h3>
      <lightning-button
        label="Prova mock installatore"
        onclick={handleMockInstallatore}>
      </lightning-button>
      <lightning-button
        label="Prova mock agenzia"
        onclick={handleMockAgenzia}>
      </lightning-button>
    </div>
  </template>

  <template if:true={areDataSetted}>
    <c-dx-locator-wrapper
      step-corrente={stepCorrente}
      agencies-list={formattedAgencies}
      is-loading={isLoading}
      field={field}
      parent-layout={groups}
      onagencyselected={handleSelezioneAgenzia}
      oninstallerselected={handleSelezioneInstallatore}
      oncomponentready={handleComponentLoading}
      onaddressselected={handleAddressSelected}
      onlocationchange={handleLocationChange}>
    </c-dx-locator-wrapper>
  </template>
</template>