<template>
  <div class="ip-utils-debugger-root">
    <lightning-card title="IP Utils Debugger" class="slds-m-around_medium">
      <div slot="actions">
        <lightning-button-menu alternative-text="Altre azioni" menu-alignment="right">
          <lightning-menu-item label="Salvati" value="saved" onclick={openHistory}></lightning-menu-item>
          <lightning-menu-item label="Logs IP" value="logs" onclick={openInvocationHistory}></lightning-menu-item>
        </lightning-button-menu>
      </div>

      <div class="slds-m-around_medium section">
        <div class="slds-grid slds-wrap slds-gutters">
          <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
            <lightning-input
              label="Integration Procedure Name"
              value={procedureName}
              onchange={handleProcedureChange}
            ></lightning-input>
          </div>
          <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
            <lightning-input
              label="Label"
              value={label}
              onchange={handleLabelChange}
            ></lightning-input>
          </div>
          <div class="slds-col slds-size_1-of-1">
            <div class="slds-form-element">
              <label class="slds-form-element__label" for="request-json">Request JSON</label>
              <div class="slds-form-element__control" id="request-json">
                <c-uu-json-view editable value={requestObject} onchange={handleRequestJsonChange}></c-uu-json-view>
              </div>
            </div>
            <div class="slds-m-top_small slds-m-bottom_small">
              <div class="slds-grid slds-wrap slds-gutters">
                <div class="slds-col slds-grow-none">
                  <lightning-button-group>
                    <lightning-button label="Formatta" onclick={formatRequest}></lightning-button>
                    <lightning-button label="Minimizza" onclick={minifyRequest}></lightning-button>
                    <lightning-button label="Copia" onclick={copyRequest}></lightning-button>
                    <lightning-button label="Pulisci" onclick={clearRequest}></lightning-button>
                  </lightning-button-group>
                </div>
                <div class="slds-col slds-grow slds-text-align_right">
                  <lightning-button
                    label="Invia"
                    onclick={handleInvokeIntegrationProcedure}
                    variant="brand"
                    disabled={disableSend}
                  ></lightning-button>
                </div>
              </div>
              <p class="slds-text-color_weak slds-m-top_x-small">Scorciatoia: Cmd/Ctrl + Invio per inviare</p>
            </div>
          </div>
        </div>
      </div>

      <template if:true={response}>
        <div class="slds-m-around_medium section">
          <div class="slds-grid slds-wrap slds-gutters slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
              <lightning-button-group>
                <lightning-button
                  label={responseJsonButtonLabel}
                  onclick={toggleResponseJson}
                ></lightning-button>
                <lightning-button
                  label="Copia"
                  onclick={copyResponseJson}
                ></lightning-button>
              </lightning-button-group>
            </div>
            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2 slds-text-align_right">
              <template if:true={durationMs}>
                <p class="slds-text-color_weak">Durata: {durationMs} ms</p>
              </template>
            </div>
          </div>
          <template if:true={showResponseJson}>
            <div class="slds-box slds-theme_default slds-scrollable_y" style="max-height: 28rem;">
              <template if:true={hasJsonResponse}>
                <c-uu-json-view value={responseObject}></c-uu-json-view>
              </template>
              <template if:false={hasJsonResponse}>
                <pre>{response}</pre>
              </template>
            </div>
          </template>
        </div>
      </template>
  </lightning-card>

  <!-- Invocation history modal moved to LightningModal wrapper (ipInvocationHistoryModal) -->

  <lightning-spinner
    alternative-text="Loading..."
    size="small"
    if:true={isLoading}
  ></lightning-spinner>
  </div>
</template>
