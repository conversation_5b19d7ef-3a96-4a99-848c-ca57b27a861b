/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 08-07-2025
 * @last modified by  : <EMAIL>
**/ 

public class CC_OptOutEmailHandler {

    public class EmailRequestWrapper {
        @InvocableVariable(label='Case Id')
        public Id caseId;

        @InvocableVariable(label='Flow Origin')
        public String flowOrigin;
    }

    @InvocableMethod(label='Send Opt-Out Email From Flow')
    public static void sendOptOutEmail(List<EmailRequestWrapper> requests) {
        if (requests == null || requests.isEmpty()) return;

        List<Id> caseIds = new List<Id>();
        Map<Id, String> flowOrigins = new Map<Id, String>();

        for (EmailRequestWrapper req : requests) {
            if (req.caseId != null) {
                caseIds.add(req.caseId);
                flowOrigins.put(req.caseId, req.flowOrigin);
            }
        }

        List<Contact> toContacts = [SELECT Id FROM Contact WHERE Email = 'e<PERSON><PERSON><PERSON><PERSON>@reply.it' LIMIT 1];
        if (toContacts.isEmpty()) {
            System.debug('Contatto dummy non trovato. Invio annullato.');
            return;
        }

        List<Case> cases = [
            SELECT Id, Account.FirstName, Account.LastName, DrCfPivaCliente__c,
                   Agency__c, NotesRecontact__c, EmailStatus__c
            FROM Case
            WHERE Id IN :caseIds
        ];

        Id templateId;
        try {
            templateId = [SELECT Id FROM EmailTemplate WHERE DeveloperName = 'OPT_OUT_Privacy_Template' LIMIT 1].Id;
        } catch (Exception e) {
            System.debug('Template email non trovato: ' + e.getMessage());
            return;
        }

        for (Case c : cases) {
            try {
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                email.setTargetObjectId(toContacts[0].Id);
                email.setToAddresses(new List<String>{ '<EMAIL>' }); // cambiare con email reale
                email.setTemplateId(templateId);
                email.setWhatId(c.Id);
                email.setSaveAsActivity(false);
                Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ email });
                if(flowOrigins.get(c.Id) == 'OPT OUT'){
                    c.EmailStatus__c  = 'InviatoOptOut';
                }else if(flowOrigins.get(c.Id) == 'Revoca CC') {
                    c.EmailStatus__c  = 'InviatoRevoca';
                }
            } catch (Exception ex) {
                c.EmailStatus__c  = 'Errore';
            }

        }

        if (!cases.isEmpty()) {
            update cases;
        }
    }
}

// public class CC_OptOutEmailHandler {

//     @InvocableMethod(label='Send Opt-Out Email')
//     public static void sendOptOutEmail(List<Id> caseIds) {
//         if (caseIds == null || caseIds.isEmpty()) return;

//         List<Case> cases = [
//             SELECT Id, EmailStatus__c
//             FROM Case
//             WHERE Id IN :caseIds
//         ];

//         Id templateId = [
//             SELECT Id
//             FROM EmailTemplate
//             WHERE DeveloperName = 'OPT_OUT_Privacy_Template'
//             LIMIT 1
//         ].Id;

//         Contact dummyContact = [
//             SELECT Id, Email
//             FROM Contact
//             WHERE Email = '<EMAIL>'
//             LIMIT 1
//         ];

//         List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
//         Map<Integer, Id> indexToCaseId = new Map<Integer, Id>();
//         Integer idx = 0;

//         for (Case c : cases) {
//             Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
//             email.setTargetObjectId(dummyContact.Id);
//             email.setTemplateId(templateId);
//             email.setWhatId(c.Id);
//             email.setToAddresses(new List<String>{ '<EMAIL>' });
//             email.setSaveAsActivity(false);

//             emails.add(email);
//             indexToCaseId.put(idx, c.Id);
//             idx++;
//         }

//         List<Messaging.SendEmailResult> results = Messaging.sendEmail(emails, false);

//         Map<Id, Case> updates = new Map<Id, Case>();
//         for (Integer i = 0; i < results.size(); i++) {
//             Id caseId = indexToCaseId.get(i);
//             Messaging.SendEmailResult res = results[i];

//             Case c = new Case(Id = caseId);
//             c.EmailStatus__c = res.isSuccess() ? 'Inviato' : 'Errore';
//             updates.put(caseId, c);
//         }

//         if (!updates.isEmpty()) {
//             update updates.values();
//         }
//     }
// }