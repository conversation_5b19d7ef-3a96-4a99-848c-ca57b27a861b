<template>
    <!-- <PERSON><PERSON> indirizzo (autocomplete) -->
    <template if:false={manualMode}>
        <lightning-input type="text" label={formData.address.label} name={formData.address.name}
            data-path={formData.address.path} value={formData.address.value} onchange={handleAddressChange}>
        </lightning-input>

        <template if:true={suggestions.length}>
            <ul class="slds-listbox slds-listbox_vertical slds-scrollable" role="listbox">
                <template for:each={suggestions} for:item="item">
                    <li key={item.value} class="slds-listbox__item" role="presentation" onclick={handleAddressSelect}
                        data-place-id={item.value}>
                        <div class="slds-listbox__option slds-listbox__option_plain" role="option">
                            {item.label}
                        </div>
                    </li>
                </template>
            </ul>
        </template>

        <!-- CTA: Inserimento manuale -->
        <div class="slds-m-top_small">
            <a class="slds-text-link" role="button" onclick={enableManualMode}>Inserisci indirizzo manualmente</a>
        </div>
    </template>

    <!-- Form indirizzo manuale -->
    <template if:true={manualMode}>
        <div class="slds-grid slds-wrap slds-gutters_small">
            <div class="slds-col slds-size_1-of-1 slds-medium-size_2-of-3">
                <lightning-input type="text" label="Via / Piazza" name="NomeStrada"
                    value={manualStreet} onchange={handleManualChange} required></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-3">
                <lightning-input type="text" label="N Civico" name="NumeroCivico"
                    value={manualCivic} onchange={handleManualChange} required></lightning-input>
            </div>

            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                <div class="slds-form-element" role="combobox" aria-expanded={provinceOpen} aria-haspopup="listbox">
                    <label class="slds-form-element__label" for="prov-input">Provincia</label>
                    <div class="slds-form-element__control">
                        <input
                            id="prov-input"
                            class="slds-input"
                            type="text"
                            placeholder="Cerca provincia (nome o sigla)"
                            value={provinceSearch}
                            oninput={handleProvinceSearchInput}
                            onfocus={handleProvinceFocus}
                            onblur={handleProvinceBlur}
                            required
                        />
                    </div>
                    <template if:true={provinceOpen}>
                        <div class="slds-dropdown slds-dropdown_fluid slds-dropdown_length-5 slds-show" role="listbox">
                            <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                                <template for:each={provinceSuggestions} for:item="opt">
                                    <li key={opt.value} role="presentation" class="slds-listbox__item">
                                        <div role="option" tabindex="0" class="slds-listbox__option slds-listbox__option_plain"
                                             data-value={opt.value} data-label={opt.label}
                                             onclick={handleProvinceSuggestionSelect}>
                                            <span class="slds-truncate" title={opt.label}>{opt.label}</span>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </template>
                </div>
            </div>

            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                <div class="slds-form-element" role="combobox" aria-expanded={townOpen} aria-haspopup="listbox">
                    <label class="slds-form-element__label" for="town-input">Comune</label>
                    <div class="slds-form-element__control">
                        <input
                            id="town-input"
                            class="slds-input"
                            type="text"
                            placeholder="Cerca comune"
                            value={townSearch}
                            oninput={handleTownSearchInput}
                            onfocus={handleTownFocus}
                            onblur={handleTownBlur}
                            disabled={isTownSearchDisabled}
                            required
                        />
                    </div>
                    <template if:true={townOpen}>
                        <div class="slds-dropdown slds-dropdown_fluid slds-dropdown_length-5 slds-show" role="listbox">
                            <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                                <template for:each={townSuggestions} for:item="opt">
                                    <li key={opt.value} role="presentation" class="slds-listbox__item">
                                        <div role="option" tabindex="0" class="slds-listbox__option slds-listbox__option_plain"
                                             data-value={opt.value} data-label={opt.label}
                                             onclick={handleTownSuggestionSelect}>
                                            <span class="slds-truncate" title={opt.label}>{opt.label}</span>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </template>
                </div>
            </div>

            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-3">
                <lightning-input type="text" label="Cap" name="Cap"
                    value={manualCap} onchange={handleManualChange} required></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-1 slds-medium-size_2-of-3">
                <lightning-combobox label="Stato" name="Stato" value={manualState} onchange={handleManualStateChange}
                    options={stateOptions} required></lightning-combobox>
            </div>
        </div>

        <div class="slds-m-top_small">
            <lightning-button variant="neutral" label="Annulla" onclick={handleManualCancel} class="slds-m-right_x-small"></lightning-button>
            <lightning-button variant="brand" label="Conferma" onclick={handleManualConfirm} disabled={isManualConfirmDisabled}></lightning-button>
        </div>
    </template>

    <lightning-spinner alternative-text="Loading..." size="small" if:true={spinner}>
    </lightning-spinner>
</template>