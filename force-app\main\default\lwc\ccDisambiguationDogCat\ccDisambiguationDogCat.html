<template>
    <lightning-card>
        <div class="slds-p-around_medium">
            <lightning-combobox
                name={mergeData.tipology.name}
                label={mergeData.tipology.label}
                value={mergeData.tipology.value}
                options={mergeData.tipology.options}
                data-path={mergeData.tipology.path}
                onchange={handleChange}>
            </lightning-combobox>
            <lightning-combobox
                name={mergeData.age.name}
                label={mergeData.age.label}
                value={mergeData.age.value}
                options={mergeData.age.options}
                data-path={mergeData.age.path}
                onchange={handleChange}>
            </lightning-combobox>
            <c-cc-disambiguation-effective-date
                form-data={mergeData}
                onfieldchange={handleChildChange}>
            </c-cc-disambiguation-effective-date>
        </div>
    </lightning-card>
</template>
