import { LightningElement, api, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getCustomerByCaseId from '@salesforce/apex/CustomerUtils.getCustomerByCaseId';

export default class CcTabContainerPreview extends NavigationMixin(LightningElement) {
  @api recordId; 
  @track customerData

  get accountId() {
  return this.customerData && this.customerData.account
    ? this.customerData.account.Id
    : null;
  }

  get fiscalCode() {
    return this.customerData && this.customerData.account
      ? this.customerData.account.ExternalId__c
      : null;
  }

  connectedCallback() {
    this.loadCustomerData();
  }

  async loadCustomerData() {
    try {
      console.log('recordIdx ', this.recordId);
      const data = await getCustomerByCaseId({ caseId: this.recordId });
      console.log('Customer data loaded: ', JSON.stringify(data));
      this.customerData = data ? { ...data } : null;
    } catch (error) {
      console.error('Error loading customer data:', error);
    }
  }

  handleViewAllQuotes() {

    this.dispatchEvent(new CustomEvent('viewallquotes', { detail: { caseId: this.recordId }}));
  }

  handleAddToCart(event) {
    const { quoteId } = event.detail || {};
    this.dispatchEvent(new CustomEvent('addtocart', { detail: { quoteId } }));
  }

  handleViewPdf(event) {
    const { quoteId } = event.detail || {};
    this.dispatchEvent(new CustomEvent('viewpdf', { detail: { quoteId } }));
  }
}