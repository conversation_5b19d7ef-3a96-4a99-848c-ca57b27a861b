import { utils } from "c/dxUtils";

/// <reference path="./bffInterprete.jsdoc.js" />

const AUTH_URL_SECURE = `interprete-web/v1/secure`;
const AUTH_URL_UNSECURE = `interprete-web/v1/unsecure`;

// const BASE_PATH = 'https://*************/api/pub';
const BASE_PATH = "https://evo-dev.unipolsai.it/api/pub";
const BASE_PATH_MOCK = "http://localhost:3000";
const NORM_PATH =
  "https://evo-dev.unipolsai.it/api/pub/indirizzi/v2/normalizza";
const BASE_PATH_BFF =
  "https://tpd-vendita-ibrida-interprete-web-evo-dev.servizi.gr-u.it";

const POI_PATH = "poiLocator/v1/bounds/poi";

/** @type {PEGA_API} */
export const PEGA_API = {
  loadPage: 0,
  nextPage: 1,
  updatePage: 2,
};

/** @type {UNI_API} */
export const UNI_API = {
  agencyList: 3,
  agencyBound: 4,
  agencyDetail: 5,
  listProvince: 6,
  ListTown: 7,
  normalize: 8,
};

/** @type {IActionLoadRequestEnum} */
export const ACTION_LOAD_REQUEST = {
  create: "CREATE",
  retrieve: "retrieve",
  duplicate: "duplicate",
  reload: "reload",
};

export default class BffInterprete {

  static getInstance(config) {
    if (!BffInterprete.instance) {
      BffInterprete.instance = new BffInterprete(config);
    }
    return BffInterprete.instance;
  }

  _userLogged = false; // TODO: Implementare il recupero dello stato di login
  _mockUrls = false; // flag utilizzato per eseguire il mock degli url

  _assignmentId;
  _actionID;
  _caseID;

  constructor(config) {
    this.config = config;
    const paramsUrl = new URLSearchParams(window.location.search);
    this._mockUrls = paramsUrl.get("mockUrls") === "true";
  }

  getURLS() {
    const basePath = this._mockUrls ? BASE_PATH_MOCK : BASE_PATH;
    const authURL = this._userLogged ? AUTH_URL_SECURE : AUTH_URL_UNSECURE;

    return {
      loadPage: `CC_DXLoadPage`,
      nextPage: `CC_DXGoNextPage`,
      updatePage: `CC_DXUpdatePage`,
      agencyList: `CC_AgencyList`,
      agencyBound: `CC_AgencyBound`,
      agencyDetail: `CC_DXAgencyDetail`,
      listProvince: `CC_ListProvince`,
      listTown: `CC_ListTown`,
      normalize: `CC_Normalizza`,
      getNormalizzationUrl: () => NORM_PATH, // Funzione per ottenere l'URL di normalizzazione
      getAllAgencies: `${basePath}/${POI_PATH}?category=AGENZIA`,
      getCloseAgencies: (lat, lng) =>
        `${basePath}/${POI_PATH}?category=AGENZIA&lat=${lat}&lng=${lng}&maxItem=5000`,
    };
  }

  /**
   * @param {PegaAPI} api
   * @param {any} request
   * @param {string} [target]
   * @returns {Promise<any>}
   */
  async makeRequest(api, request, target) {
    const response = await this.apiMapper[api](request, target);

    if (response.status >= 400) {
      const error = response.error || "Errore nella risposta API";
      throw { status: response.status, message: error, body: response };
    }

    switch (target) {
      case "modalDialog":
        break;

      default:
        this._actionID = response?.metaBodyResponse?.actionId;
        this._assignmentId = response?.metaBodyResponse?.assignmentId;
        this._caseID = response?.pegaBodyResponse?.caseID;
        break;
    }

    return response;
  }

  /**
   * @typedef {Object} ApiMapper
   * @property {function(ILoadPageRequest): Promise<any>} [PEGA_API.loadPage] -
   * @property {function(IGoNextPageRequest): Promise<any>} [PEGA_API.nextPage]
   * @property {function(IUpdatePageRequest): Promise<any>} [PEGA_API.updatePage]
   * @property {function(IAgencyListRequest): Promise<any>} [UNI_API.agencyList]
   * @property {function(IAgencyBoundRequest): Promise<any>} [UNI_API.agencyBound]
   * @property {function(IAgencyDetailRequest): Promise<any>} [UNI_API.agencyDetail]
   */

  /** @type {ApiMapper} */
  apiMapper = {
    [PEGA_API.loadPage]: (request) => {
      return this.loadPage(request);
    },
    [PEGA_API.nextPage]: (request) => {
      return this.nextPage(request);
    },
    [PEGA_API.updatePage]: (request) => {
      return this.updatePage(request);
    },
    [UNI_API.agencyList]: (request) => {
      return this.agencyList(request);
    },
    [UNI_API.agencyBound]: (request) => {
      return this.agencyBound(request);
    },
    [UNI_API.agencyDetail]: (request) => {
      return this.agencyDetail(request);
    },
    [UNI_API.listProvince]: (request) => {
      return this.listProvince(request);
    },
    [UNI_API.listTown]: (request) => {
      return this.listTown(request);
    },
    [UNI_API.normalize]: (request) => {
      return this.normalize(request);
    },
  };

  /**
   * @typedef {Object} RequestMapper
   * @property {function(IActionLoadRequest, any): Promise<any>} [PEGA_API.loadPage]
   * @property {function(any): Promise<any>} [PEGA_API.nextPage]
   * @property {function(any): Promise<any>} [PEGA_API.updatePage]
   */

  /** @type {RequestMapper} */
  requestMapper = {
    [PEGA_API.loadPage]: (actionOnLoad, overrides) => {
      return {
        assignmentId: this._assignmentId,
        action: actionOnLoad,
        caseId: this._caseID,
        productType: this.config?.productType,
        env: this.config?.env,
        // retrieveType: this.retrieveType,
        // referencesToUpdate: this.directLogin
        //   ? {
        //       ...this._initData,
        //       ...this.processManagement,
        //       ...this.gestioneProcesso,
        //       ...this.VediEModifica
        //     }
        //   : this._initData,
        // idOfferta: this.idOfferta,
        // versione: this.versione,
        // env: this.env
        ...overrides,
      };
    },
    [PEGA_API.nextPage]: (overrides) => {
      return {
        assignmentId: this._assignmentId,
        actionId: this._actionID,
        productType: this.config?.productType,
        // captchaToken: this.statusService.captchaToken,
        // retrieveType: this.retrieveType,
        // referencesToUpdate: {
        //   ...this.formValue,
        //   ...this.processManagement,
        //   ...this.gestioneProcesso,
        //   ...this.VediEModifica
        // },
        // abTesting: this.abTesting,
        ...overrides,
      };
    },
    [PEGA_API.updatePage]: (overrides) => {
      return {
        assignmentId: this._assignmentId,
        actionId: this._actionID,
        productType: this.config?.productType,
        // referencesToUpdate: {
        //   ...this.formValue,
        //   ...this.processManagement,
        //   ...this.gestioneProcesso
        // },
        // refreshFor: this.refreshFor,
        ...overrides,
      };
    },
  };

  /**
   * @param {ILoadPageRequest} loadPageBody - The body of the request to load the page.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async loadPage(loadPageBody) {
    return utils.apiRequest("IP", this.getURLS().loadPage, null, loadPageBody);
  }

  /**
   * @param {IGoNextPageRequest} nextPageBody - The body of the request to load the next page.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async nextPage(nextPageBody) {
    return utils.apiRequest("IP", this.getURLS().nextPage, null, nextPageBody);
  }

  /**
   * @param {IUpdatePageRequest} updatePageBody - The body of the request to update the page.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async updatePage(updatePageBody) {
    return utils.apiRequest(
      "IP",
      this.getURLS().updatePage,
      null,
      updatePageBody,
    );
  }

  /**
   * @param {IAgencyListRequest} request - The request object for the agencyList API.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async agencyList(request) {
    return utils.apiRequest("IP", this.getURLS().agencyList, null, request);
  }

  /**
   * @param {IAgencyBoundRequest} request - The request object for the agencyBound API.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async agencyBound(request) {
    return utils.apiRequest("IP", this.getURLS().agencyBound, null, request);
  }

  /**
   * @param {IAgencyDetailRequest} request - The request object for the agencyDetail API.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async agencyDetail(request) {
    return utils.apiRequest("IP", this.getURLS().agencyDetail, null, request);
  }

  async listProvince(request) {
    return utils.apiRequest("IP", this.getURLS().listProvince, null, request);
  }

  async listTown(request) {
    return utils.apiRequest("IP", this.getURLS().listTown, null, request);
  }
  
  async normalize(request) {
    return utils.apiRequest("IP", this.getURLS().normalize, null, request);
  }
}
