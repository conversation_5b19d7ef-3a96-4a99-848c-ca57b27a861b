<template>
    <lightning-modal-header label={title}></lightning-modal-header>
    <lightning-modal-body>
        <template if:true={hasFields}>
            <div class="form-grid slds-p-bottom_small">
                <template for:each={fields} for:item="f">
                    <lightning-input key={f.label} label={f.label} value={f.value} readonly></lightning-input>
                </template>
            </div>
        </template>
        <template if:true={record}>
            <c-uu-json-view value={record} height="20rem"></c-uu-json-view>
        </template>
        <template if:false={record}>
            <div class="slds-p-vertical_small slds-text-color_weak">Dettagli non disponibili.</div>
        </template>
    </lightning-modal-body>
    <lightning-modal-footer>
        <lightning-button label="Chiudi" onclick={handleClose}></lightning-button>
    </lightning-modal-footer>
</template>
