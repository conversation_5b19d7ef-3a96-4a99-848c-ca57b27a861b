import { LightningElement, api } from 'lwc';
import { utils } from "c/dxUtils";
import { utilsPegaText } from "c/dxUtilsPegaText";


export default class CardSezione extends LightningElement {
    _field;

    @api groups;

    @api
    set field(input) {
        if (input) {
            this._field = input;
        }
    }

    get field() {
        return this._field;
    }

    get selected() {
        return this.field?.customAttributes?.selected === 'true';
    }
    
    get deselectedText() {
        return this.field?.customAttributes?.deselectedText || '';
    }
    
    get renderGroups() {
        return this.groups.filter(
            group => group?.field?.control?.type !== 'pxHidden'
        );
    }

    get containerClass() {
        return `CardSezioneContainer ${!this.selected ? 'deselected' : ''}`;
    }

    get deselectedText () {
        return  this.field?.customAttributes?.deselectedText || '';
    }

    get deselectedTextStyle () {
        return 'TEXT APP BDB16 WEB BLB18C BLB18C BLB18C';
    }


}