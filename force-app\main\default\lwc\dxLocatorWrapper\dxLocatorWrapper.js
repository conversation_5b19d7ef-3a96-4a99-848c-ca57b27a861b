import { LightningElement, track, api } from 'lwc';
import getPlacesAutocomplete from '@salesforce/apex/GoogleMapsProxy.getPlacesAutocomplete';
import getPlaceDetails from '@salesforce/apex/GoogleMapsProxy.getPlaceDetails';

export default class LocatorWrapper extends LightningElement {
  @api stepCorrente = 'scelta_agenzia';
  @api isLoading = false;

  @api field;
  @api parentLayout;

  userLat;
  userLng;
  
  _agencies = [];

  @track mapCenter = {};
  @track mapMarkers = [];
  @track userLocation = null;
  @track inputValue = '';
  @track predictions = []; 
  @track selectedAddress;
  @track selectedAgency = '';
  @track selectedMarker;
  @track showCustomPopup = false;
  inputPrincipaleUtente = '';              
  inputPrincipaleClass = 'standard-input'; 
  isSearchingPredictions = false;          
  shouldShowDropdown = false;              
  predictions = [];        

mapInitialized = false;
 
  @api
  set agenciesList(value) {
    this._agencies = value || [];
    this.updateMapMarkers();
  }
  
  get agenciesList() {
    return this._agencies;
  }

  formatOpeningHours(rawOpeningTime) {
    if (!rawOpeningTime) return 'Orari non disponibili';
  
    const days = ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'];
    const slots = rawOpeningTime.split('|').filter(Boolean);
    const formatted = [];
  
    for (let i = 0; i < days.length; i++) {
      const base = i * 4;
      const open1 = slots[base] || '';
      const close1 = slots[base + 1] || '';
      const open2 = slots[base + 2] || '';
      const close2 = slots[base + 3] || '';
  
      let line = '';
      if (open1.toUpperCase() === 'CHIUSO' || !open1) {
        line = `${days[i]}: CHIUSO`;
      } else {
        line = `${days[i]}: ${open1}-${close1}`;
        if (open2 && close2) {
          line += ` / ${open2}-${close2}`;
        }
      }
  
      formatted.push(line);
    }
  
    return formatted.join('\n');
  }
  
  updateMapMarkers() {
    if (!Array.isArray(this._agencies)) {
        console.warn('[LocatorWrapper] _agencies non è un array:', this._agencies);
        return;
    }

    this.mapMarkers = this._agencies
        .filter(agency => agency && agency.location)
        .map(agency => ({
            location: agency.location,
            title: agency.name,
            description: agency.mapMarkerData?.description || agency.address,
            icon: agency.icon || 'standard:location',
            value: agency.id,
            mapMarkerData: agency.mapMarkerData
        }));

    if (this.mapMarkers.length > 0) {
        this.mapCenter = {
            location: this.mapMarkers[0].location
        };
    }
  }
  
  get hasMarkers() {
    return Array.isArray(this.mapMarkers) && this.mapMarkers.length > 0;
  }

  connectedCallback() {
    this.mapMarkers = this.agenciesList.map(agency => ({
      location: {
        Latitude: parseFloat(agency.latitude),
        Longitude: parseFloat(agency.longitude)
      },
      title: agency.name,
      description: `${agency.address}, ${agency.town}`,
    }));

    if (this.mapMarkers.length > 0) {
      this.mapCenter = {
        Latitude: this.mapMarkers[0].location.Latitude,
        Longitude: this.mapMarkers[0].location.Longitude
      };
    }
  }

    
  handleAgencySelect(evt) {
    const agencyId = evt.currentTarget.dataset.id;
    const selectedAgency = this._agencies.find(agency => agency.id === agencyId);
  
    if (selectedAgency) {
      console.log('[locatorWrapper] Agenzia selezionata:', selectedAgency);
  
      this.dispatchEvent(new CustomEvent('agencyselected', {
        detail: selectedAgency.rawData || selectedAgency,
        bubbles: true,
        composed: true
      }));
    }
  }
  
  
   
handleAddressSelected(event) {
  const { lat, lng } = event.detail;
  if (lat && lng) {
    this.loadNearestAgencies(lat, lng);
  }
}

  handleAgencyChange(event) {
      this.selectedAgency = event.detail.value;
      if (this.selectedAgency && this.agencies[this.selectedAgency]) {
          const agency = this.agencies[this.selectedAgency];
          this.mapCenter = {
              location: {
                  Latitude: agency.Latitude,
                  Longitude: agency.Longitude
              }
          };
          this.mapMarkers = [
            {
              location: {
                  Latitude: agency.Latitude,
                  Longitude: agency.Longitude
              },
              title: agency.Name
            }
          ];
        } else {
          this.mapMarkers = [];
        }
    }

  handleAgencyClick() {
  }

  addressAutocompleteTest = {
    label: '',
    control: {
      modes: [{ placeholder: 'Città, Indirizzo, CAP' }],
      options: [],
    },
    readOnly: false,
    labelFormat: 'Text APP GDB16 WEB BAB16 BAB16 BAB16',
    disabled: false,
    value: '',
    reference: '',
  };

  debounceTimeout;

  handleInputChange(event) {
    this.inputValue = event.target.value;

    clearTimeout(this.debounceTimeout);
    if (this.inputValue.length < 3) {
      this.predictions = [];
      return;
    }

    this.debounceTimeout = setTimeout(() => {
      getPlacesAutocomplete({ input: this.inputValue })
        .then(result => {

          const response = JSON.parse(result);
          if (response.error) {
            this.predictions = [];
            console.error('Errore autocomplete:', response.error);
          } else {
            this.predictions = response.predictions;
          }
        })
        .catch(error => {
          console.error('Errore chiamata Apex:', error);
          this.predictions = [];
        });
    }, 100); 
  }

  handleSelectPrediction(event) {
    const placeId = event.currentTarget.dataset.placeId;

    getPlaceDetails({ placeId })
      .then(result => {
        const response = JSON.parse(result);
        if (response.error) {
          console.error('Errore dettagli luogo:', response.error);
        } else {
          this.selectedAddress = response.result;
          this.predictions = [];
          this.inputValue = this.selectedAddress.formatted_address;

          const lat = this.selectedAddress.geometry.location.lat;
          const lng = this.selectedAddress.geometry.location.lng;

          this.mapCenter = {
            location: {
              Latitude: lat,
              Longitude: lng
            }
          };
          this.dispatchEvent(new CustomEvent('locationchange', {
            detail: { lat, lng },
            bubbles: true, 
            composed: true
          }));
        }
      })
      .catch(error => {
        console.error('Errore chiamata dettagli luogo:', error);
      });
  }
}