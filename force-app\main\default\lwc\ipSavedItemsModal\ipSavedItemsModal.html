<template>
	<lightning-modal-header label="Salvati"></lightning-modal-header>
	<lightning-modal-body>
		<div class="slds-m-top_small">
			<lightning-input type="search" label="Cerca" value={historySearchTerm} onchange={handleHistorySearchChange}></lightning-input>
			<div class="slds-m-top_small">
				<lightning-button-group>
					<lightning-button label="Esporta JSON" onclick={exportHistory}></lightning-button>
					<lightning-button label={importToggleButtonLabel} onclick={toggleImportArea}></lightning-button>
				</lightning-button-group>
			</div>
		</div>
		<template if:true={showImportArea}>
			<lightning-textarea label="Incolla JSON da importare" value={importJson} onchange={handleImportJsonChange} data-id="import"></lightning-textarea>
			<div class="slds-m-top_small">
				<lightning-button label="Importa" onclick={importHistory}></lightning-button>
			</div>
			<hr class="slds-m-vertical_medium"/>
		</template>

		<template if:true={hasFilteredHistory}>
			<lightning-datatable
				key-field="id"
				data={filteredHistory}
				columns={historyColumns}
				hide-checkbox-column
				onrowaction={handleHistoryRowAction}
			></lightning-datatable>
		</template>
		<template if:false={hasFilteredHistory}>
			<p>Nessun elemento salvato</p>
		</template>
	</lightning-modal-body>
	<lightning-modal-footer>
		<lightning-button label="Chiudi" onclick={handleClose}></lightning-button>
	</lightning-modal-footer>
</template>

