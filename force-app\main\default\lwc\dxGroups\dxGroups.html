<template>
  <template if:true={isVisible}>
    <div class="groupDetector">
      <div if:false={customComponent} class={componentClass} style={computedStyles}>
        <template if:true={groups.view}>
          <template if:true={groups.view.visible}>
            <c-dx-view view={groups.view} debug={debug}></c-dx-view>
          </template>
        </template>

        <template if:true={groups.layout}>
          <c-dx-layout debug={debug} layout={groups.layout}></c-dx-layout>
        </template>

        <template if:true={groups.field}>
          <c-dx-field field={groups.field} debug={debug}></c-dx-field>
        </template>

        <template if:true={groups.caption}>
          <c-dx-field debug={debug} field={groups.caption} field-type="caption"></c-dx-field>
        </template>

        <template if:true={groups.paragraph}>
          <c-dx-field debug={debug} field={groups.paragraph} field-type="paragraph"></c-dx-field>
        </template>

      </div>
      <div if:true={customComponent} class={componentClass}>
        <template if:true={groups.field}>
          <c-dx-field if:true={isPxHidden} field={groups.field} groups={customComponent} debug={debug}></c-dx-field>
        </template>
      </div>
    </div>
  </template>
</template>