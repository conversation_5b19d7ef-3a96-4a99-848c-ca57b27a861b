import { LightningElement, track, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import IpInvocationDetailModal from 'c/ipInvocationDetailModal';
import IpSaveLabelModal from 'c/ipSaveLabelModal';
import IPUtils from 'c/ipUtils';
import { upsertItem } from 'c/uuLocalStorageUtils';

export default class IPInvocationHistory extends LightningElement {
  @track invocationHistory = [];
  @track requestReadonly = true;
  @track editableRequest = '';
  columns = [
    { label: 'Data e ora', fieldName: 'formattedDate', type: 'text', wrapText: false },
    { label: 'Procedura', fieldName: 'procedureName', type: 'text', wrapText: true },
    { label: 'Stato', fieldName: 'status', type: 'text' },
    {
      type: 'action',
      typeAttributes: {
        rowActions: [
          { label: '<PERSON>i dettagli', name: 'open_details', iconName: 'utility:preview' },
          { label: 'Salva Request', name: 'save_request', iconName: 'utility:save' }
        ],
        menuAlignment: 'right'
      }
    }
  ];

  // Modals handled via LightningModal
  showInvocationDetailModal = false;
  showLabelModal = false;
  selectedInvocation;
  invocationToSave;
  labelInput = '';
  activeSections = [];
  historyStorageKey = 'iPUtilsDebuggerHistory';

  @api
  refresh() {
    this.invocationHistory = IPUtils.getInvocationHistory()
      .map((h) => ({ ...h, formattedDate: this.formatDate(h.timestamp) }))
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  get requestReadonlyLabel() {
    return this.requestReadonly ? 'Modifica Request' : 'Blocca Request';
  }

  connectedCallback() {
    this.refresh();
    this.activeSections = [];
  }

  clearInvocationHistory() {
    IPUtils.getInvocationHistory().length = 0;
    this.invocationHistory = [];
  }

  handleClose() {
    this.dispatchEvent(new CustomEvent('close'));
  }

  handleInvocationSelect(event) {
    const id = parseInt(event.currentTarget.dataset.id, 10);
    this.selectedInvocation = this.invocationHistory.find(
      (h) => h.timestamp === id
    );
    console.log('selectedInvocation', this.selectedInvocation);
    this.editableRequest = this.selectedInvocation
      ? JSON.stringify(this.selectedInvocation.request, null, 2)
      : '';
  this.openDetails(id);
  }

  handleRowAction(event) {
    const { action, row } = event.detail;
    if (!row) return;
    switch (action.name) {
      case 'open_details':
        this.openDetails(row.timestamp);
        break;
      case 'save_request':
        this.openSaveLabel(row.timestamp);
        break;
      default:
        break;
    }
  }

  openDetails(timestamp) {
    const id = parseInt(timestamp, 10);
  this.selectedInvocation = this.invocationHistory.find((h) => h.timestamp === id);
  this.editableRequest = this.selectedInvocation ? JSON.stringify(this.selectedInvocation.request, null, 2) : '';
  if (!this.selectedInvocation) return;
  IpInvocationDetailModal.open({ size: 'large', label: 'Dettagli Invocazione', invocation: this.selectedInvocation, timestamp: id }).then((result)=>{
    if (!result) return;
    switch (result.action) {
      case 'saverequest':
        this.openSaveLabel(result.timestamp);
        break;
      case 'copy':
        this.handleCopyMenuSelect({ detail: { value: result.value } });
        break;
      case 'invokeedited':
        // feed edited request into current context and invoke
        this.editableRequest = result.request;
        this.handleInvokeEditedRequest();
        break;
      default:
        break;
    }
  });
  }

  async openSaveLabel(timestamp) {
    const id = parseInt(timestamp, 10);
    this.invocationToSave = this.invocationHistory.find((h) => h.timestamp === id);
    if (!this.invocationToSave) return;
    this.labelInput = this.invocationToSave.procedureName;
    try {
      const res = await IpSaveLabelModal.open({ label: 'Salva Request', defaultLabel: this.labelInput });
      const newLabel = (res && typeof res.label === 'string') ? res.label : '';
      if (!newLabel) return; // cancelled or empty
      this.labelInput = newLabel;
      this.confirmSaveLabel();
      this.dispatchEvent(new ShowToastEvent({ title: 'Salvato', message: `Salvato come "${this.labelInput}"`, variant: 'success' }));
    } catch (e) {
      // ignore
    }
  }

  handleSaveRequest(event) {
    event.stopPropagation();
    const id = parseInt(event.currentTarget.dataset.id, 10);
    this.invocationToSave = this.invocationHistory.find((h) => h.timestamp === id);
    if (this.invocationToSave) {
      this.labelInput = this.invocationToSave.procedureName;
      this.showLabelModal = true;
    }
  }

  handleLabelChange(event) {
    this.labelInput = event.target.value;
  }

  confirmSaveLabel() {
    if (!this.invocationToSave) {
      return;
    }
    const item = {
      id: Date.now().toString(),
      name: this.invocationToSave.procedureName,
      request: JSON.stringify(this.invocationToSave.request, null, 2),
      response: JSON.stringify(this.invocationToSave.response, null, 2),
      label: this.labelInput || this.invocationToSave.procedureName,
      timestamp: Date.now(),
    };
    try {
      upsertItem(this.historyStorageKey, item, 'label');
    } catch (e) {
      // ignore storage errors
    }
    this.closeLabelModal();
  }

  handleRequestEdit(event) {
    this.editableRequest = event.target.value;
  }

  async handleInvokeEditedRequest() {
    if (!this.selectedInvocation) {
      return;
    }
    try {
      const req = this.editableRequest || JSON.stringify(this.selectedInvocation.request);
      const parsedRequest = JSON.parse(req);
      // Speed clone selectedInvocation
      const cloned = JSON.parse(JSON.stringify(this.selectedInvocation));
      const response = await IPUtils.invokeIntegrationProcedure(
        cloned.procedureName,
        { request: parsedRequest }
      );
      // Aggiorna la risposta solo sulla copia
      cloned.response = response;
      cloned.status = 'SUCCESS';
      cloned.request = parsedRequest;
      this.selectedInvocation = cloned;
      this.refresh();
    } catch (error) {
      // Aggiorna solo la copia in caso di errore
      const cloned = JSON.parse(JSON.stringify(this.selectedInvocation));
      cloned.response = error;
      cloned.status = 'ERROR';
      this.selectedInvocation = cloned;
    }
  }

  closeLabelModal() {
  this.showLabelModal = false;
  this.invocationToSave = undefined;
  this.labelInput = '';
  }

  closeInvocationDetail() {
  this.showInvocationDetailModal = false;
  this.selectedInvocation = undefined;
  }

  get formattedInvocationRequest() {
    if (!this.selectedInvocation) {
      return '';
    }
    try {
      return JSON.stringify(this.selectedInvocation.request, null, 2);
    } catch (e) {
      return String(this.selectedInvocation.request);
    }
  }

  get formattedInvocationResponse() {
    if (!this.selectedInvocation) {
      return '';
    }
    try {
      return JSON.stringify(this.selectedInvocation.response, null, 2);
    } catch (e) {
      return String(this.selectedInvocation.response);
    }
  }

  handleCopyInvocation() {
    if (!this.selectedInvocation) {
      return;
    }
    const payload = {
      request: this.selectedInvocation.request,
      response: this.selectedInvocation.response,
      methodName: this.selectedInvocation.procedureName,
    };
    const textToCopy = JSON.stringify(payload, null, 2);
  this.copyText(textToCopy);
  }

  handleSectionToggle(event) {
    this.activeSections = event.detail.openSections;
  }

  formatDate(ts) {
    const d = new Date(ts);
    const pad = (n) => n.toString().padStart(2, '0');
    return `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())} ${pad(
      d.getDate()
    )}/${pad(d.getMonth() + 1)}/${d.getFullYear()}`;
  }

  toggleRequestEdit() {
    this.requestReadonly = !this.requestReadonly;
  }

  // Helpers & new copy handlers
  copyText(textToCopy) {
    if (!textToCopy) return;
    try {
      navigator.clipboard.writeText(textToCopy);
    } catch (e) {
      const el = document.createElement('textarea');
      el.value = textToCopy;
      document.body.appendChild(el);
      el.select();
      document.execCommand('copy');
      document.body.removeChild(el);
    }
  }

  handleCopyRequest() {
    if (!this.selectedInvocation) return;
    const text = this.requestReadonly
      ? this.formattedInvocationRequest
      : this.editableRequest;
    this.copyText(text);
  }

  handleCopyResponse() {
    if (!this.selectedInvocation) return;
    const text = this.formattedInvocationResponse;
    this.copyText(text);
  }

  handleCopyMenuSelect(event) {
    const value = event.detail.value;
    switch (value) {
      case 'request':
        this.handleCopyRequest();
        break;
      case 'response':
        this.handleCopyResponse();
        break;
      case 'json':
        this.handleCopyInvocation();
        break;
      default:
        break;
    }
  }
}
