<template>
  <div class={containerClass}>
    <template if:true={renderGroup}>
      <template for:each={renderGroup} for:index="index" for:item="group">
        <div key={key}>
          <c-dx-groups groups={group}></c-dx-groups>
        </div>
      </template>
    </template>

    <template if:true={hasConvenzione}>
      <span class="UnicoPortezioneRibbon">
        <c-dx-custom-text-styles content={testoConvenzione} text-css={stileTestoConvenzione}></c-dx-custom-text-styles>
        <c-dx-custom-text-styles content={convenzione} text-css={stileConvenzione}></c-dx-custom-text-styles>
      </span>
    </template>
  </div>
</template>