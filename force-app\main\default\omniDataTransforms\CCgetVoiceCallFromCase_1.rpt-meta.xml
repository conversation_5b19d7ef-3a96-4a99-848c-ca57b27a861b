<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CCgetVoiceCallFromCase</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom1072</globalKey>
        <inputFieldName>Case:Esito__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>esito</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom7419</globalKey>
        <inputFieldName>VoiceCall:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>nameVoiceCall</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom4325</globalKey>
        <inputFieldName>VoiceCall:CallStartDateTime</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>dataOra</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom6420</globalKey>
        <inputFieldName>prodotto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>prodotto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>&quot;Unica&quot;</formulaConverted>
        <formulaExpression>&quot;Unica&quot;</formulaExpression>
        <formulaResultPath>prodotto</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>CCgetVoiceCallFromCaseCustom4806</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom5786</globalKey>
        <inputFieldName>Case:Priority</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>priorita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>CCgetVoiceCallFromCaseCustom8539</globalKey>
        <inputFieldName>RelatedCase__c</inputFieldName>
        <inputObjectName>VoiceCall</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>VoiceCall</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom209</globalKey>
        <inputFieldName>VoiceCall:CallType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipologia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom7999</globalKey>
        <inputFieldName>Case:CommercialAreasOfNeed__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ambito</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCgetVoiceCallFromCaseCustom3209</globalKey>
        <inputFieldName>VoiceCall:CallType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>modalita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>CCgetVoiceCallFromCaseCustom0jI9O000000wOhtUAEItem4</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Case</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Case</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:{Records}.length</formulaConverted>
        <formulaExpression>{Records}.length</formulaExpression>
        <formulaResultPath>recordLength</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>CCgetVoiceCallFromCaseCustom6014</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>CCgetVoiceCallFromCaseCustom6514</globalKey>
        <inputFieldName>RelatedRecordId</inputFieldName>
        <inputObjectName>VoiceCall</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetVoiceCallFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>VoiceCall</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;recordId&quot; : &quot;5009O00000c0YAHQA2&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>CCgetVoiceCallFromCase_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
