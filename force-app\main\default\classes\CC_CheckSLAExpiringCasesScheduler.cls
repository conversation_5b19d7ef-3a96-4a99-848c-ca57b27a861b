/*
 * Scheduling snippet (how to run this Batch regularly)
 *
 * Anonymous Apex (Developer Console ▶ Execute Anonymous):
 *   // Run every day at 00:00 (midnight)
 *   System.schedule(
 *       'CC_CheckSLAExpiringCases_Nightly',
 *       '0 0 0 * * ?',
 *       new CC_CheckSLAExpiringCasesScheduler()
 *   );
 *
 *   // Weekdays at 08:00
 *   System.schedule(
 *       'CC_CheckSLAExpiringCases_Weekdays8am',
 *       '0 0 8 ? * MON-FRI',
 *       new CC_CheckSLAExpiringCasesScheduler()
 *   );
 *
 * One‑off (schedule a single future execution of the Batch):
 *   // Start in 10 minutes, batch size 200
 *   System.scheduleBatch(new CC_CheckSLAExpiringCases(), 'CC_SLA_OneOff', 10, 200);
 *
 * Via UI (Setup):
 *   Setup → Apex Classes → Schedule Apex → Select 'CC_CheckSLAExpiringCasesScheduler'
 *   → Set frequency and time → Save.
 */
global with sharing class CC_CheckSLAExpiringCasesScheduler implements Schedulable {
    global void execute(SchedulableContext sc) {
        Database.executeBatch(new CC_CheckSLAExpiringCases(), 200);
    }

    // Helper to programmatically schedule the job at 00:00 every day
    public static void scheduleDailyMidnight() {
        String cron = '0 0 0 * * ?'; // at 00:00 every day
        System.schedule('CC_CheckSLAExpiringCases_Nightly', cron, new CC_CheckSLAExpiringCasesScheduler());
    }
}
