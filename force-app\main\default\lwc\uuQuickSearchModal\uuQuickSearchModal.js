import LightningModal from 'lightning/modal';
import { track, api } from 'lwc';

export default class UuQuickSearchModal extends LightningModal {
  @api items = [];
  @track query = '';
  @track highlightedIndex = 0;
  _focusTries = 0;

  get hasItems() {
    return this.filteredItems.length > 0;
  }

  get filteredItems() {
    const q = (this.query || '').trim().toLowerCase();
    if (!q) return this.items || [];
    return (this.items || []).filter((it) => {
      const label = (it.label || '').toLowerCase();
      const name = (it.name || '').toLowerCase();
      const desc = (it.description || '').toLowerCase();
      return label.includes(q) || name.includes(q) || desc.includes(q);
    });
  }

  get displayItems() {
    const list = this.filteredItems;
    const hi = this.highlightedIndex;
    return list.map((it, idx) => ({
      ...it,
      _idx: idx,
      cssClass: idx === hi ? 'qs-item active' : 'qs-item'
    }));
  }

  renderedCallback() {
    // autofocus search input on open
    if (!this._focusedOnce) {
      this._focusedOnce = true;
      const focusIt = () => {
        const el = this.template.querySelector('lightning-input[data-id="search"]');
        if (el && typeof el.focus === 'function') {
          try { el.focus(); } catch (e) { /* no-op */ }
        }
        // retry a few times in case modal steals initial focus
        if (this._focusTries < 6) {
          this._focusTries += 1;
          setTimeout(() => {
            try { el && typeof el.focus === 'function' && el.focus(); } catch (e) {}
          }, 40 * this._focusTries);
        }
      };
      // rAF first, then microtask fallback
      if (typeof requestAnimationFrame === 'function') {
        requestAnimationFrame(() => focusIt());
      } else {
        Promise.resolve().then(() => focusIt());
      }
    }
  }

  disconnectedCallback() {
    this._focusTries = 0;
  }

  handleInput = (evt) => {
    // support both lightning-input change event and native key events
    this.query = (evt.detail && evt.detail.value) || evt.target.value || '';
    // reset highlight when typing
    this.highlightedIndex = 0;
  };

  handleKeyup = (evt) => {
    // keep query in sync while typing
    this.query = evt.target && typeof evt.target.value === 'string' ? evt.target.value : this.query;
  };

  handleKeydown = (evt) => {
    const items = this.filteredItems;
    switch (evt.key) {
      case 'ArrowDown':
        evt.preventDefault();
        if (items.length) {
          this.highlightedIndex = (this.highlightedIndex + 1) % items.length;
        }
        break;
      case 'ArrowUp':
        evt.preventDefault();
        if (items.length) {
          this.highlightedIndex = (this.highlightedIndex - 1 + items.length) % items.length;
        }
        break;
      case 'Enter':
        evt.preventDefault();
        if (items.length) {
          this.select(items[this.highlightedIndex]);
        }
        break;
      case 'Escape':
        this.close();
        break;
      default:
        break;
    }
  };

  handleClickItem = (evt) => {
    const name = evt.currentTarget?.dataset?.name;
    const item = (this.items || []).find((i) => i.name === name);
    if (item) this.select(item);
  };

  select(item) {
    // Return minimal info; caller can look up details in its config
    this.close({ name: item.name });
  }

  handleClose = () => this.close();
}
