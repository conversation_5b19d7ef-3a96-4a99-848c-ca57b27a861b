import { errors } from 'c/dxErrorMessage';
import { getFormState, markFieldTouched, setFieldValue } from 'c/dxFormState';
import { fireEvent } from 'c/pubsub';
import { utils } from 'c/dxUtils';
import { LightningElement, api, track } from 'lwc';

export default class FieldPxCurrency extends LightningElement {
  _field;

  @track isValid = true;

  @api disabled;
  @api parentLayout;
  @api debug;
  @api decodedValue;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    setFieldValue(
      value.reference,
      value.value,
      value?.customAttributes?.validation || 'text',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get placeholder() {
    return parseInt(this.label) + this.field.control.modes[1].symbolValue;
  }

  get isReadonly() {
    return this.field?.readOnly === true;
  }

  get textAlign() {
    const alignment =
      this.field?.control?.modes?.find((mode) => mode.modeType === 'editable')?.textAlign ||
      'Right';

    return `input-field ${alignment}`.trim();
  }

  get minValue() {
    return this.field?.customAttributes?.minValue || '0';
  }

  get maxValue() {
    return this.field?.customAttributes?.maxValue || '999999999';
  }

  get componentClass() {
    return `pxCurrency ${this.debug ? 'debug' : ''}`.trim();
  }

  get errorMessage() {
    let message;

    if (!this.isValid && this.field.value === '') {
      message = errors['required'];
    }
    return message;
  }

  get symbolValue() {
    return (
      this.field?.control?.modes?.find((mode) => mode.modeType === 'readOnly')?.symbolValue ?? '€'
    );
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get value() {
    return `${this.decodedValue} ${this.symbolValue}`;
  }

  handleInputChange(evt) {
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('.input-field');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
      this.isValid = false;
      return;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }

    fireEvent('handleFieldChanged', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }
}
