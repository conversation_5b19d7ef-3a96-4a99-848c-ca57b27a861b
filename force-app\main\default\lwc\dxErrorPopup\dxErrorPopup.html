<template>
  <div class="modale-errore-container">
    <div class="modale-errore-view">
      <template if:true={isErroreGenrico}>
        <div class="modale-errore-content-container">
          <img src={modalErrorIcon} alt="" class="modal-icon">
          <span class="modale-errore-titolo">Attenzione</span>
          <span class="modale-errore-contenuto">{popupErroreAutoUsataContenuto.contenuto}</span>
          <span class="modale-errore-button" onclick={closePopup}>Riprova</span>
        </div>
      </template>

      <template if:true={isErroreAutoUsata}>
        <div class="modale-errore-content-container">
          <img src={modalErrorIcon} alt="" class="modal-icon">
          <span class="modale-errore-titolo">C'é qualcosa che non va</span>
          <span class="modale-errore-contenuto">
            <span>
              Targa: <b>{popupErroreAutoUsataContenuto.targa}</b>
              <br>
              Data di Nascita: <b>{popupErroreAutoUsataContenuto.dataDiNascita}</b>
            </span>
            <template for:each={popupErroreAutoUsataContenuto.contenuto} for:item="paragraph">
              <span key={paragraph}>{paragraph}<br></span>
            </template>
          </span>

          <div class="container-pulsanti">
            <span class="modale-errore-button negativo" onclick={closePopup}>Modifica i dati</span>
            <span class="modale-errore-button" onclick={loadNewPage}>Continua</span>
          </div>
        </div>
      </template>

      <template if:true={isErroreAutoUsataInd}>
        <div class="modale-errore-content-container">
          <img src={modalErrorIcon} alt="" class="modal-icon">
          <span class="modale-errore-titolo">Attenzione</span>
          <span class="modale-errore-contenuto">
            <template for:each={popupErroreAutoUsataContenuto.contenuto} for:item="paragraph">
              <span key={paragraph}>{paragraph}<br></span>
            </template>
          </span>

          <div class="container-pulsanti">
            <span class="modale-errore-button negativo" onclick={closePopup}>Modifica i dati</span>
            <span class="modale-errore-button" onclick={loadNewPage}>Continua</span>
          </div>
        </div>
      </template>

      <template if:true={isErroreTargaFittizia}>
        <div class="modale-errore-content-container">
          <img src={modalErrorIcon} alt="" class="modal-icon">
          <span class="modale-errore-titolo">Attenzione</span>
          <span class="modale-errore-contenuto">{popupErroreAutoUsataContenuto.contenuto}</span>
          <div class="container-pulsanti">
            <span class="modale-errore-button negativo" onclick={closePopup}>Modifica i dati</span>
            <span class="modale-errore-button" onclick={loadNewPage}>Continua</span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>