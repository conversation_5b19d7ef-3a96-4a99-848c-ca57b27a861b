import { LightningElement, api } from 'lwc';

export default class UuUcaUsers extends LightningElement {
  @api users = [];

  selectedUserId;

  columns = [{ label: 'Group', fieldName: 'group' }];

  groupToCheck = '';
  groupCheckResult = null;

  connectedCallback() {
    if (this.users?.length) {
      this.selectedUserId = this.users[0].userId;
    }
  }

  get options() {
    return (this.users || []).map((user) => ({
      label: user.userId,
      value: user.userId,
    }));
  }

  handleChange(event) {
    this.selectedUserId = event.detail.value;
  }

  handleGroupInputChange(event) {
    this.groupToCheck = event.target.value;
    this.groupCheckResult = null;
  }

  handleGroupCheck() {
    this.groupCheckResult = this.isGroupPresent(this.groupToCheck);
  }

  get groups() {
    const selected = (this.users || []).find(
      (user) => user.userId === this.selectedUserId,
    );
    return selected ? selected.groups : [];
  }

  get hasGroups() {
    return this.groups && this.groups.length;
  }

  get groupsData() {
    return this.groups.map((group, index) => ({
      id: index,
      group,
    }));
  }

  get groupCheckMessage() {
    if (this.groupCheckResult === null) {
      return '';
    }
    return this.groupCheckResult ? 'Group is present' : 'Group not found';
  }

  @api isGroupPresent(groupName) {
    return this.groups.includes(groupName);
  }
}
