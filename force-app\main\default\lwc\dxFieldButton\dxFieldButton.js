import { isFormValid } from 'c/dxFormState';
import { ICON_MAPPER, ICON_MAPPER_URLS } from 'c/dxIconMapping';
import { utils } from 'c/dxUtils';
import { utilsPega } from 'c/dxUtilsPega';
import { utilsPegaText } from 'c/dxUtilsPegaText';
import { fireEvent, unregisterListener } from 'c/pubsub';
import { LightningElement, api } from 'lwc';

export default class FieldButton extends LightningElement {
  @api decodedValue;
  @api disabled;

  labelFormatCss;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.labelFormatCss = utilsPegaText.getTextCss(this.field.labelFormat);
  }

  get label() {
    return this.field.control?.label ? utils.decodeHTML(this.field.control?.label) : '';
  }

  get buttonControlClass() {
    let formatClass = '';
    if (this.field?.control?.modes) {
      this.field.control.modes.forEach((mode) => {
        if (mode && mode.controlFormat) formatClass = utils.getClassFromFormat(mode.controlFormat);
      });
    }
    return formatClass;
  }

  get isDocumentationButton() {
    return this.buttonControlClass === 'DocumentationButton';
  }

  get isBackButton() {
    return this.buttonControlClass === 'Back';
  }

  get composedStyle() {
    const styleObj = {};

    // Icona
    const iconKey = this.field?.customAttributes?.buttonIcons;
    if (iconKey) {
      const iconName = ICON_MAPPER[ iconKey ];
      const iconUrl = ICON_MAPPER_URLS[ iconName ];
      if (iconUrl) {
        styleObj[ '--after-icon' ] = `url('${iconUrl}')`;
      }
    }

    // Inverted layout
    if (this.field?.customAttributes?.invertedIconPosition) {
      Object.assign(styleObj, {
        display: 'flex',
        width: '100%',
        'flex-direction': 'row-reverse',
        'align-items': 'center',
        'justify-content': 'center',
      });
    }

    return utils.getStyleStringFromObj(styleObj);
  }

  get customAttributes() {
    const linkType = utilsPega.customAttributes.getLinkType(this.field.customAttributes);

    return {
      linkType,
      link: this.field.customAttributes?.link,
      target: this.field.customAttributes?.Target ? this.field.customAttributes?.Target : '_blank',
      submitButton: this.field.customAttributes?.submitButton === 'true',
    };
  }

  handleClick = (evt) => {
    const { linkType, link, target, submitButton } = this.customAttributes;

    console.log('button click');

    if (linkType) {
      switch (linkType) {
        case '_URL':
        case 'Pdf':
          if (link) {
            window.open(link, target);
          }
          break;
        case 'GO_TO_HOME':
          window.open('/');
          break;
        case 'EXIT_FLOW':
          window.open('/');
          break;
        case 'GO_TO_LOGIN':
          window.open('/accesso');
          break;
        case 'GO_TO_QUOTATION_HOME':
          break;
        case 'GO_TO_REGISTRATION':
          window.open('/registrazione-cliente?uri_from=accesso');
          break;
        case 'GO_TO_APP':
          window.open('/app');
          break;
        case 'GO_TO_NEW_QUOTATION': {

          break;
        }
        default:
          if (link) {
            window.open(link, target);
          }
      }

      return;
    }


    if (submitButton) {
      const checkForm = isFormValid();
      if (checkForm.isValid) {
        // do something
      } else {
        fireEvent('triggerValidation');

        window.scrollTo({ top: 0, behavior: 'smooth' });
        return;
      }
    }

    if (
      evt.preventDefault &&
      this.field.control.actionSets &&
      this.field.control.actionSets.length > 0
    ) {
      evt.preventDefault();
    }

    console.log('fireEvent', evt, this.field, this.parentLayout);

    fireEvent('handleFieldClicked', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  };

  disconnectedCallback() {
    unregisterListener('fieldClickedCompleted', this.handleFieldClickedComplete, this);
  }
}
