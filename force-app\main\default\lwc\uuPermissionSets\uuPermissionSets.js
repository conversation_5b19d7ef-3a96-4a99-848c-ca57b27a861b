import { LightningElement, api } from 'lwc';

export default class UuPermissionSets extends LightningElement {
  @api permissionSets = [];

  columns = [{ label: 'Permission Set', fieldName: 'name' }];

  get hasPermissionSets() {
    return this.permissionSets && this.permissionSets.length;
  }

  get permissionSetData() {
    return (this.permissionSets || []).map((name, index) => ({
      id: index,
      name,
    }));
  }
}
