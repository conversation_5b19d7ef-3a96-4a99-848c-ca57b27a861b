import { LightningElement, api } from 'lwc';
import { utils } from "c/dxUtils";


export default class BoxIndirizzo extends LightningElement {
    _field;
    _groups;

    iconBox;
    customAttributes = {};
    address = '';

    @api
    set field(value) {
        this._field = value;
        this.customAttributes = this._field?.customAttributes || {};
        this.address = utils.decodeHTML(this.customAttributes.address || '');
    }

    get field() {
        return this._field;
    }

    get isRequired() {
        return this.field?.customAttributes?.required === true;
    }

    get icon() {
        return utils.getFirstFieldInGroupsByType(this._groups, 'pxIcon');
    }

    @api
    set groups(value) {
        this._groups = value;
        if (this._groups) {
            const firstIcon = utils.getFirstFieldInGroupsByType(this._groups, 'pxIcon', true);
            if (firstIcon) {
                this.iconBox = firstIcon;
            }
        }

    }

    get groups() {
        return this._groups;
    }

    
}