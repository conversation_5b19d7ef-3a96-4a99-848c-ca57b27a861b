<template>
    <div class="CardGaranzieHeaderContainer">
      <input class="visibleMobile" type="hidden" />
  
      <span class="policyNameLabel">{policyName}</span>
  
  <c-dx-field field={infoIcon}></c-dx-field>
  
      <div class="RightSideContainer">

        <template if:true={isRequired}>
          <span class="requiredLabel">{requiredLabel}</span>
        </template>
        <template if:true={showCheckbox}>
          <c-dx-field field={checkbox}></c-dx-field>
        </template>
        <template if:true={showLockIcon}>
          <c-dx-field field={lockIcon}></c-dx-field>
        </template>
      </div>
    </div>
    <div class="leftSideContainer">
      <span class="captionTextLabel">{captionText}</span>
    </div>
  </template>