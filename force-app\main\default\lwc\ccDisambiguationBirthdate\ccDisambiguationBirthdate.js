import CcDisambiguationCoreChild from "c/ccDisambiguationCoreChild";

export default class CcDisambiguationBirthdate extends CcDisambiguationCoreChild {
  get overrideData() {
    return {
      ...this.formData,
      birthdate: {
        ...this.formData.birthdate,
        validation: (event) => {
          const value = event.target.value;
          console.log("Birthdate validation triggered:", { value });
          if (!value) {
            event.target.setCustomValidity(
              "Data di nascita non può essere vuota."
            );
          } else if (new Date(value) > new Date()) {
            event.target.setCustomValidity(
              "La data di nascita non può essere futura."
            );
          } else {
            //la data inserita deve essere di un utente almeno maggiorenne
            const today = new Date();
            const birthDate = new Date(value);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();

            if (age < 18 || (age === 18 && monthDiff < 0)) {
              event.target.setCustomValidity(
                "L'utente deve essere maggiorenne."
              );
            } else {
              event.target.setCustomValidity(""); // Clear custom validity if valid
              this.handleChange(event);
            }
          }
        },
      },
    };
  }
}
