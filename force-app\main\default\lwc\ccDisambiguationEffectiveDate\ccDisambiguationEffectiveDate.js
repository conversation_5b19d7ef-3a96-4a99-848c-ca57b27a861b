import CcDisambiguationCoreChild from "c/ccDisambiguationCoreChild";

export default class CcDisambiguationEffectiveDate extends CcDisambiguationCoreChild {
  get overrideData() {
    return {
      ...this.formData,
      effectiveDate: {
        ...this.formData.effectiveDate,
        validation: (event) => {
          const value = event.target.value;
          if (!value) {
            event.target.setCustomValidity(
              "Data Decorrenza non può essere vuota."
            );
          } else if (new Date(value) < new Date()) {
            event.target.setCustomValidity(
              "La data effetto non può essere precedente alla data odierna"
            );
          } else {
            event.target.setCustomValidity("");
            this.handleChange(event);
          }
        },
      },
    };
  }
}
