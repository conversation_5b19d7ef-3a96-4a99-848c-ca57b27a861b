import { LightningElement, api, track } from "lwc";
import IPUtils from "c/ipUtils";
import { getFocusedTabInfo, openSubtab } from "lightning/platformWorkspaceApi";

export default class CcCartWidget extends LightningElement {
  @api recordId = "5009O00000gYzgoQAC";
  @api fiscalCode = "****************";

  @track isLoading = true;

  // Internal state
  data; // es: { CarrelloAttivo: [ {...} ] }

  // Display config
  currency = "EUR";
  locale = "it-IT";

  get logs() {
    return [
      {
        title: "Cart",
        value: this.data,
      },
    ];
  }

  connectedCallback() {
    this.loadCart();
  }

  async loadCart() {
    try {
      this.isLoading = true;
      const codiceFiscale = this.fiscalCode;
      const response = await IPUtils.invokeIntegrationProcedure(
        "CC_DXCartActive",
        { request: { codiceFiscale } },
      );
      this.data = response || {};
    } catch (e) {
      // Non-blocking: keep widget empty on failure
      // eslint-disable-next-line no-console
      console.error("Errore caricamento carrello attivo:", e);
      this.data = {};
    } finally {
      this.isLoading = false;
    }
  }

  get _list() {
    if (this.data && Array.isArray(this.data.CarrelloAttivo))
      return this.data.CarrelloAttivo;
    return [];
  }

  get cartCount() {
    return this._list.length;
  }

  get hasCartItem() {
    return this.cartCount > 0;
  }

  get item() {
    return this._list[0] || null;
  }

  // Mappature campi payload
  get numeroPreventivo() {
    return this.item?.NumeroPreventivo || "";
  }

  get numeroItems() {
    return this.item?.NumeroItems ?? 0;
  }

  get caseId() {
    return this.item?.CaseID || "";
  }

  // Se nel payload fosse presente un totale (es. item.PremioTotale), verrà formattato
  get totaleFormatted() {
    const raw = this.item?.PremioTotale;
    if (raw == null) return null;
    try {
      return new Intl.NumberFormat(this.locale, {
        style: "currency",
        currency: this.currency,
        minimumFractionDigits: 2,
      }).format(Number(raw));
    } catch {
      return `${Number(raw).toFixed(2)} ${this.currency}`;
    }
  }

  async openDisambiguation(data){
    try {
      this.isLoading = true;
      const focused = await getFocusedTabInfo();
      const parentId = focused?.tabId;

      const pageRef = {
        type: 'standard__component',
        attributes: { componentName: 'c__ccDisambiguationRootWrapper' },
        state: { c__recordId: this.recordId }
      };

      console.log('componentName', this.componentName);
      console.log("recordId", this.recordId);
      console.log("parentId", parentId);

      await openSubtab(parentId, {
        recordId: this.recordId,
        pageReference: pageRef,
        focus: true
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Errore apertura subtab:', e);
    } finally {
      this.isLoading = false;
    }
  }

  // Eventi
  async handleGoToCart() {
    this.openDisambiguation();
  }

  async handleNew() {
    this.openDisambiguation();
  }

  handlePreventivoClick() {
    this.dispatchEvent(
      new CustomEvent("preventivoclick", {
        detail: {
          numeroPreventivo: this.numeroPreventivo,
          caseId: this.caseId,
        },
      }),
    );
  }
}
