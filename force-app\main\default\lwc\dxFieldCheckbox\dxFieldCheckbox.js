import { getFormState, setFieldValue } from 'c/dxFormState';
import { fireEvent } from 'c/pubsub';
import { utils } from 'c/dxUtils';
import { LightningElement, api, track } from 'lwc';

export default class FieldCheckbox extends LightningElement {
  _field;

  @track isValid = true;
  @track _checked = false;

  @api decodedValue;
  @api disabled;
  @api parentLayout;
  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this._checked = value.value === 'true';

    setFieldValue(
      value.reference,
      this._checked,
      'checkbox',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.control.label ? utils.decodeHTML(this.field.control.label) : '';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get readonly() {
    return this.field.readOnly === true;
  }

  get componentClass() {
    return `pxCheckbox ${this.debug ? 'debug' : ''}`.trim();
  }

  get isChecked() {
    return this._checked;
  }

  isCheckboxValid() {
    const state = getFormState();

    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    this.isValid = isRequired && isTouched && value === '' ? false : true;
  }

  get checkboxClass() {
    const classes = [
      'checkmark',
      this.isChecked ? 'checkmark-checked' : '',
      this.disabled ? 'disabled-checkmark' : '',
      this.disabled ? 'checkmark-disabled' : '',
      this.isValid ? '' : 'error-checkmark',
      this.field?.customAttributes?.style === 'Rounded' ? 'round' : '',
      this.field?.customAttributes?.style === 'Card' ? '' : '',
      this.field?.customAttributes?.ComponentPurpose === 'CheckPackage' ? 'round' : '',
    ];
    return classes.filter(Boolean).join(' ');
    // const classes = [
    //   'us-checkbox-checkmark-green',
    //   'us-checkbox-check',
    //   this.field?.customAttributes?.style === 'Rounded' ? 'round' : '',
    //   this.field?.customAttributes?.style === 'Card' ? '' : '',
    //   this.field?.customAttributes?.ComponentPurpose === 'CheckPackage' ? 'round' : '',
    // ];
    // return classes.filter(Boolean).join(' ');
  }

  get containerClass() {
    let classes = ['checkbox-container'];

    if (this.field?.customAttributes?.checkboxWithBorder === 'true') {
      classes.push('checkbox-with-border');
    }

    if (this.field?.customAttributes?.labelPositionWeb === 'left') {
      classes.push('position-left');
    }

    if (this.disabled) {
      classes.push('disabled');
    }

    return classes.join(' ');
  }

  handleInputChange(evt) {
    this._checked = !this._checked;

    // aggiorna il valore nel formState
    setFieldValue(
      this.field.reference,
      this._checked ? 'true' : 'false',
      'checkbox',
      this.field.required || this.field?.customAttributes?.required
    );
    fireEvent('handleFieldChanged', {
      evt: {
        ...evt,
        target: {
          ...evt.target,
          checked: this._checked,
        },
      },
      field: this.field,
      parentLayout: this.parentLayout,
    });

    this.dispatchEvent(new CustomEvent('checkboxchange', {
      detail: {
        checked: this._checked,
        field: this.field
      }
    }));
}

}
