import { LightningElement, api, track } from "lwc";

export default class UuJsonView extends LightningElement {
  // Mostra la CTA copia solo se readonly
  get showCopyBtn() {
    return !this.editable;
  }
  @api height; // e.g., '16rem', '240px'
  @api
  get editable() {
    return this._editable;
  }
  set editable(val) {
    const next = !!val;
    if (this._editable === next) return;
    this._editable = next;
    // Re-init editor if using external JSON formatter
    if (this.useJsonFormatter && this.JSONFormatterInstance) {
      this.JSONFormatterInstance.destroy?.();
      this.JSONFormatterInstance = null;
      this._rendered = false;
    }
    // Keep textarea content in sync when falling back
    if (!this.useJsonFormatter) {
      this.textValue = this.stringifySafe(this._value);
    }
  }

  @api
  set value(val) {
    this._value = val;
    if (this.useJsonFormatter && this.JSONFormatterInstance) {
      this.JSONFormatterInstance.update(val);
    }
    if (!this.useJsonFormatter) {
      this.textValue = this.stringifySafe(val);
    }
  }
  get value() {
    return this._value;
  }

  @track _value;
  @track _editable = false;
  @track textValue = "";
  @track parseError;

  @track JSONFormatter;
  JSONFormatterInstance;

  get formattedValue() {
    return JSON.stringify(this.value, null, 2);
  }

  get useJsonFormatter() {
    return !!this.JSONFormatter;
  }

  get containerStyle() {
    const h = this.height || '16rem';
    return `height:${h};width:100%;`;
  }

  connectedCallback() {
    // Subscribe to a shared JSON formatter provider if present in host app
    this.$?.subscribeOneShot(
      this.$?.EVENTS.JSON_FORMATTER,
      this.handleJsonFormatterEvent,
    );
    this.$?.sync({ target: this.$?.TARGETS.JSON_FORMATTER });
  }

  handleJsonFormatterEvent = ({ detail }) => {
    this.$?.log?.(detail);
    this.JSONFormatter = detail;
    // When formatter becomes available after initial render, force re-init
    if (this._rendered && this.JSONFormatter && !this.JSONFormatterInstance) {
      this._rendered = false;
    }
  };

  renderedCallback() {
    if (this._rendered || !this.useJsonFormatter) return;
    this._rendered = true;
    this.renderJson();
  }

  renderJson() {
    const root = this.template.querySelector(".editor-host");
    if (!root || !this.JSONFormatter) return;

    const options = {
      mode: this.editable ? "tree" : "view",
      navigationBar: true,
      mainMenuBar: false,
      statusBar: false,
      onError: (err) => console.error("jsoneditor error", err),
      onChange: () => {
        try {
          const updated = this.JSONFormatterInstance?.get?.();
          if (updated !== undefined) {
            this.dispatchChange(updated);
          }
        } catch (e) {
          // ignore parse errors from editor; external editor usually surfaces these in UI
        }
      },
    };

    this.JSONFormatterInstance = new this.JSONFormatter(root, options);
    this.JSONFormatterInstance.set(this.value);
  }

  handleTextareaInput = (event) => {
    const text = event.target.value;
    this.textValue = text;
    try {
      const parsed = JSON.parse(text);
      this.parseError = undefined;
      this.dispatchChange(parsed);
    } catch (e) {
      this.parseError = e?.message || "Invalid JSON";
    }
  };

  dispatchChange(value) {
    const evt = new CustomEvent("change", {
      detail: { value },
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.dispatchEvent(evt);
  }

  stringifySafe(val) {
    try {
      return JSON.stringify(val, null, 2);
    } catch (e) {
      return "";
    }
  }

  handleCopyClick = () => {
    // Copia il JSON formattato negli appunti
    const text = this.formattedValue;
    if (navigator && navigator.clipboard) {
      navigator.clipboard.writeText(text);
    } else {
      // Fallback per browser non compatibili
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand('copy');
      } catch (e) {}
      document.body.removeChild(textarea);
    }
  }
}
