<template>
  <div class="slds-m-around_medium section">
    <lightning-card title="Network Details">
      <div class="slds-p-around_medium">
        <template if:true={data}>
          <lightning-layout multiple-rows>
            <lightning-layout-item size="12" medium-device-size="4">
              <lightning-input label="User Code" value={data.userId} readonly></lightning-input>
            </lightning-layout-item>
            <lightning-layout-item size="12" medium-device-size="4">
              <lightning-input label="Society Id" value={data.societyId} readonly></lightning-input>
            </lightning-layout-item>
            <lightning-layout-item size="12" medium-device-size="4">
              <lightning-input label="Society Code" value={data.societyCode} readonly></lightning-input>
            </lightning-layout-item>
            <lightning-layout-item size="12" medium-device-size="4">
              <lightning-input label="Society" value={data.society} readonly></lightning-input>
            </lightning-layout-item>
          </lightning-layout>
          <div class="slds-m-top_medium section">
            <h3 class="slds-text-heading_small">Preferred Network</h3>
            <template if:true={preferredNetwork}>
              <lightning-layout multiple-rows>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Id" value={preferredNetwork.Id} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Name" value={preferredNetwork.Name} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Agency" value={preferredNetwork.Agency__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="External Id" value={preferredNetwork.ExternalId__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Fiscal Code" value={preferredNetwork.FiscalCode__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input type="checkbox" label="Is Active" checked={preferredNetwork.IsActive__c} disabled></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Network User" value={preferredNetwork.NetworkUser__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Permission Sets" value={preferredNetwork.PermissionSets__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Society" value={preferredNetwork.Society__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input type="checkbox" label="Preferred" checked={preferredNetwork.Preferred__c} disabled></lightning-input>
                </lightning-layout-item>
              </lightning-layout>
            </template>
          </div>
          <div class="slds-m-top_medium section">
            <h3 class="slds-text-heading_small">Networks</h3>
            <template if:true={data.networks}>
              <lightning-datatable
                key-field="Id"
                data={data.networks}
                columns={networkColumns}
              ></lightning-datatable>
            </template>
          </div>
          <div class="slds-m-top_medium section">
            <h3 class="slds-text-heading_small">Agency</h3>
            <template if:true={agency}>
              <lightning-layout multiple-rows>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Name" value={agency.Name} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="External Id" value={agency.ExternalId__c} readonly></lightning-input>
                </lightning-layout-item>
                <lightning-layout-item size="12" medium-device-size="4">
                  <lightning-input label="Agency Code" value={agency.AgencyCode__c} readonly></lightning-input>
                </lightning-layout-item>
              </lightning-layout>
            </template>
          </div>
          <template if:true={ucaUsers}>
            <div class="slds-m-top_medium section">
              <h3 class="slds-text-heading_small">UCA User</h3>
              <lightning-button-group>
                <lightning-button
                  label="Show UCA Users"
                  data-section="showUcaUsers"
                  onclick={toggleSection}
                ></lightning-button>
              </lightning-button-group>
              <template if:true={showUcaUsers}>
                <c-uu-uca-users users={ucaUsers}></c-uu-uca-users>
              </template>
            </div>
          </template>
        </template>
        <template if:false={data}>
          Nessun dato Network
        </template>
      </div>
    </lightning-card>
  </div>
</template>
