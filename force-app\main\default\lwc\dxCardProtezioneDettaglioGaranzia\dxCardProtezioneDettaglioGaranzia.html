<template>
  <div class="CardProtezioneDettaglioGaranziaContainer" class:selected={selected}>
    <template if:true={selected}>
      <span class="CardProtezioneCheck"></span>
    </template>

    <div class="CardProtezioneDettaglioGaranziaBody" class:selected={selected}>
      <div class="CardProtezioneDettaglioGaranziaContent">
        <span class="CardProtezioneDettaglioGaranziaHeader">
          <template if:true={nomeGaranzia}>
            <c-dx-field field={nomeGaranzia} field-type="caption"></c-dx-field>
          </template>
          <template if:true={tooltipIcon}>
            <template if:false={selected}>
              <c-dx-field-px-icon field={tooltipIcon}></c-dx-field-px-icon>
            </template>
          </template>
        </span>

        <template if:true={descrizioneGaranzia}>
          <template if:false={selected}>
            <c-dx-field field={descrizioneGaranzia} field-type="caption"></c-dx-field>
          </template>
        </template>

        <template if:true={descrizioneGaranziaSelezionata}>
          <template if:true={selected}>
            <c-dx-field field={descrizioneGaranziaSelezionata} field-type="caption"></c-dx-field>
          </template>
        </template>
      </div>

      <template if:true={checkboxGaranzia}>
        <template if:false={selected}>
          <c-dx-field-checkbox field={checkboxGaranzia}></c-dx-field-checkbox>
        </template>
      </template>

      <template if:true={selected}>
  <c-dx-custom-text-styles
          text-css={checkboxTextStyle}
          content={checkboxLabel}
  ></c-dx-custom-text-styles>
      </template> 
    </div>
  </div>
</template>