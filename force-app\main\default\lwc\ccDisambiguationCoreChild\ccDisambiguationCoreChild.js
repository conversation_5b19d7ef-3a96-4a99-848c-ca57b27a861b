import { LightningElement, api } from "lwc";

export default class CcDisambiguationCoreChild extends LightningElement {
  @api formData;

  handleChange(event) {
    const {
      name,
      value,
      dataset: { path },
    } = event.target;
    console.log("Child change:", { name, value, path });

    this.dispatchEvent(
      new CustomEvent("fieldchange", {
        detail: {
          field: name,
          value: value,
          path: path,
        },
      })
    );
  }
}
