<template>
  <lightning-card title="IP Log Debugger" class="slds-m-around_medium">
    <div class="slds-m-around_medium">
      <lightning-textarea
        label="Incolla qui il JSON del log dell'IP"
        value={inputText}
        onchange={handleInputChange}
      ></lightning-textarea>
      <lightning-button-group class="slds-m-top_small">
        <lightning-button label="Parse" variant="brand" onclick={parse}></lightning-button>
        <lightning-button label="Clear" variant="neutral" onclick={clearAll}></lightning-button>
      </lightning-button-group>
      <template if:true={errorMessage}>
        <div class="slds-text-color_error slds-m-top_small">{errorMessage}</div>
      </template>
    </div>

    <template if:true={hasParsed}>
      <div class="slds-m-around_medium">
        <h3 class="slds-text-heading_small slds-m-bottom_x-small">Riepilogo</h3>
        <div class="summary-grid slds-box slds-m-bottom_small">
          <div>
            <div class="label">Elapsed Actual</div>
            <div class="value">{summary.elapsedTimeActual}</div>
          </div>
          <div>
            <div class="label">Elapsed CPU</div>
            <div class="value">{summary.elapsedTimeCPU}</div>
          </div>
        </div>
        <lightning-button-group class="slds-m-bottom_small">
          <lightning-button label="Copy Request" data-target="request" onclick={copyText}></lightning-button>
          <lightning-button label="Copy Response" data-target="response" onclick={copyText}></lightning-button>
          <lightning-button label={summaryJsonButtonLabel} onclick={toggleSummaryJson}></lightning-button>
        </lightning-button-group>
        <template if:true={showSummaryJson}>
          <c-uu-json-view value={summary} height="12rem"></c-uu-json-view>
        </template>
      </div>

      <template if:true={hasSteps}>
        <div class="slds-m-around_medium">
          <h3 class="slds-text-heading_small slds-m-bottom_x-small">Sequenza di Esecuzione</h3>
          <lightning-accordion allow-multiple-sections-open active-section-name={activeStepSections}>
            <template for:each={steps} for:item="step">
              <lightning-accordion-section key={step.key} name={step.sectionName} label={step.label}>
                <div class={step.headerClass}>
                  <span class="pill">{step.type}</span>
                  <span class="pill">{step.statusLabel}</span>
                  <span class="pill">t={step.elapsedTime}ms</span>
                  <span class="pill">cpu={step.elapsedTimeCPU}ms</span>
                </div>
                <div class="slds-grid slds-wrap slds-gutters slds-m-top_small">
                  <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                    <div class="slds-text-title_caps slds-m-bottom_xx-small">Input</div>
                    <c-uu-json-view value={step.input} height="16rem"></c-uu-json-view>
                  </div>
                  <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                    <div class="slds-text-title_caps slds-m-bottom_xx-small">Output</div>
                    <c-uu-json-view value={step.output} height="16rem"></c-uu-json-view>
                  </div>
                </div>
                <template if:true={step.debug}>
                  <div class="slds-m-top_small">
                    <div class="slds-text-title_caps slds-m-bottom_xx-small">Debug</div>
                    <c-uu-json-view value={step.debug} height="12rem"></c-uu-json-view>
                  </div>
                </template>
              </lightning-accordion-section>
            </template>
          </lightning-accordion>
        </div>
      </template>

      <div class="slds-m-around_medium">
        <h3 class="slds-text-heading_small slds-m-bottom_x-small">Sezioni Raw</h3>
        <template for:each={rawSections} for:item="sec">
          <div key={sec.name} class="slds-m-bottom_small">
            <div class="slds-text-title_caps slds-m-bottom_xx-small">{sec.name}</div>
            <c-uu-json-view value={sec.value} height="12rem"></c-uu-json-view>
          </div>
        </template>
      </div>
    </template>
  </lightning-card>
</template>
