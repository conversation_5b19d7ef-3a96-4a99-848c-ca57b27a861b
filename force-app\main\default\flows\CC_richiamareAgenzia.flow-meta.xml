<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>cloneCase</name>
        <label>cloneCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>CC_cloneCaseCallMeBackAgenzia</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>getCloneActivity</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>caseId</name>
            <value>
                <elementReference>get_activity.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>CC_cloneCaseCallMeBackAgenzia</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Copy_1_of_cloneCase</name>
        <label>Copy 1 of cloneCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>CC_cloneCaseCallMeBackAgenzia</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Copy_1_of_getCloneActivity</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>caseId</name>
            <value>
                <elementReference>get_activity.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>CC_cloneCaseCallMeBackAgenzia</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>assign_case_clone</name>
        <label>assign case clone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCloneActivity.TimeSlot__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Fascia_Oraria</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.Activity_Notes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Da richiamare Agenzia</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.ParentId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_activity.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>New</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.ContactRequestStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Call me back da Contact Center</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCloneActivity.Type</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>CallMeBack</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>updateCloneCase</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_text_final</name>
        <label>assign text final</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>TextScreenFinal</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>La richiesta di ricontatto è stata associata correttamente alla trattativa</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>closeParentCase</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>change_negotiation_type</name>
        <label>change negotiation type</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_negotiation.ContactChannel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agenzia</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>update_opportunity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>closeParentCase</name>
        <label>closeParentCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_activity.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_activity.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Da richiamare Agenzia</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>parentActivity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_assign_case_clone</name>
        <label>Copy 1 of assign case clone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.TimeSlot__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Fascia_Oraria</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.Activity_Notes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.ParentId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_activity.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>New</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.ContactRequestStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Call me back da Contact Center</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Copy_1_of_getCloneActivity.Type</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>CallMeBack</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_updateCloneCase</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_assign_text_final</name>
        <label>Copy 2 of assign text final</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>TextScreenFinal</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>L&apos;attività {!get_activity.CaseNumber} è stata chiusa correttamente</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>closeParentCase</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>FasceOrariaMattina</name>
        <choiceText>09:00/12:00 - mattina</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>09-12</stringValue>
        </value>
    </choices>
    <choices>
        <name>FasciaOrariaPomeriggio</name>
        <choiceText>12:00/16:00 - pomeriggio</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>12-16</stringValue>
        </value>
    </choices>
    <choices>
        <name>FasciaOrarioSera</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;16:00/20:00 - sera&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>16-20</stringValue>
        </value>
    </choices>
    <decisions>
        <name>checkDecisionButton</name>
        <label>checkDecisionButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_cloneCase</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>isAnnulla</defaultConnectorLabel>
        <rules>
            <name>isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>buttonEsito.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>cloneCase</targetReference>
            </connector>
            <label>isConferma</label>
        </rules>
    </decisions>
    <description>Flow per la gestione della quick action richiamare agenzia</description>
    <environments>Default</environments>
    <interviewLabel>CC-richiamareAgenzia {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-richiamareAgenzia</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Copy_1_of_getCloneActivity</name>
        <label>Copy 1 of getCloneActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_assign_case_clone</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Copy_1_of_cloneCase.newCaseId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_activity</name>
        <label>get activity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_record_type_activity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_negotiation</name>
        <label>get negotiation</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Screen_seleziona_fascia_oraria</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_activity.Opportunity__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_record_type_activity</name>
        <label>get record type activity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_negotiation</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Call_Me_Back</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCloneActivity</name>
        <label>getCloneActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>assign_case_clone</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>cloneCase.newCaseId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Copy_1_of_updateCloneCase</name>
        <label>Copy 1 of updateCloneCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>assign_text_final</targetReference>
        </connector>
        <inputReference>Copy_1_of_getCloneActivity</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>parentActivity</name>
        <label>parentActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>change_negotiation_type</targetReference>
        </connector>
        <inputReference>get_activity</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>update_opportunity</name>
        <label>update opportunity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>screenAnnullaSuccess</targetReference>
        </connector>
        <inputReference>get_negotiation</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>updateCloneCase</name>
        <label>updateCloneCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Copy_2_of_assign_text_final</targetReference>
        </connector>
        <inputReference>getCloneActivity</inputReference>
    </recordUpdates>
    <screens>
        <name>screen_esito</name>
        <label>screen  scelta esito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkDecisionButton</targetReference>
        </connector>
        <fields>
            <name>textAggiungiEsito</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;em style=&quot;background-color: rgb(255, 255, 255); font-size: 16px; font-family: &amp;quot;Segoe UI VSS (Regular)&amp;quot;, &amp;quot;Segoe UI&amp;quot;, -apple-system, BlinkMacSystemFont, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Ubuntu, Arial, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;, &amp;quot;Segoe UI Symbol&amp;quot;; color: rgb(0, 0, 0);&quot;&gt;Vuoi aggiungerlo anche come esito finale della chiamata?&lt;/em&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>buttonEsito</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelPrevious</name>
                <value>
                    <stringValue>Indietro</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen_seleziona_fascia_oraria</name>
        <label>Screen seleziona fascia oraria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>screen_esito</targetReference>
        </connector>
        <fields>
            <name>Fascia_Oraria</name>
            <choiceReferences>FasceOrariaMattina</choiceReferences>
            <choiceReferences>FasciaOrariaPomeriggio</choiceReferences>
            <choiceReferences>FasciaOrarioSera</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Fascia Oraria</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Note</name>
            <defaultValue>
                <stringValue>{!get_activity.NotesRecontact__c}</stringValue>
            </defaultValue>
            <fieldText>Note</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>screenAnnullaSuccess</name>
        <label>screenAnnullaSuccess</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>textAnnullaCompleted</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;{!TextScreenFinal}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_activity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TextScreenFinal</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
