<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Copy_1_of_sendoEmailCC</name>
        <label>Copy 1 of sendoEmailCC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>CC_OptOutEmailHandler</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>screenConfermaRevoca</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>caseId</name>
            <value>
                <elementReference>get_Attivit.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>flowOrigin</name>
            <value>
                <stringValue>Revoca CC</stringValue>
            </value>
        </inputParameters>
        <nameSegment>CC_OptOutEmailHandler</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>sendoEmailCC</name>
        <label>sendoEmailCC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>CC_OptOutEmailHandler</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>screenRichiestaEsitata</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>caseId</name>
            <value>
                <elementReference>get_Attivit.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>flowOrigin</name>
            <value>
                <stringValue>Revoca CC</stringValue>
            </value>
        </inputParameters>
        <nameSegment>CC_OptOutEmailHandler</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>Copy_1_of_modifyEsitoAttivit_2</name>
        <label>Copy 1 of modifyEsitoAttività2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.RevocationCC_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_updateActivity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_modifyEsitoAttivit2</name>
        <label>modifyEsitoAttività2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>dependencyPicklistEsito.topValue</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Sottoesito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>dependencyPicklistEsito.middleValue</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.RevocationCC_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>updateActivity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>modifyEsitoAttivit</name>
        <label>modifyEsitoAttività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Non interessato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Sottoesito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Revoca Consensi CC</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>screenConferma</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>check_inviata_revoca</name>
        <label>check inviata revoca</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>revoca_Consenso_CC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>non inviata in precedenza</defaultConnectorLabel>
        <rules>
            <name>inviata_in_precedenza</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Attivit.RevocationCC_Sent__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>revoca_gi_inviata</targetReference>
            </connector>
            <label>inviata in precedenza</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkDecisionButton</name>
        <label>checkDecisionButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_modifyEsitoAttivit_2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>test.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>modifyEsitoAttivit</targetReference>
            </connector>
            <label>isConferma</label>
        </rules>
    </decisions>
    <description>Flow che attraverso un action manda la richiesta di revoca CC</description>
    <environments>Default</environments>
    <interviewLabel>CC- {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-Revoca Consensi CC</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>get_Attivit</name>
        <label>get Attività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_inviata_revoca</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Copy_1_of_updateActivity</name>
        <label>Copy 1 of updateActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Copy_1_of_sendoEmailCC</targetReference>
        </connector>
        <inputReference>get_Attivit</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>updateActivity</name>
        <label>updateActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>sendoEmailCC</targetReference>
        </connector>
        <inputReference>get_Attivit</inputReference>
    </recordUpdates>
    <screens>
        <name>revoca_Consenso_CC</name>
        <label>revoca Consenso CC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Revoca_Consenso_CC2</targetReference>
        </connector>
        <fields>
            <name>textInoltroRichiesta</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Sei sicuro di voler inoltrare la richiesta di &lt;em&gt;Revoca consensi CC all&apos;ufficio di Revoca Consenso ?&lt;/em&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Revoca_Consenso_CC2</name>
        <label>Revoca Consenso CC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkDecisionButton</targetReference>
        </connector>
        <fields>
            <name>textEsitoFinaleChiamata</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;em style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; font-family: &amp;quot;Segoe UI VSS (Regular)&amp;quot;, &amp;quot;Segoe UI&amp;quot;, -apple-system, BlinkMacSystemFont, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Ubuntu, Arial, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;, &amp;quot;Segoe UI Symbol&amp;quot;; color: rgb(0, 0, 0);&quot;&gt;Vuoi aggiungerlo anche come esito finale della chiamata?&lt;/em&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>test</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>renderNext</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelPrevious</name>
                <value>
                    <stringValue>Previous</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>revoca_gi_inviata</name>
        <label>revoca già inviata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>textrevocaeffettuata</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;La richiesta di Revoca è stata gia inviata sul attività &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!get_Attivit.CaseNumber}&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenConferma</name>
        <label>Esito Finale</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Copy_2_of_modifyEsitoAttivit2</targetReference>
        </connector>
        <fields>
            <name>EsitoFinale</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Conferma gli esiti per questa attività &lt;/span&gt;&lt;a href=&quot;/lightning/r/Case/{!get_Attivit.Id}/view&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;{!get_Attivit.CaseNumber}&lt;/a&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>dependencyPicklistEsito</name>
            <extensionName>flowruntime:dependentPicklists</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>dependencyWrapperApiName</name>
                <value>
                    <stringValue>case</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topPicklistApiName</name>
                <value>
                    <stringValue>Esito__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middlePicklistApiName</name>
                <value>
                    <stringValue>Sottoesito__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topLabel</name>
                <value>
                    <stringValue>Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleLabel</name>
                <value>
                    <stringValue>Sotto Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topValue</name>
                <value>
                    <elementReference>get_Attivit.Esito__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleValue</name>
                <value>
                    <elementReference>get_Attivit.Sottoesito__c</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenConfermaRevoca</name>
        <label>screenConfermaRevoca</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>screenRevocaConsensiSuccess</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;La richiesta è stata inoltrata correttamente&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenRichiestaEsitata</name>
        <label>screenRichiestaEsitata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>TextScreenSuccessRevoca</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;L’attività &quot;&lt;/span&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;{!get_Attivit.CaseNumber}&lt;/strong&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&quot; è stata chiusa correttamente&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_Attivit</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
