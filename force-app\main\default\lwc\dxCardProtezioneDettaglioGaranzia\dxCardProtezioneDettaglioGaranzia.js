import { LightningElement, api } from 'lwc';
import { utils } from "c/dxUtils";


export default class CardPotezioneDettaglioGaranzia extends LightningElement {
    _field;

    @api groups;

    @api
    set field(input) {
        if (input) {
            this._field = input;
        }
    }

    get field() {
        return this._field;
    }

    get nomeGaranzia() {
      const field = utils.getFirstCaptionByControlFormat(this.groups, 'NomeGaranzia');
      if (field?.control) {
        field.control.format = this.selected
          ? 'TEXT APP BDB16 WEB BDB20 BDB20 BDB16'
          : 'TEXT APP BDB16 WEB BDB16 BDB16 BDB13';
      }
      return field;
    }
  
    get descrizioneGaranzia() {
      const field = utils.getFirstCaptionByControlFormat(this.groups, 'DescrizioneGaranzia');
      if (field?.control) {
        field.control.format = 'TEXT APP BDB16 WEB BDL13 BDL13 BDL13';
      }
      return field;
    }
  
    get descrizioneGaranziaSelezionata() {
      const field = utils.getFirstCaptionByControlFormat(this.groups, 'DescrizioneGaranziaSelezionata');
      if (field?.control) {
        field.control.format = 'TEXT APP BDB16 WEB BDL13 BDL13 BDL13';
      }
      return field;
    }
  
    get checkboxGaranzia() {
      const field = utils.getFirstFieldInGroupsByType(this?.groups, 'pxCheckbox');
      if (field) {
        field.label = '';
      }
      return field;
    }
  
    get tooltipIcon() {
      const field = utils.getFirstFieldInGroupsByType(this.groups, 'pxIcon');
      if (field?.customAttributes) {
        field.customAttributes.resource = 'info';
        field.customAttributes.webResponsiveSize = '24L 24L 24L';
      }
      return field;
    }
  
    get checkboxLabel() {
      return this.checkboxGaranzia?.customAttributes?.removeCheckboxLabel ?? 'Rimuovi';
    }
  
    get checkboxTextStyle() {
      return getTextCss('TEXT APP BDB16 WEB BDBN14 BDBN14 BDBN14');
    }
  
    @api
    get selected() {
      return this.checkboxGaranzia?.value === 'true';
    }
  
    handleClickRimozione() {
      const checkboxCmp = this.template.querySelector('c-tpd-px-checkbox');
      if (checkboxCmp) {
        checkboxCmp.onClick(!this.selected);
      }
    }

   

}