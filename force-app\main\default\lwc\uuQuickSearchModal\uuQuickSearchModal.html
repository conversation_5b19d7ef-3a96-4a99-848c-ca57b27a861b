<template>
    <lightning-modal-header label="Quick Search"></lightning-modal-header>
    <lightning-modal-body onkeydown={handleKeydown}>
        <div class="qs">
            <lightning-input data-id="search" type="search" variant="label-hidden"
                placeholder="Cerca utility (⌘K / Ctrl+K)" value={query} onchange={handleInput} onkeyup={handleKeyup}
                onkeydown={handleKeydown}></lightning-input>
            <template if:true={hasItems}>
                <ul class="qs-list">
                    <template for:each={displayItems} for:item="item">
                        <li key={item.name} data-name={item.name} class={item.cssClass} onclick={handleClickItem}>
                            <span class="qs-label">{item.label}</span>
                            <span class="qs-name">({item.name})</span>
                        </li>
                    </template>
                </ul>
            </template>
            <template if:false={hasItems}>
                <div class="qs-empty">Nessun risultato</div>
            </template>
        </div>
    </lightning-modal-body>
    <lightning-modal-footer>
        <lightning-button label="Close" onclick={handleClose}></lightning-button>
    </lightning-modal-footer>
</template>