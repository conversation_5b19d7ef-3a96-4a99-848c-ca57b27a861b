import { LightningElement, api } from 'lwc';
import { utils } from "c/dxUtils";
import { utilsPegaText } from "c/dxUtilsPegaText";


export default class StickyFooter extends LightningElement {
    _field;

    @api groups;

    @api
    set field(input) {
        if (input) {
            this._field = input;
        }
    }

    get field() {
        return this._field;
    }

    footerLayout;
    footerPositiveButton;
    footerSecondaryButton;
    secondaryButtonVisible = false;

    footerIcon = [];
    footerOpened = [];
    footerButton = [];
    groupsFooter = [];

    showPlaceholder = false;
    _footerPlaceholderUpdateLoop = false;

    connectedCallback() {

        if (this.groups && this.groups.length) {
            this.groups.forEach(group => this.filterGroups(group));
        }

        if (this.showPlaceholder) {
            this.updateFooterPlaceholder();
        }
    }

    disconnectedCallback() {
        this._footerPlaceholderUpdateLoop = false;
    }

    filterGroups(group) {
        const field = group.field;
        const layout = group.layout;

        if (layout?.visible && layout.groupFormat === 'PriceContainer') {
            this.footerLayout = layout;
        } else if (field?.visible) {
            const componentID = field.customAttributes?.componentID;
            if (componentID === 'footerPrimaryCTA') {
                this.footerPositiveButton = field;
            } else if (componentID === 'footerSecondaryCTA') {
                this.secondaryButtonVisible = true;
                this.footerSecondaryButton = field;
            }
        }
    }

    get hasFooterGroups() {
        return this.footerLayout && this.footerLayout.groups;
      }

}