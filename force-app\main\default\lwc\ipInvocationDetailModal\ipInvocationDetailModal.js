import { api } from 'lwc';
import LightningModal from 'lightning/modal';

export default class IpInvocationDetailModal extends LightningModal {
  @api invocation;
  @api timestamp;

  requestReadonly = true;
  editableRequest = '';
  editableRequestObj;
  activeSections = [];

  connectedCallback() {
    this.activeSections = [];
    this.editableRequest = this.invocation ? JSON.stringify(this.invocation.request, null, 2) : '';
    this.editableRequestObj = this.invocation ? this.invocation.request : undefined;
  }

  get requestReadonlyLabel() {
    return this.requestReadonly ? 'Modifica Request' : 'Blocca Request';
  }

  get formattedInvocationRequest() {
    if (!this.invocation) return '';
    try { return JSON.stringify(this.invocation.request, null, 2); } catch { return String(this.invocation.request); }
  }
  get formattedInvocationResponse() {
    if (!this.invocation) return '';
    try { return JSON.stringify(this.invocation.response, null, 2); } catch { return String(this.invocation.response); }
  }

  // uuJsonView bindings
  get requestEditable() {
    return !this.requestReadonly;
  }
  get requestObject() {
    // when editing, prefer the in-progress object; otherwise show current invocation.request
    if (this.requestEditable && this.editableRequestObj !== undefined) return this.editableRequestObj;
    return this.invocation ? this.invocation.request : undefined;
  }
  get responseObject() {
    return this.invocation ? this.invocation.response : undefined;
  }

  handleSectionToggle = (event) => { this.activeSections = event.detail.openSections; }
  toggleRequestEdit = () => { this.requestReadonly = !this.requestReadonly; }
  handleRequestEdit = (event) => { this.editableRequest = event.target.value; }
  handleRequestJsonChange = (event) => {
    // Keep both object and string version in sync for compatibility with caller
    const val = event?.detail?.value;
    this.editableRequestObj = val;
    try {
      this.editableRequest = JSON.stringify(val, null, 2);
    } catch (e) {
      // If value cannot be stringified, leave previous string
    }
  }

  // In LightningModal, communicate to opener via this.close(payload)
  handleSaveRequest = () => { this.close({ action: 'saverequest', timestamp: this.timestamp }); }
  handleCopyMenuSelect = (event) => { this.close({ action: 'copy', value: event.detail.value }); }
  handleInvokeEditedRequest = () => { this.close({ action: 'invokeedited', request: this.editableRequest }); }

  handleClose = () => this.close();
}
