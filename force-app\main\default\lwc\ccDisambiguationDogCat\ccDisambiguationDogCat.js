import CcDisambiguationCore from "c/ccDisambiguationCore";

export default class CcDisambiguationDogCat extends CcDisambiguationCore {

  get formData() {
    return {
      tipology: {
        name: "tipology",
        value: "",
        label: "Tipologia di Animale",
        options: this.entityDomains?.TIPORISC || [],
        path: "Ambito.Bene.Pet.TipologiaAnimale",
      },
      age: {
        name: "age",
        value: "",
        label: "Età Animale",
        options: this.entityDomains?.ETA || [],
        path: "Ambito.Bene.Pet.Eta",
      },
      effectiveDate: CcDisambiguationCore.FIELD_EFFECTIVE_DATE,
    };
  }
}
