import { LightningElement, api, track, wire } from 'lwc';
import getOpportunityCoverages from '@salesforce/apex/CC_OpportunityCoverageController.getOpportunityCoverages';

export default class cC_quoteListDetail extends LightningElement {
    // @api recordId; 
    _recordId;
    @track data = [];
    @track error;
    @track expandedDetailData = [];
    @track documentUrl;
    @track showPreview = false;

    @api 
    get recordId () {
        return this._recordId;
    }

    set recordId(value) {
        this._recordId = value;
        if (value) {
            Promise.resolve().then(() => this.loadData());
        }
    }



    columns = [
        {
            type: 'button-icon',
            initialWidth: 50,
            typeAttributes: {
                iconName: { fieldName: 'expandoIcon' },
                variant: 'bare',
                alternativeText: 'Espandi dettagli',
                name: 'toggle_details',
            },
        },
        { label: 'Id Preventivo', fieldName: 'Id', type: 'text' },
        { label: 'Prodotto', fieldName: 'Quote__c', type: 'text' },
        { label: 'Ambito', fieldName: 'AreaOfNeed__c', type: 'text' },
        { label: 'Stato', fieldName: 'Quote__r.Status', type: 'text' },
        { label: 'Premio', fieldName: 'Amount__c', type: 'text' },
        { label: 'Step Digitale', fieldName: 'StageName__c', type: 'text' },
        { label: 'Data Creazione', fieldName: 'CreatedDate', type: 'date' },
        { label: 'Data Scadenza', fieldName: 'Quote__r.ExpirationDate', type: 'date' },
        {
            type: 'action',
            typeAttributes: {
                rowActions: [
                    { label: 'Visualizza PDF', name: 'pdf' },
                    { label: 'Aggiungi al carrello', name: 'cart' }
                ]
            }
        }
    ];

    dettaglioColumns = [
        { label: 'Bene Assicurato', fieldName: 'ProductOfInterest__c', type: 'text' },
        { label: 'Dettaglio', fieldName: 'Description__c', type: 'text' },
        { label: 'Frazionamento', fieldName: 'Fractionation__c', type: 'text' },
        { label: 'Convenzioni', fieldName: 'Conventions__c', type: 'text' },
        { label: 'Assicurato', fieldName: 'Assicurato__c', type: 'text' }  // Che campo è
    ];

    async connectedCallback() {
        console.log('Component connected with recordId: ', this.recordId);
        await this.loadData();
    }

    // @wire(getOpportunityCoverages, { caseId: '$recordId' })
    // wiredData({ error, data }) {
    async loadData() {
        console.log('recordId: ', this.recordId);
        let data = await getOpportunityCoverages({ caseId: this.recordId });

        if (data) {
            console.log('Dati coperture opportunità: ', JSON.stringify(data));
            this.data = data.map(rec => ({ 
                ...rec, 
                isExpanded: false,
                expandoIcon: 'utility:chevronright',
                
                dettaglioRecord: [
                    { 
                        ProductOfInterest__c: rec.ProductOfInterest__c,
                        Description__c: rec.Description__c,
                        Fractionation__c: rec.Fractionation__c,
                        Conventions__c: rec.Conventions__c,
                        Assicurato__c: rec.Assicurato__c 
                    }
                ]
            }));
            this.error = undefined;
        } else if (error) {
            this.error = error;
            this.data = [];
        }
    }

    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;

        if (actionName === 'toggle_details') {
            const rowIndex = this.data.findIndex(item => item.Id === row.Id);
            
            let updatedData = JSON.parse(JSON.stringify(this.data)); 
            updatedData[rowIndex].isExpanded = !updatedData[rowIndex].isExpanded;
            updatedData[rowIndex].expandoIcon = updatedData[rowIndex].isExpanded ? 'utility:chevrondown' : 'utility:chevronright';

            this.data = updatedData;

            if (this.data[rowIndex].isExpanded) {
                this.expandedDetailData = this.data[rowIndex].dettaglioRecord;
            } else {
                this.expandedDetailData = [];
            }
        } else if (actionName === 'pdf') {
            console.log('Visualizza PDF per ' + row.Id);
            this.documentUrl = row.Quote__r.DocumentURL__c;
            console.log('Quote : ', JSON.stringify(row.Quote__c));
            console.log('Quote test : ', JSON.stringify(row.Quote__r));
            console.log('url riga : ', row.Quote__r.DocumentURL__c);
            console.log('url : ', this.documentUrl);

            if (this.documentUrl) {
                this.showPreview = true;
            } else {
                console.error('URL del documento non trovato per il preventivo ' + row.Id);
            }
        } else if (actionName === 'cart') {
            console.log('Aggiungi al carrello ' + row.Id);
        }
    }

    closePDFPreview() {
        this.showPreview = false;
        this.documentUrl = null;
    }

}