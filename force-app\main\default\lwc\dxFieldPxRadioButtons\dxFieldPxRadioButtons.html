<template>
    <div class={componentClass}>
        <template if:true={debug}>
            <p class="temporaryLabel">RADIOBUTTON: {label}</p>
        </template>
        <template if:true={isFlatCardInverted}>
            <div class="FlatInvertedCardContainer">
                <template for:each={options} for:item="option">
                    <div key={option.value} class="FlatInvertedCard" onclick={handleInputChange}>
                        <label for={option.value}>
                            <div class="marker">
                                <input type="radio" id={option.reference} data-value={option.value}
                                    data-reference={field.reference} name={field.reference} value={option.value}
                                    checked={option.checked} disabled={disabled} required={required} />
                            </div>
                            {option.label}
                        </label>
                    </div>
                </template>
            </div>
        </template>

        <template if:true={isPackage}>
            <div class={labelFormatClass}></div>
            <div class="radio-layout">
                <template for:each={options} for:item="option">
                    <div key={option.value} class="package-radio-option" onclick={handleInputChange}>
                        <div class="radio-input-container">
                            <input type="radio" id={option.reference} data-reference={field.reference}
                                name={field.reference} value={option.value} checked={option.checked} disabled={disabled}
                                required={required} />
                        </div>
                        <label for={option.value}>
                            {option.label}
                        </label>
                    </div>
                </template>
            </div>
        </template>

        <template if:true={isGreenCheck}>
            <div class="GreenCheckRadioContainer">
                <template for:each={options} for:item="option">
                    <div key={option.value} class="GreenCheckRadioItem" onclick={handleInputChange}>
                        <label for={option.value}>
                            <input type="radio" id={option.reference} data-reference={field.reference}
                                name={field.reference} value={option.value} checked={option.checked} disabled={disabled}
                                required={required} />
                            <span class="GreenCheck"></span>
                            <span class="GreenCheckContentContainer">
                                <c-dx-custom-text-styles content={option.value}
                                    text-css={subtitleFormat}></c-dx-custom-text-styles>

                                <template if:true={greenCheckSubtitle}>
                                    <span class="GreenCheckContentContainer">
                                        <c-dx-custom-text-styles content={option.value}
                                            text-css={subtitleFormat}></c-dx-custom-text-styles>
                                    </span>
                                </template>
                            </span>
                        </label>
                    </div>
                </template>
            </div>
        </template>

        <template if:true={isRadioCard}>
            <div class="radio-card-container">
                <template for:each={options} for:item="option">
                    <div key={option.value} class="radio-card" onclick={handleInputChange}>
                        <label for={option.value}>
                            <input type="radio" id={option.reference} data-reference={field.reference}
                                name={field.reference} value={option.value} checked={option.checked} disabled={disabled}
                                required={required} />
                            {option.label}
                        </label>
                    </div>
                </template>
            </div>
        </template>

        <template if:true={isFlatCard}>
            <div class="FlatCardContainer">
                <template for:each={options} for:item="option">
                    <div key={option.value} class="flat-card" onclick={handleInputChange}>
                        <label for={option.value}>
                            <input type="radio" id={option.reference} data-reference={field.reference}
                                name={field.reference} value={option.value} checked={option.checked} disabled={disabled}
                                required={required} />
                            <div class="FlatCardContent">
                                <span>{option.label}</span>
                                <span if:true={option.subtitle}>{option.subtitle}</span>
                            </div>
                        </label>
                    </div>
                </template>
            </div>
        </template>

        <template if:true={isRadioCardFrazionamento}>
            <div class={containerClass}>
                <template for:each={options} for:item="option">
                    <div key={option.value} class="FlatCard" onclick={handleInputChange}>
                        <label for={option.value} style="margin-bottom: 0;">
                            <input type="radio" id={option.reference} data-reference={field.reference}
                                name={field.reference} value={option.value} checked={option.checked} disabled={disabled}
                                required={required} />
                            <div>
                                <template if:true={option.checked}> 
                                    <c-dx-custom-text-styles content={option.label} 
                                        text-css={titleFormatSelected}></c-dx-custom-text-styles> <!-- se selezionato -->
                                </template>
                                <template if:false={option.checked}> 
                                    <c-dx-custom-text-styles content={option.label} 
                                        text-css={titleFormat}></c-dx-custom-text-styles> <!-- se NON selezionato -->
                                </template>
                            
                                <template if:true={option.isMensile}>

                                    <div class="card-content-mensile">
                                        <c-dx-layout debug={debug} layout={layoutFrazionamentoMensile}></c-dx-layout>
                                    </div>
                                </template>

                                <template if:true={option.isAnnuale}>
                                    <template if:true={layoutFrazionamentoAnnuale}>
                                        <div class="card-content-annuale">
                                            <c-dx-layout debug={debug} layout={layoutFrazionamentoAnnuale}></c-dx-layout>
                                        </div>
                                    </template>
                                </template>
                            </div>
                            <div class="marker" style="align-self: flex-start; "></div>
                        </label>
                    </div>
                </template>
            </div>
        </template>
    </div>
</template>