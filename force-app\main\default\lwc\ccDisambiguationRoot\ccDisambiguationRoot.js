import { LightningElement, api, track } from "lwc";

import { buildPayload, writePath } from "./ccHelper.js";

import IPUtils from "c/ipUtils";
import { defaultViews } from "c/uuUtils";

import { registerListener, unregisterListener } from "c/pubsub";

import getCustomerByInput from "@salesforce/apex/CustomerUtils.getCustomerByInput";

export default class CcDisambiguationRoot extends LightningElement {
  @api DEM;
  @api debugActive = false;
  @api accountId;
  @api caseId;

  @track selectedEntityCode;
  @track showInterprete = false;
  @track entityOptions = [];
  @track formData = {};

  @track spinner = false;
  @track step = 0;

  @track showPopup = false;
  @track showFailurePage = false;
  @track modalErrorMessage = {};
  @track showMissingContextModal = false;
  @track missingParams = [];

  entityData = [];
  entityDomains = {};

  stepsView = {
    PUVIAGGI: 1,
  };

  mappingLabels = {
    Veicoli: "Veicoli",
    Mobilita: "Mobilità",
    Casa: "Casa",
    "Protezione Famiglia": "Famiglia",
    "Cane&Gatto": "Cane & Gatto",
    "Viaggi Temporaneo": "Viaggi",
    Salute: "Salute",
    Infortuni: "Infortuni",
  };

  // Show entity-specific sections only when an entity is selected
  get hasSelectedEntity() {
    return !!this.selectedEntityCode;
  }

  get defaultData() {
    return {
      Ambito: {
        Proprietario: {
          TipoPersona:
            this.customerContext?.account?.RecordType?.DeveloperName ===
            "PersonAccount"
              ? "PF"
              : "PG",
        },
      },
      Contraente: {
        Nome: this.customerContext?.contact?.FirstName || "",
        Cognome: this.customerContext?.contact?.LastName || "",
        CodiceFiscale: this.customerContext?.account?.ExternalId__c || "",
        Residenza: {
          Cap: this.customerContext?.account?.BillingPostalCode || "",
        },
        Contatti: {
          Email: this.customerContext?.account?.Email__c || "",
          Cellulare: this.customerContext?.account?.Phone || "",
        },
      },
    };
  }

  get show() {
    return this.entityOptions.reduce((acc, option) => {
      acc[ option.value ] = option.value === this.selectedEntityCode;
      return acc;
    }, {});
  }

  get steps() {
    return this.entityOptions.reduce((acc, option) => {
      const maxStep = this.stepsView[ option.value ] || 0;
      for (let i = 0; i <= maxStep; i++) {
        acc[ option.value ] = [ ...(acc[ option.value ] || []), "step" + (i + 1) ];
      }
      return acc;
    }, {});
  }

  get showSteps() {
    return (this.steps[ this.selectedEntityCode ] || []).reduce(
      (acc, step, stepIndex) => {
        acc[ step ] = this.step === stepIndex;
        return acc;
      },
      {},
    );
  }

  get popupMoreData() {
    return {
      targa: this.formData?.Ambito?.Bene?.Auto?.Targa,
      dataDiNascita: this.formData?.Ambito?.Proprietario?.DataDiNascita,
    };
  }

  get logs() {
    return [
      {
        title: "View",
        value: {
          step: this.step,
          showSteps: this.showSteps,
          show: this.show,
        },
      },
      {
        title: "Customer Context",
        value: this.customerContext,
      },
      {
        title: "Form Data",
        value: this.formData,
      },
      {
        title: "Payload",
        value: this.payload,
      },
    ];
  }

  get isProceedDisabled() {
    const code = this.selectedEntityCode;
    const d = this.formData || {};

    // Small helpers
    const has = (obj, pathArray) =>
      pathArray.reduce(
        (acc, key) =>
          acc && acc[ key ] !== undefined && acc[ key ] !== null
            ? acc[ key ]
            : undefined,
        obj,
      );
    const nonEmpty = (v) =>
      v !== undefined && v !== null && String(v).trim() !== "";
    const hasObjKeys = (o) =>
      o &&
      typeof o === "object" &&
      Object.values(o).some(
        (v) => v !== undefined && v !== null && String(v).trim() !== "",
      );

    switch (code) {
      case "PUVEICOLO": {
        const tipology = has(d, [ "Ambito", "Bene", "Auto", "TipoVeicolo" ]);
        const effectiveDate = has(d, [ "Ambito", "DataDecorrenza" ]);
        const birthdate = has(d, [ "Ambito", "Proprietario", "DataDiNascita" ]);
        const address = has(d, [ "Ambito", "Proprietario", "Residenza" ]);
        const plate = has(d, [ "Ambito", "Bene", "Auto", "Targa" ]);
        const notPlate = !!has(d, [ "Ambito", "Bene", "Auto", "SenzaTarga" ]);
        const plateValid =
          nonEmpty(plate) && /^[A-Z]{2}[0-9]{3}[A-Z]{2}$/.test(plate);
        const hasPlateInfo = plateValid || notPlate;
        return !(
          nonEmpty(tipology) &&
          nonEmpty(effectiveDate) &&
          nonEmpty(birthdate) &&
          (hasObjKeys(address) || nonEmpty(address)) &&
          hasPlateInfo
        );
      }
      case "PUCASA": {
        const tipology = has(d, [
          "Ambito",
          "Bene",
          "Casa",
          "TipologiaAbitazione",
        ]);
        const effectiveDate = has(d, [ "Ambito", "DataDecorrenza" ]);
        const address = has(d, [ "Ambito", "Bene", "Casa", "Indirizzo" ]);
        return !(
          nonEmpty(tipology) &&
          nonEmpty(effectiveDate) &&
          (hasObjKeys(address) || nonEmpty(address))
        );
      }
      case "PUPET": {
        const tipology = has(d, [ "Ambito", "Bene", "Pet", "TipologiaAnimale" ]);
        const age = has(d, [ "Ambito", "Bene", "Pet", "Eta" ]);
        const effectiveDate = has(d, [ "Ambito", "DataDecorrenza" ]);
        return !(
          nonEmpty(tipology) &&
          nonEmpty(age) &&
          nonEmpty(effectiveDate)
        );
      }
      case "PUVIAGGI": {
        if (this.step === 0) {
          const country = has(d, [
            "Ambito",
            "Bene",
            "Viaggio",
            "PaeseDestinazione",
          ]);
          return !nonEmpty(country);
        }
        const start = has(d, [
          "Ambito",
          "Bene",
          "Viaggio",
          "DataViaggioAndata",
        ]);
        const end = has(d, [ "Ambito", "Bene", "Viaggio", "DataViaggioRitorno" ]);
        const people = has(d, [
          "Ambito",
          "Bene",
          "Viaggio",
          "NumeroViaggiatori",
        ]);
        return !(nonEmpty(start) && nonEmpty(end) && nonEmpty(people));
      }
      default:
        // Unknown or unsupported entities: disable only if no product is chosen
        return !nonEmpty(code);
    }
  }

  get payload() {
    return {
      action: "CREATE",
      env: "UNICO",
      productType: this.selectedEntityCode,
      referencesToUpdate: buildPayload(this.formData),
      captchaToken:
        "03AFcWeA6_6qa-hQv_vwR40Ct0A0J9Nv6pktOWUNBI_fXgKMHJSmMkwXGrOJ43yFIIa2fhFcHt79T0TwqF2JT1YHZhH9-_uPGYnOg6Q2I5AmjnG1FlUfOS9ae6bn6yRchZbRgNBi0W6x_lHZm9Pnbmdfha9oUwQ99woFG7xb7GRU9CnezhNPpP7GZbh37mq77h3qvUnu9aqfnaNzMVVt5-PUuQWEWhUtpPI5u1TG05P1oTi91GmP3CTiR0dYCwyFTd7qhjQUK5dxlnMHB1k3Si5j4J1Qy54zVbCHqEqWhdwFF5YziK2p49PBIYPCcKErpBJMMuwtDbGuyK8yuhYSDqf_4f9vLYVZv8-KgAhubmqQZGXGNOeH7xYsku69oCOkJmmw20ncK3ghhPAqabYJUFfGdmQFYYwL4PojrcRvM5mWxBJXxi398EO4Wi9NkfWab8TvruumgGWNnlngi62XPLoZXATp-PSGJAhebg6cXjreMQRUYDUX1XueBs4gblqSBr0HWlNxgcu8BW1ytxXEwlrIL0Zpb9n3ZxErKCGbxLlrS4C3UObufZUMwOXBlJbK5NiNYsm78MR7OgR8as5cLiT-8WazXrOlgZ9_2KNfd7_hE3QP4NLqYZWgLTPQT4mbieJoVFLjVKIyT4JahvRZjvVPqPgLGFkbttp3JxSTf465oFKQUf6lXykQRRdr1thVBPVpS1CT_-jZ0Q9Xst8erh24opB3gou3fa7tjk8sboUNDgDFOqyReUXN08o1z7ciZIcxkOyAaQSiAuOIF9K_F8f39gozRlKSMp9PGkSDj917g__iq9s9-SA8XvrNuiFHXaDAgdpcxA6eMnSEf_2hbnFhrCgCdQP47mwmBqPerB2QNNDBhtmAmYqRn3zOVbBSVE2ZsTQ9bcDvVSXVEY--CmnWYjW9oZGYLxYK2_Zca3XmDQM4GSNXMKYWbExAxlbDuCZu6iJa7n9qhcd6V-IxQj34vgTJ93S0LxEAewMMDzYBDEK95ZBK7I2h3iPwY2l-nkMRJ7bAXHKVw7WsjZnEUjHVrWxqWPm-bO4aNRW9h1tmVQG5XP8hXl4juR1N1lBMsfBD8eNulY6-D5WRhN-55Gwcv_w9yUEnzA5KZg7ve0OKPKgvItcBze4g2-42DAXsMY2IglDT3nur5XaVL93pbRw64DwXOOWk5ZE-2jSJGOjVd6v_A5NPK_nZuCyS_P0DIhXket0e_-YkT6rpWsN1rPoZ5FnuprFNG6VLaO8HQcAtWU2WfwIp7C-qvzhNqpAt5Z3l7gtErY3fjAffUXeMFHxD9Vi92XU74mTlCAXuLu9O-NoYGIg3cYQlFAcU_32VVADREyUza8cdf6DlPqTf14wiE6gXhujVxHjfSn7ozjgYBAexQi17opre5a0nyDlvw5kwQNYIaSu9EcQQMNfUCUQQW-VpDSMP3EPM-7YvnWzTq3iDHjYEzCzDsSb3BSwGWFVaBOqXNV_oAZFMCu3T-Tm5bO7CjESjqtn825LxToJvtIrUCgnvavJO-PcJggyj4yRhErGswcELRfPlGsvd5AKXVRr2EZ05xS2mnVO-ghnFOm0f3T98VCOSC5aTalfAXH-K3QIjhtLIdGjCFRM9TKGB3PCxq7egpSIX5okUYT3PxWTcPhbmCyC5FNOXaMrCt8hqRkogg7vSDO",
    };
  }

  configDashboard = [
    ...defaultViews,
    {
      name: "showDisambiguation",
      label: "Disambiguation",
      type: "pubSub",
      pubSub: {
        action: "ccShowDisambiguation",
      },
    },
    {
      name: "pegaLoad",
      label: "Pega Load",
      type: "pubSub",
      pubSub: {
        action: "dxLoadPage",
      },
    },
    {
      name: "pegaReload",
      label: "Pega Reload",
      type: "pubSub",
      pubSub: {
        action: "dxReloadPage",
      },
    },
  ];

  connectedCallback() {
    // Check required context before loading
    this.evaluateMissingParams();
    if (!this.showMissingContextModal) {
      this.loadContext();
    }
    registerListener(
      "ccShowDisambiguation",
      this.handleShowDisambiguation,
      this,
    );
  }

  disconnectedCallback() {
    unregisterListener(
      "ccShowDisambiguation",
      this.handleShowDisambiguation,
      this,
    );
  }

  renderedCallback() {
    this.DEM?.sync({ data: { logs: this.logs, config: this.configDashboard } });
  }

  async loadContext() {
    try {
      this.spinner = true;

      this.customerContext = await getCustomerByInput({
        input: JSON.stringify({
          caseId: this.caseId,
          accountId: this.accountId,
        }),
      });

      this.serviceContext = await IPUtils.invokeIntegrationProcedure(
        "CC_DXAmbitiDiBisogno",
      );
      console.log("entity serviceContext:", { ...this.serviceContext });

      this.entityOptions = this.serviceContext.entities.map((entity) => ({
        label:
          this.mappingLabels[
            entity.entityKey.description.replace("Prodotto Unico - ", "")
          ],
        value: entity.entityKey.code,
      }));

      // Do not preselect any entity; wait for explicit user choice
      this.selectedEntityCode = null;

      console.log("entity code:", this.selectedEntityCode);
      console.log("entity options:", [ ...this.entityOptions ]);

      this.formData = { ...this.defaultData };

      // With no selected entity yet, domains will remain empty; will be set on change
      this.handleServiceData();
    } catch (error) {
      console.error("Error loading context:", error);
    } finally {
      this.spinner = false;
      console.log("Context loading completed.");
    }
  }

  handleShowDisambiguation() {
    this.showInterprete = !this.showInterprete;
  }

  handleServiceData() {
    if (!this.selectedEntityCode) {
      // No selection yet: clear data/domains
      this.entityData = null;
      this.entityDomains = {};
      console.log("entity data/domains cleared (no selection)");
      return;
    }

    this.entityData =
      this.serviceContext.entities.find(
        (entity) => entity.entityKey.code === this.selectedEntityCode,
      ) || null;

    console.log("entity data:", this.entityData);

    this.entityDomains = (this.entityData?.domains || []).reduce(
      (acc, domain) => {
        acc[ domain.code ] = domain.dropDownItems;
        return acc;
      },
      {},
    );
    console.log("entity domains:", this.entityDomains);
  }

  handleEntityChange(event) {
    this.selectedEntityCode = event.detail.value;
    console.log("Selected Entity Code:", this.selectedEntityCode);
    this.step = 0; // Reset step when entity changes
    this.showInterprete = false; // Reset interprete visibility
    this.formData = { ...this.defaultData };
    console.log("Form data reset:", this.formData);
    this.handleServiceData();
  }

  handleChildFieldChange(event) {
    const { field, value, path } = event.detail;
    console.log("Child field change:", { field, value, path });
    this.formData = { ...writePath(path, value, this.formData) };
  }

  hasNextStep() {
    return this.step < (this.stepsView[ this.selectedEntityCode ] ?? 0);
  }

  handleClick() {
    if (this.hasNextStep()) return this.step++;
    this.showInterprete = true;
  }

  handleNoResult(event) {
    this.showPopup = true;
    this.showInterprete = false;

    console.log("No result event:", event.detail);

    const separatorIndex = event.detail.indexOf("|");
    const type = event.detail.substring(0, separatorIndex).trim();
    const contenuto = event.detail.substring(separatorIndex + 1).trim();

    this.modalErrorMessage = { type, contenuto };
  }

  showError() {
    this.showPopup = false;
    this.showFailurePage = true;
  }

  closePopup() {
    this.showPopup = false;
  }

  evaluateMissingParams() {
    console.log("evaluateMissingParams (AND logic - at least one required):", this.accountId, this.caseId);
    const isEmpty = (v) => v === undefined || v === null || String(v).trim() === "";
    const accountEmpty = isEmpty(this.accountId);
    const caseEmpty = isEmpty(this.caseId);

    // New rule: validation passes if at least ONE of the two is populated.
    // We only show the modal (block) when BOTH are missing.
    if (accountEmpty && caseEmpty) {
      this.missingParams = ["Account", "Caso"]; // both required to proceed when neither provided
      this.showMissingContextModal = true;
    } else {
      // Even if one is missing we allow proceeding, so we don't surface partial missing list.
      this.missingParams = [];
      this.showMissingContextModal = false;
    }
  }

  closeMissingContextModal() {
    this.showMissingContextModal = false;
  }
}
