<template>
  <template if:true={isType4}>
    <div id="accordion4">
      <template if:true={headerGroups}>
        <div class="accordion d-flex space-between align-center accordion-4">
          <template for:each={headerGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group} group-class="mimicASentence"></c-dx-groups>
          </template>
          <i class={arrowIconClass} onclick={toggleVisibility}></i>
        </div>
      </template>
      <template if:true={contentGroups}>
        <div class="accordion">
          <template if:true={showContent}>
            <div class="accordion-4">
              <div class="content-4">
                <template for:each={contentGroups} for:index="index" for:item="group">
                  <c-dx-groups key={key} groups={group}></c-dx-groups>
                </template>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
  </template>

  <template if:true={isType7}>
    <div id="accordion7">
      <template if:true={headerGroups}>
        <div class="d-flex fd-column jsf-center accordion-7">
          <template for:each={headerGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group}></c-dx-groups>
          </template>
          <template if:true={ribbonGroups}>
            <template for:each={ribbonGroups} for:index="index" for:item="group">
              <c-dx-groups key={key} groups={group}></c-dx-groups>
            </template>
          </template>
        </div>
      </template>

      <template if:true={showContent}>
        <span class="accordion-7-separator"></span>
      </template>

      <template if:true={contentGroups}>
        <div class="accordion">
          <template if:true={showContent}>
            <div class="accordion-7-content">
              <div class="content-7">
                <template for:each={contentGroups} for:index="index" for:item="group">
                  <c-dx-groups key={key} groups={group}></c-dx-groups>
                </template>
              </div>
            </div>
          </template>
        </div>
      </template>

      <span class="accordion-7-separator"></span>
      <div class="d-flex jsf-center accordion-7-footer" onclick={toggleVisibility}>
        <div if:false={showContent} class="simple-white">
          <span class="button-text">Mostra dettagli</span>
        </div>
        <div if:true={showContent} class="simple-white">
          <span class="button-text">Nascondi dettagli</span>
        </div>
        <i class={arrowIconClass}></i>
      </div>
    </div>
  </template>

  <template if:true={isType8}>
    <div class={accordionTypeClass}>
      <template if:true={headerGroups}>
        <template for:each={headerGroups} for:index="index" for:item="group">
          <c-dx-groups key={key} groups={group}></c-dx-groups>
        </template>
      </template>
      <template if:true={contentGroups}>
        <div class="accordion">
          <div class="accordion-content" if:false={showContent}>
            <template for:each={contentGroups} for:item="group">
              <c-dx-groups key={key} groups={group}></c-dx-groups>
            </template>
          </div>
        </div>
      </template>
      <template if:true={footerGroups}>
        <div class="accordion">
          <c-dx-groups groups={footerGroups} if:true={showContent}></c-dx-groups>
        </div>
      </template>
      <template if:true={showExplodingCTA}>
        <span onclick={changeVisibility} class={accordionLabelClass}>
          {toggleLabel}
        </span>
      </template>
    </div>
  </template>

  <template if:true={isType12}>
    <div class="Accordion12">
      <template if:true={headerGroups}>
        <div class="accordionHead">
          <template for:each={headerGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group}></c-dx-groups>
          </template>
        </div>
      </template>
      <template if:true={showContent}>
        <template for:each={contentGroups} for:index="index" for:item="group">
          <c-dx-groups key={key} groups={group}></c-dx-groups>
        </template>
      </template>
      <template if:true={showFooter}>
        <div class="accordionFooter">
          <template for:each={footerGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group}></c-dx-groups>
          </template>
        </div>
      </template>
      <template if:true={showExplodingCTA}>
        <span class="arrowButton" onclick={toggleVisibility}>
          <span class="arrowButtonText">Vedi e personalizza</span>
          <i class={arrowIconClass}></i>
        </span>
      </template>
    </div>
  </template>

  <template if:true={isTelematica}>
    <div class="AccordionTelematica">
      <template if:true={headerGroups}>
        <div class="AccordionHead">
          <c-dx-groups groups={headerGroups}></c-dx-groups>
          <span class="IconContainer">
            <span if:false={showContent} class="PenIcon" onclick={toggleVisibility}></span>
            <template if:true={thrashIconTelematica}>
              <c-dx-field-px-icon field={thrashIconTelematica}></c-dx-field-px-icon>
            </template>
          </span>
        </div>
      </template>
      <template if:true={contentGroups}>
        <div class="AccordionContent" if:true={showContent}>
          <template for:each={contentGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group}></c-dx-groups>
          </template>
        </div>
      </template>
      <template if:true={footerGroups}>
        <template if:false={showContent} for:each={footerGroups} for:index="index" for:item="group">
          <c-dx-groups key={key} groups={group}></c-dx-groups>
        </template>
      </template>
    </div>
  </template>

  <template if:true={isDefault}>
    <div id="accordionDefault" class={defaultAccordionClass} style={paddingStyle}>
      <template if:true={headerGroups}>
        <div class="accordion d-flex space-between align-center accordion-header">
          <template for:each={headerGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group} group-class="mimicASentence-responsiveAlign"></c-dx-groups>
          </template>
          <template if:true={showAccordionArrow}>
            <i class={arrowIconClass} onclick={toggleVisibility}></i>
          </template>
        </div>
      </template>
      <template if:true={contentGroups}>
        <div class="accordion">
          <div class="accordion-content" if:true={showContent}>
            <template for:each={contentGroups} for:index="index" for:item="group">
              <c-dx-groups key={key} groups={group}></c-dx-groups>
            </template>
          </div>
        </div>
      </template>
      <template if:true={footerGroups}>
        <div class="accordion" id="footerAccordion">
          <template if:false={showContent} for:each={footerGroups} for:index="index" for:item="group">
            <c-dx-groups key={key} groups={group}></c-dx-groups>
          </template>
        </div>
      </template>
    </div>
  </template>
</template>