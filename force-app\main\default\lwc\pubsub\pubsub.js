const events = {}; // Oggetto per memorizzare i listener degli eventi

/**
 * Registra un listener per un evento.
 * @param {string} eventName - Nome dell'evento.
 * @param {function} callback - Funzione di callback.
 * @param {Object} thisArg - Contesto (this) da associare alla callback.
 */
const registerListener = (eventName, callback, thisArg) => {
  if (!events[eventName]) {
    events[eventName] = [];
  }
  events[eventName].push({ callback, thisArg });
};

/**
 * Deregistra un listener per un evento.
 * @param {string} eventName - Nome dell'evento.
 * @param {function} callback - Funzione di callback da rimuovere.
 * @param {Object} thisArg - Contesto associato alla callback.
 */
const unregisterListener = (eventName, callback, thisArg) => {
  if (events[eventName]) {
    events[eventName] = events[eventName].filter(
      (listener) => listener.callback !== callback || listener.thisArg !== thisArg
    );
  }
};

/**
 * Deregistra tutti i listener associati ad un determinato contesto.
 * @param {Object} thisArg - Contesto da rimuovere.
 */
const unregisterAllListeners = (thisArg) => {
  Object.keys(events).forEach((eventName) => {
    events[eventName] = events[eventName].filter((listener) => listener.thisArg !== thisArg);
  });
};

/**
 * Emette un evento con un payload.
 * @param {string} eventName - Nome dell'evento.
 * @param {any} payload - Dati da passare al listener.
 */
const fireEvent = (eventName, payload) => {
  if (events[eventName]) {
    events[eventName].forEach((listener) => {
      try {
        listener.callback.call(listener.thisArg, payload);
      } catch (error) {
        console.error(`Errore nel listener per ${eventName}:`, JSON.stringify(error));
      }
    });
  }
};

// Esportiamo le funzioni per l'uso nei componenti
export { fireEvent, registerListener, unregisterAllListeners, unregisterListener };