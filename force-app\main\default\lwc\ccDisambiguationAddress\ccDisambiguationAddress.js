import { track } from "lwc";

import CcDisambiguationCoreChild from "c/ccDisambiguationCoreChild";
import getPlacesAutocomplete from "@salesforce/apex/GoogleMapsProxy.getPlacesAutocomplete";
import getPlaceDetails from "@salesforce/apex/GoogleMapsProxy.getPlaceDetails";
import IPUtils from "c/ipUtils";

export default class CcDisambiguationAddress extends CcDisambiguationCoreChild {
  @track suggestions = [];
  @track spinner = false;
  @track manualMode = false;
  @track provinces = [];
  @track towns = [];
  // Typeahead state
  @track provinceSearch = ""; // what user types/shows in the Provincia input
  @track townSearch = ""; // what user types/shows in the Comune input
  @track provinceOpen = false; // controls visibility of province dropdown
  @track townOpen = false; // controls visibility of town dropdown
  maxSuggestions = 20;

  // Manual address model
  @track manualStreet = "";
  @track manualCivic = "";
  @track manualProvince = "";
  @track manualCity = "";
  @track manualCap = "";
  @track manualState = "Italia";

  stateOptions = [
    { label: "Italia", value: "Italia" },
    // { label: "San Marino", value: "San Marino" },
    // { label: "Città del Vaticano", value: "Città del Vaticano" },
    // { label: "Altri stati", value: "Altri stati" },
  ];

  get overrideData() {
    return {
      ...this.formData,
    };
  }

  // Computed options for the Provincia combobox based on CC_ListProvince response
  get provinceOptions() {
    return (this.provinces || [])
      .filter(
        (p) =>
          p?.codice &&
          p?.nome &&
          (p.active === "1" ||
            p.active === 1 ||
            p.active === true ||
            p.active === undefined),
      )
      .map((p) => ({
        label: `${p.nome} (${p.codice})`,
        value: p.codice,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }

  // Computed options for the Comune combobox based on CC_ListTown response
  get townOptions() {
    return (this.towns || [])
      .filter((c) => c?.nome)
      .map((c) => ({
        label: c.cap ? `${c.nome} (${c.cap})` : c.nome,
        value: c.nome,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }

  // Filtered suggestions for Provincia based on user input
  get provinceSuggestions() {
    const q = this._normalize(this.provinceSearch);
    const list = this.provinceOptions;
    if (!q) return list.slice(0, this.maxSuggestions);
    return list
      .filter(
        (opt) =>
          this._normalize(opt.label).includes(q) ||
          this._normalize(opt.value).includes(q),
      )
      .slice(0, this.maxSuggestions);
  }

  // Filtered suggestions for Comune based on user input
  get townSuggestions() {
    const q = this._normalize(this.townSearch);
    const list = this.townOptions;
    if (!q) return list.slice(0, this.maxSuggestions);
    return list
      .filter(
        (opt) =>
          this._normalize(opt.label).includes(q) ||
          this._normalize(opt.value).includes(q),
      )
      .slice(0, this.maxSuggestions);
  }

  // Disable comune select if province not selected or no towns loaded
  get isTownDisabled() {
    return !this.manualProvince || (this.townOptions?.length || 0) === 0;
  }

  // Disable town search box likewise
  get isTownSearchDisabled() {
    return this.isTownDisabled;
  }

  // Disable Conferma CTA until all manual fields are provided
  get isManualConfirmDisabled() {
    const vals = [
      this.manualStreet,
      this.manualCivic,
      this.manualProvince,
      this.manualCity,
      this.manualCap,
      this.manualState,
    ];
    return vals.some((v) => !v || String(v).trim() === "");
  }

  connectedCallback() {
    // Load provinces on component initialization
    this.loadProvinces();
  }

  async loadProvinces() {
    try {
      this.spinner = true;
      const response =
        await IPUtils.invokeIntegrationProcedure("CC_ListProvince");
      console.log("CC_ListProvince response:", response);
      // Use the contract provided: { esito: { stato }, province: [ { nome, codice, ... } ] }
      if (response?.esito?.stato !== "OK") {
        console.warn(
          "CC_ListProvince returned non-OK status:",
          response?.esito,
        );
      }
      this.provinces = Array.isArray(response?.province)
        ? response.province
        : [];
    } catch (error) {
      console.error("CC_ListProvince error:", error);
    } finally {
      this.spinner = false;
    }
  }

  async loadTownsForProvince(codiceProvincia) {
    if (!codiceProvincia) {
      this.towns = [];
      return;
    }
    try {
      this.spinner = true;
      const response = await IPUtils.invokeIntegrationProcedure("CC_ListTown", {
        request: { codiceProvincia },
      });
      console.log("CC_ListTown response:", response);
      if (response?.esito?.stato !== "OK") {
        console.warn("CC_ListTown returned non-OK status:", response?.esito);
      }
      this.towns = Array.isArray(response?.comuni) ? response.comuni : [];
      // If the user already typed something in comune, keep it visible to filter
      this.townOpen = true;
    } catch (error) {
      console.error("CC_ListTown error:", error);
      this.towns = [];
    } finally {
      this.spinner = false;
    }
  }

  handleAddressChange(event) {
    this.address = event.target.value;
    console.log("digit address", this.address);

    clearTimeout(this.debounceTimeout);
    this.debounceTimeout = setTimeout(() => {
      this.invokeGetPlacesAutocomplete(this.address);
    }, 200);
  }

  async invokeGetPlacesAutocomplete(input) {
    try {
      const resultJson = await getPlacesAutocomplete({ input });
      const result = JSON.parse(resultJson);
      console.log("invokeGetPlacesAutocomplete result:", result);

      const { predictions } = result;

      this.suggestions = predictions.map((prediction) => ({
        label: prediction.description,
        value: prediction.place_id,
      }));
      console.log("Suggestions:", this.suggestions);
    } catch (error) {
      console.error("Error fetching places autocomplete:", error);
    }
  }

  async invokeGetPlaceDetails(placeId) {
    try {
      this.spinner = true;
      const resultJson = await getPlaceDetails({ placeId });
      const { result } = JSON.parse(resultJson);
      console.log("invokeGetPlaceDetails result:", result);

      if (result && result.formatted_address) {
        this.address = result.formatted_address;
        console.log("Formatted address:", this.address);

        const addressFields = this.extractAddressFields(result);
        console.log("Extracted address fields:", addressFields);

        // Normalize also after Google selection (CC_Normalizza)
        try {
          // Ensure we have towns for the detected province from Google before trying to pick codice catastale
          const provCode = (addressFields.Provincia || "").toUpperCase();
          if (provCode) {
            await this.loadTownsForProvince(provCode);
            // avoid UI dropdown side-effect from loadTownsForProvince in this flow
            this.townOpen = false;
            this.spinner = true;
          }

          // Attempt to enrich with codiceBelfioreComune using a normalized name match
          const targetComune = this._normalize(addressFields.Comune || "");
          const selectedTown = (this.towns || []).find(
            (t) => this._normalize(t?.nome || "") === targetComune,
          );
          const codiceBelfioreComune = selectedTown?.codiceCatastale || null;

          const request = {
            civico: addressFields.NumeroCivico || "",
            codiceBelfioreComune,
            comune: (addressFields.Comune || "").toUpperCase(),
            indirizzo: addressFields.NomeStrada || "",
            siglaProvincia: (addressFields.Provincia || "").toUpperCase(),
          };

          const normalizzaResp = await IPUtils.invokeIntegrationProcedure(
            "CC_Normalizza",
            { request },
          );
          console.log(
            "CC_Normalizza (after Google selection) response:",
            normalizzaResp,
          );

          // Best-effort: reflect codiceBelfioreComune into outgoing value for consumers
          addressFields.CodiceCatastaleComune = codiceBelfioreComune || null;
        } catch (e) {
          console.error("CC_Normalizza (after Google selection) error:", e);
        }

        this.suggestions = [];

        this.dispatchEvent(
          new CustomEvent("fieldchange", {
            detail: {
              field: this.overrideData.address.name,
              value: addressFields,
              state: this.address,
              path: this.overrideData.address.path,
            },
          }),
        );
      }
    } catch (error) {
      console.error("Error fetching place details:", error);
    } finally {
      this.spinner = false;
    }
  }

  async handleAddressSelect(event) {
    const { placeId } = event.currentTarget.dataset;
    console.log("Selected address:", placeId);
    await this.invokeGetPlaceDetails(placeId);
  }

  extractAddressFields(placeDetails) {
    const components = placeDetails.address_components;
    const getComponent = (type) =>
      components.find((c) => c.types.includes(type));

    return {
      NomeStrada: getComponent("route")?.long_name || null,
      NumeroCivico: getComponent("street_number")?.long_name || null,
      Comune: getComponent("administrative_area_level_3")?.long_name || null,
      Provincia:
        getComponent("administrative_area_level_2")?.short_name || null,
      Cap: getComponent("postal_code")?.long_name || null,
      Stato: getComponent("country")?.long_name || null,
      CodiceCatastaleComune: null, // Non disponibile via Google API
    };
  }

  // Manual flow handlers
  enableManualMode() {
    // Clear suggestions and switch mode
    this.manualMode = true;
    this.suggestions = [];
    // reset typeahead state
    this.provinceSearch = "";
    this.townSearch = "";
    this.provinceOpen = false;
    this.townOpen = false;
  }

  handleManualChange(event) {
    // lightning-input uses event.target.value; lightning-combobox uses event.detail.value
    const name = event.target.name;
    const value = event.detail?.value ?? event.target.value;
    switch (name) {
      case "NomeStrada":
        this.manualStreet = value;
        break;
      case "NumeroCivico":
        this.manualCivic = value;
        break;
      case "Provincia":
        // This branch is kept for backward compatibility if a combobox is used.
        this._setProvinceByCode(value);
        break;
      case "Comune":
        // This branch is kept for backward compatibility if a combobox is used.
        this._setTownByName(value);
        break;
      case "Cap":
        this.manualCap = value;
        break;
      default:
        break;
    }
  }

  handleManualStateChange(event) {
    this.manualState = event.detail.value;
  }

  handleManualCancel() {
    // Reset manual fields and return to search mode
    this.manualStreet = "";
    this.manualCivic = "";
    this.manualProvince = "";
    this.manualCity = "";
    this.manualCap = "";
    this.manualState = "Italia";
    this.manualMode = false;
  }

  async handleManualConfirm() {
    // Prepare payload for CC_Normalizza
    const selectedTown = (this.towns || []).find(
      (t) => t?.nome === this.manualCity,
    );
    const codiceBelfioreComune =
      this.manualState === "Italia" && selectedTown?.codiceCatastale
        ? selectedTown.codiceCatastale
        : null;

    const request = {
      civico: this.manualCivic || "",
      codiceBelfioreComune, // valorizzato dal codiceCatastale del comune selezionato
      comune: (this.manualCity || "").toUpperCase(),
      indirizzo: this.manualStreet || "",
      siglaProvincia: (this.manualProvince || "").toUpperCase(),
    };

    try {
      this.spinner = true;
      const response = await IPUtils.invokeIntegrationProcedure(
        "CC_Normalizza",
        { request },
      );
      console.log("CC_Normalizza response:", response);
      // TODO: valutare se aggiornare i campi con l'indirizzo normalizzato da response
    } catch (error) {
      console.error("CC_Normalizza error:", error);
    } finally {
      this.spinner = false;
    }

    const addressFields = {
      NomeStrada: this.manualStreet || null,
      NumeroCivico: this.manualCivic || null,
      Comune: this.manualCity || null,
      Provincia: this.manualProvince || null,
      Cap: this.manualCap || null,
      Stato: this.manualState || null,
      CodiceCatastaleComune: codiceBelfioreComune || null,
    };

    const formatted = [
      [this.manualStreet, this.manualCivic].filter(Boolean).join(", "),
      [this.manualCap, this.manualCity, this.manualProvince]
        .filter(Boolean)
        .join(" "),
      this.manualState,
    ]
      .filter(Boolean)
      .join(" - ");

    this.dispatchEvent(
      new CustomEvent("fieldchange", {
        detail: {
          field: this.overrideData.address.name,
          value: addressFields,
          state: formatted,
          path: this.overrideData.address.path,
        },
      }),
    );

    this.manualMode = false;
  }

  // ============== Typeahead (Provincia/Comune) ==============
  handleProvinceSearchInput(event) {
    this.provinceSearch = event.target.value || "";
    this.provinceOpen = (this.provinceSuggestions?.length || 0) > 0;
  }

  handleProvinceFocus() {
    this.provinceOpen = (this.provinceSuggestions?.length || 0) > 0;
  }

  handleProvinceBlur() {
    // allow click to register before closing; slight delay
    setTimeout(() => (this.provinceOpen = false), 150);
  }

  handleProvinceSuggestionSelect(event) {
    const code = event.currentTarget?.dataset?.value;
    const label = event.currentTarget?.dataset?.label;
    if (!code) return;
    this._setProvinceByCode(code, label);
    this.provinceOpen = false;
  }

  _setProvinceByCode(code, labelFromItem) {
    this.manualProvince = code;
    // reflect label into the visible search input
    if (labelFromItem) this.provinceSearch = labelFromItem;
    else {
      const opt = this.provinceOptions.find((o) => o.value === code);
      this.provinceSearch = opt ? opt.label : code;
    }
    // reset dependent fields and load towns for selected province
    this.manualCity = "";
    this.townSearch = "";
    this.manualCap = "";
    this.towns = [];
    this.loadTownsForProvince(code);
  }

  handleTownSearchInput(event) {
    this.townSearch = event.target.value || "";
    this.townOpen = (this.townSuggestions?.length || 0) > 0;
  }

  handleTownFocus() {
    if (!this.isTownSearchDisabled) {
      this.townOpen = (this.townSuggestions?.length || 0) > 0;
    }
  }

  handleTownBlur() {
    setTimeout(() => (this.townOpen = false), 150);
  }

  handleTownSuggestionSelect(event) {
    const name = event.currentTarget?.dataset?.value;
    const label = event.currentTarget?.dataset?.label;
    if (!name) return;
    this._setTownByName(name, label);
    this.townOpen = false;
  }

  _setTownByName(name, labelFromItem) {
    this.manualCity = name;
    this.townSearch = labelFromItem || name;
    // Best-effort: auto-fill CAP if present in towns list
    try {
      const selected = (this.towns || []).find((t) => t?.nome === name);
      if (selected?.cap) {
        this.manualCap = selected.cap;
      }
    } catch (e) {
      // noop
    }
  }

  // Helpers
  _normalize(str) {
    if (!str) return "";
    try {
      return String(str)
        .toLowerCase()
        .normalize("NFD")
        .replace(/\p{Diacritic}/gu, "");
    } catch (e) {
      // Fallback without unicode property escapes
      return String(str)
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "");
    }
  }
}
