import { LightningElement,api,track } from 'lwc';
import { utils } from 'c/dxUtils';
export default class RadioCardFrazionamento extends LightningElement {
    radioButtonsFrazionamento;
    field;
    layoutFrazionamentoMensile;
    layoutFrazionamentoAnnuale;
    @track _customType;
  
    @api decodedValue;
    @api disabled;
    @api debug = false;;
    @api groups;

    get radioButtonFrazionamento() {
        return this.field;
    }

    get isDisabled() {
        return this.field.readOnly || this.field.disabled;
  }

    connectedCallback() {
        this.field = utils.getFirstFieldInGroupsByType(this.groups, "pxRadioButtons");
        this.layoutFrazionamentoMensile = utils.getFirstLayoutInGroupsByGroupFormat(this.groups, 'ContenutoFrazionamentoMensile');
        this.layoutFrazionamentoAnnuale = utils.getFirstLayoutInGroupsByGroupFormat(this.groups, 'ContenutoFrazionamentoAnnuale');
    }
}