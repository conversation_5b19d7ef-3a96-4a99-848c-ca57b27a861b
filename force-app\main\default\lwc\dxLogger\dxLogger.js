/*
 * Lightweight logger for LWC components with centralized gating.
 * Usage:
 *   import { createLogger } from 'c/dxLogger';
 *   const log = createLogger('dxInterprete', () => this.debug);
 *   log.debug('message'); log.warn('warn'); log.error('error');
 */

export const isGlobalDebug = () => {
  try {
    return window?.localStorage?.getItem('debug') === 'true';
  } catch (e) {
    return false;
  }
};

export const createLogger = (prefix = 'app', localDebugGetter) => {
  const isLocalDebugEnabled = () => {
    try {
      const local = typeof localDebugGetter === 'function' ? !!localDebugGetter() : false;
      return local || isGlobalDebug();
    } catch (e) {
      return isGlobalDebug();
    }
  };

  const tag = `[${prefix}]`;

  return {
    debug: (...args) => {
      if (isLocalDebugEnabled()) {
        // eslint-disable-next-line no-console
        console.log(tag, ...args);
      }
    },
    warn: (...args) => {
      if (isLocalDebugEnabled()) {
        // eslint-disable-next-line no-console
        console.warn(tag, ...args);
      }
    },
    error: (...args) => {
      // eslint-disable-next-line no-console
      console.error(tag, ...args);
    },
  };
};
