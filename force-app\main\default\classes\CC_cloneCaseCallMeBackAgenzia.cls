/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 08-08-2025
 * @last modified by  : <EMAIL>
**/
public with sharing class CC_cloneCaseCallMeBackAgenzia {
    
    public class CloneCaseRequest {
        @InvocableVariable(required=true)
        public Id caseId;
    }
    
    public class CloneCaseResponse {
        @InvocableVariable
        public Id newCaseId;
    }

    @InvocableMethod(label='Clone Case CC')
    public static List<CloneCaseResponse> cloneCase(List<CloneCaseRequest> requests) {
        List<CloneCaseResponse> results = new List<CloneCaseResponse>();

        List<String> fieldNames = new List<String>(Schema.SObjectType.Case.fields.getMap().keySet());
        String soqlFields = String.join(fieldNames, ',');

        for (CloneCaseRequest req : requests) {
            Case originalCase = (Case) Database.query(
                'SELECT ' + soqlFields + ' FROM Case WHERE Id = :caseId LIMIT 1'
                .replace(':caseId', '\'' + String.valueOf(req.caseId) + '\'')
            );

            Case clonedCase = originalCase.clone(false, true, false, false);
            Id defaultRtId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('Call Me Back').getRecordTypeId();
            clonedCase.RecordTypeId = defaultRtId;
            clonedCase.EmailStatus__c = null;
            clonedCase.sottoEsito__c = null;

            insert clonedCase;

            CloneCaseResponse res = new CloneCaseResponse();
            res.newCaseId = clonedCase.Id;
            results.add(res);
        }

        return results;
    }
}