<template>
    <template if:true={textCss}>
        <span class="visible-desktop" style={labelCssFormat.desktopCss}>
            {content}
        </span>
        <span class="visible-tablet" style={labelCssFormat.tabletCss}>
            {content}
        </span>
        <span class="visible-mobile" style={labelCssFormat.mobileCss}>
            {content}
        </span>
    </template>
    <template if:false={textCss}>
        <span>{content}</span>
    </template>
</template>