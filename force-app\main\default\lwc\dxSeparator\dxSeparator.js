import { LightningElement, api } from 'lwc';

export default class Separator extends LightningElement {
    @api customAttributes;

    // get separatorStyle() {
    //     const height = this.customAttributes?.size ? `${this.customAttributes.size}px` : '1px';
    //     const color = this.customAttributes?.separatorColor ? `var(--${this.customAttributes.separatorColor})` : 'var(--blue-primary)';
    //     const width = this.customAttributes?.maxWidth ? `${this.customAttributes.maxWidth}px` : '1px';
    //     return `height: ${height}; border-left: ${width} solid ${color}; opacity: 0.5;`;
    // }
}