import { LightningElement, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getCustomerByAccountId from '@salesforce/apex/CustomerUtils.getCustomerByAccountId';
import { loadItems, upsertItem } from 'c/uuLocalStorageUtils';
import UuCustomerViewDetailModal from 'c/uuCustomerViewDetailModal';

export default class UuCustomerView extends LightningElement {
    @track accountId = '';
    @track loading = false;
    @track context = null;
    @track error = null;

    storageKey = 'uuCustomerViewPrefs';

    connectedCallback() {
        // Restore last used accountId if present
        try {
            const items = loadItems(this.storageKey);
            const last = (items || []).find((it) => it.id === 'last') || items?.[0];
            if (last && last.accountId) {
                this.accountId = last.accountId;
            }
        } catch (e) {
            // ignore storage errors
        }
    }

    get isLoadDisabled() {
        return this.loading || !this.isValidSfId(this.accountId);
    }

    handleInputChange(event) {
        const { name, value } = event.target;
        if (name === 'accountId') {
            this.accountId = value?.trim();
            this.persistAccountId();
        }
    }

    async handleLoad() {
        this.error = null;
        this.context = null;
        this.loading = true;
        try {
            const res = await getCustomerByAccountId({ accountId: this.accountId });
            this.context = res;
            this.persistAccountId();
        } catch (e) {
            this.error = this.normalizeError(e);
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Errore caricamento contesto',
                    message: this.error,
                    variant: 'error'
                })
            );
        } finally {
            this.loading = false;
        }
    }

    // ===== Derived view fields =====
    get accountName() {
        return this.context?.account?.Name || '-';
    }
    get accountType() {
        const isPerson = !!this.context?.account?.IsPersonAccount;
        return isPerson ? 'Persona Fisica' : 'Azienda';
    }
    get accountEmail() {
        return (
            this.context?.account?.PersonEmail ||
            this.context?.account?.Email__c ||
            this.context?.contact?.Email ||
            '-'
        );
    }
    get accountPhone() {
        return (
            this.context?.account?.Phone ||
            this.context?.contact?.MobilePhone ||
            '-'
        );
    }

    get opportunitiesCount() {
        return (this.context?.opportunity || []).length;
    }
    get quotesCount() {
        return (this.context?.quote || []).length;
    }
    get assetsCount() {
        return (this.context?.assets || []).length;
    }

    get societiesCount() {
        const s = this.context?.AccountFianancialData?.societies;
        return s ? Object.keys(s).length : 0;
    }
    get agenciesCount() {
        const a = this.context?.AccountFianancialData?.agencies;
        return a ? Object.keys(a).length : 0;
    }

    get hasSocieties() {
        return this.societies.length > 0;
    }
    get hasAgencies() {
        return this.agencies.length > 0;
    }

    get societies() {
        const map = this.context?.AccountFianancialData?.societies || {};
        return Object.keys(map).map((code) => {
            const item = map[code] || {};
            const accountDetailsData = item.accountDetailsData || {};
            const detailTypes = Object.keys(accountDetailsData).join(', ');
            const details = Object.keys(accountDetailsData).map((type) => {
                const d = accountDetailsData[type] || {};
                return {
                    type,
                    accountDetailId: d.accountDetailId || d.accountDetail?.Id,
                    accountDetail: d.accountDetail,
                    accountDetailNPI: d.accountDetailNPI,
                    hasNPI: !!d.accountDetailNPI
                };
            });
            return {
                code,
                name: item.accountFinancial?.Name || '',
                detailTypes: detailTypes || '-/-',
                detailCount: Object.keys(accountDetailsData).length || 0,
                assetsCount: (item.assets || []).length,
                details
            };
        });
    }

    get agencies() {
        const map = this.context?.AccountFianancialData?.agencies || {};
        return Object.keys(map).map((code) => {
            const item = map[code] || {};
            return {
                code,
                name: item.accountFinancial?.Name || '',
                assetsCount: (item.assets || []).length
            };
        });
    }

    get errorMessage() {
        return this.error || '';
    }

    get prettyContext() {
        return this.context ? JSON.stringify(this.context, null, 2) : '';
    }

    // ===== *Data sections =====
    get hasAssetsData() {
        return (this.context?.assetsData || []).length > 0;
    }
    get assetsDataColumns() {
        return [
            { label: 'Asset', fieldName: 'name' },
            { label: 'Society', fieldName: 'societyCode' },
            { label: 'Agency', fieldName: 'agencyCode' },
            { label: 'Code', fieldName: 'assetCode' },
            { label: 'Key', fieldName: 'key' },
            { label: 'Value', fieldName: 'value' },
            { type: 'action', typeAttributes: { rowActions: [ { label: 'Dettagli', name: 'view' } ] } }
        ];
    }
    get assetsDataRows() {
        const list = this.context?.assetsData || [];
        return list.map((ad) => {
            const assetId = ad.assetId || ad.asset?.Id;
            const name = ad.asset?.Name || ad.asset?.Product2?.Name || assetId || '-/-';
            const societyCode = ad.societyCode || '';
            const agencyCode = ad.agencyCode || '';
            const assetCode = ad.assetCode || '';
            const key = ad.asset?.Key__c || '';
            const value = ad.asset?.Value__c;
            const labelParts = [name];
            const code = societyCode || agencyCode;
            if (code) labelParts.push(`(${code})`);
            if (assetCode) labelParts.push(`#${assetCode}`);
            return {
                assetId,
                name,
                societyCode,
                agencyCode,
                assetCode,
                key,
                value,
                label: labelParts.join(' '),
                assetRaw: ad.asset
            };
        });
    }

    get hasOpportunitiesData() {
        return (this.context?.opportunitiesData || []).length > 0;
    }
    get opportunitiesDataRows() {
        const list = this.context?.opportunitiesData || [];
        return list.map((od) => {
            const quotes = od.quotes || [];
            const quotesData = od.quotesData || [];
            const coveragesCount = quotesData.reduce((acc, qd) => acc + ((qd.opportunityCoverages || []).length), 0);
            const amount = od.opportunity?.Amount;
            const probability = od.opportunity?.Probability;
            return {
                id: od.opportunityId || od.opportunity?.Id,
                name: od.opportunity?.Name || od.opportunityId || '-/-',
                quotesCount: quotes.length || quotesData.length || 0,
                coveragesCount,
                stageName: od.opportunity?.StageName,
                engagementPoint: od.opportunity?.EngagementPoint__c,
                recordTypeName: od.opportunity?.RecordTypeName__c || od.opportunity?.Record_Type_Name__c || od.opportunity?.RecordTypeName,
                amount,
                amountFormatted: this.formatCurrency(amount),
                probability,
                probabilityPct: typeof probability === 'number' ? probability / 100 : null,
                probabilityFormatted: this.formatPercent(probability),
                domainType: od.opportunity?.DomainType__c,
                agencyFormula: od.opportunity?.AgencyFormula__c,
                contactChannel: od.opportunity?.ContactChannel__c,
                raw: od.opportunity,
                quotes: quotesData.map((qd) => ({
                    id: qd.quoteId || qd.quote?.Id,
                    name: qd.quote?.Name || qd.quoteId || '-/-',
                    number: qd.quote?.QuoteNumber,
                    status: qd.quote?.Status,
                    totalPrice: qd.quote?.TotalPrice ?? qd.quote?.GrandTotal ?? qd.quote?.Total,
                    email: qd.quote?.Email,
                    phone: qd.quote?.Phone,
                    areasOfNeed: qd.quote?.AreasOfNeed__c,
                    policyChannel: qd.quote?.PolicyChannel__c,
                    domainType: qd.quote?.DomainType__c,
                    engagementPoint: qd.quote?.EngagementPoint__c,
                    externalId: qd.quote?.ExternalId__c,
                    coverages: (qd.opportunityCoverages || []).map((c) => ({ id: c.Id, name: c.Name })),
                    coveragesTableRows: (qd.opportunityCoverages || []).map((c) => ({
                        id: c.Id,
                        FirstName__c: c.FirstName__c,
                        LastName__c: c.LastName__c,
                        EngagementPoint__c: c.EngagementPoint__c,
                        AreaOfNeed__c: c.AreaOfNeed__c,
                        BirthDate__c: c.BirthDate__c,
                        FiscalCode__c: c.FiscalCode__c,
                        MobilePhone__c: c.MobilePhone__c,
                        Email__c: c.Email__c,
                        Asset__c: c.Asset__c,
                        Fractionation__c: c.Fractionation__c,
                        Amount__c: c.Amount__c,
                        raw: c
                    }))
                })),
                quotesTableRows: quotesData.map((qd) => ({
                    id: qd.quoteId || qd.quote?.Id,
                    name: qd.quote?.Name || qd.quoteId || '-/-',
                    coveragesCount: (qd.opportunityCoverages || []).length,
                    number: qd.quote?.QuoteNumber,
                    status: qd.quote?.Status,
                    totalPrice: qd.quote?.TotalPrice ?? qd.quote?.GrandTotal ?? qd.quote?.Total,
                    email: qd.quote?.Email,
                    phone: qd.quote?.Phone,
                    areasOfNeed: qd.quote?.AreasOfNeed__c,
                    policyChannel: qd.quote?.PolicyChannel__c,
                    domainType: qd.quote?.DomainType__c,
                    engagementPoint: qd.quote?.EngagementPoint__c,
                    externalId: qd.quote?.ExternalId__c,
                    raw: qd.quote
                }))
            };
        });
    }
    get opportunitiesDataColumns() {
        return [
            { label: 'Opportunity', fieldName: 'name' },
            { label: 'StageName', fieldName: 'stageName' },
            { label: 'EngagementPoint__c', fieldName: 'engagementPoint' },
            { label: 'RecordTypeName__c', fieldName: 'recordTypeName' },
            { label: 'Amount', fieldName: 'amount', type: 'currency', cellAttributes: { alignment: 'left' } },
            { label: 'Probability', fieldName: 'probabilityPct', type: 'percent', typeAttributes: { minimumFractionDigits: 0, maximumFractionDigits: 2 }, cellAttributes: { alignment: 'left' } },
            { label: 'DomainType__c', fieldName: 'domainType' },
            { label: 'AgencyFormula__c', fieldName: 'agencyFormula' },
            { label: 'ContactChannel__c', fieldName: 'contactChannel' },
            { type: 'action', typeAttributes: { rowActions: [ { label: 'Dettagli', name: 'view' } ] } }
        ];
    }
    get quotesDataColumns() {
        return [
            { label: 'Quote', fieldName: 'name' },
            { label: 'Number', fieldName: 'number' },
            { label: 'Status', fieldName: 'status' },
            { label: 'Email', fieldName: 'email' },
            { label: 'Phone', fieldName: 'phone' },
            { label: 'AreasOfNeed__c', fieldName: 'areasOfNeed' },
            { label: 'TotalPrice', fieldName: 'totalPrice', type: 'number', cellAttributes: { alignment: 'left' } },
            { label: 'PolicyChannel__c', fieldName: 'policyChannel' },
            { label: 'DomainType__c', fieldName: 'domainType' },
            { label: 'EngagementPoint__c', fieldName: 'engagementPoint' },
            { label: 'ExternalId__c', fieldName: 'externalId' },
            { label: 'Coverages', fieldName: 'coveragesCount', type: 'number', cellAttributes: { alignment: 'left' } },
            { type: 'action', typeAttributes: { rowActions: [ { label: 'Dettagli', name: 'view' } ] } }
        ];
    }
    get coveragesColumns() {
        return [
            { label: 'FirstName__c', fieldName: 'FirstName__c' },
            { label: 'LastName__c', fieldName: 'LastName__c' },
            { label: 'EngagementPoint__c', fieldName: 'EngagementPoint__c' },
            { label: 'AreaOfNeed__c', fieldName: 'AreaOfNeed__c' },
            { label: 'BirthDate__c', fieldName: 'BirthDate__c', type: 'date' },
            { label: 'FiscalCode__c', fieldName: 'FiscalCode__c' },
            { label: 'MobilePhone__c', fieldName: 'MobilePhone__c' },
            { label: 'Email__c', fieldName: 'Email__c' },
            { label: 'Asset__c', fieldName: 'Asset__c' },
            { label: 'Fractionation__c', fieldName: 'Fractionation__c' },
            { label: 'Amount__c', fieldName: 'Amount__c', type: 'number', cellAttributes: { alignment: 'left' } },
            { type: 'action', typeAttributes: { rowActions: [ { label: 'Dettagli', name: 'view' } ] } }
        ];
    }
    get assetsDataColumns() {
        return [
            { label: 'Asset', fieldName: 'name' },
            { label: 'Society', fieldName: 'societyCode' },
            { label: 'Agency', fieldName: 'agencyCode' },
            { label: 'Code', fieldName: 'assetCode' },
            { label: 'Key', fieldName: 'key' },
            { label: 'Value', fieldName: 'value' },
            { type: 'action', typeAttributes: { rowActions: [ { label: 'Dettagli', name: 'view' } ] } }
        ];
    }
    get opportunitiesDataColumns() {
        return [
            { label: 'Opportunity', fieldName: 'name' },
            { label: 'StageName', fieldName: 'stageName' },
            { label: 'EngagementPoint__c', fieldName: 'engagementPoint' },
            { label: 'RecordTypeName__c', fieldName: 'recordTypeName' },
            { label: 'Amount', fieldName: 'amount', type: 'currency', cellAttributes: { alignment: 'left' } },
            { label: 'Probability', fieldName: 'probabilityPct', type: 'percent', typeAttributes: { minimumFractionDigits: 0, maximumFractionDigits: 2 }, cellAttributes: { alignment: 'left' } },
            { label: 'DomainType__c', fieldName: 'domainType' },
            { label: 'AgencyFormula__c', fieldName: 'agencyFormula' },
            { label: 'ContactChannel__c', fieldName: 'contactChannel' },
            { type: 'action', typeAttributes: { rowActions: [ { label: 'Dettagli', name: 'view' } ] } }
        ];
    }

    // Row action handlers to open detail modals
    handleAssetsRowAction(event) {
        const { action, row } = event.detail;
        if (action.name !== 'view') return;
        const fields = [
            { label: 'AssetId', value: row.assetId },
            { label: 'Society', value: row.societyCode },
            { label: 'Agency', value: row.agencyCode },
            { label: 'Code', value: row.assetCode },
            { label: 'Key', value: row.key },
            { label: 'Value', value: row.value }
        ];
        UuCustomerViewDetailModal.open({ size: 'large', title: row.name || 'Asset', fields, record: row.assetRaw });
    }
    handleOppRowAction(event) {
        const { action, row } = event.detail;
        if (action.name !== 'view') return;
        const fields = [
            { label: 'Name', value: row.name },
            { label: 'StageName', value: row.stageName },
            { label: 'EngagementPoint__c', value: row.engagementPoint },
            { label: 'RecordTypeName__c', value: row.recordTypeName },
            { label: 'Amount', value: this.formatCurrency(row.amount) },
            { label: 'Probability', value: this.formatPercent(row.probability) },
            { label: 'DomainType__c', value: row.domainType },
            { label: 'AgencyFormula__c', value: row.agencyFormula },
            { label: 'ContactChannel__c', value: row.contactChannel }
        ];
        UuCustomerViewDetailModal.open({ size: 'large', title: 'Opportunity', fields, record: row.raw });
    }
    handleQuoteRowAction(event) {
        const { action, row } = event.detail;
        if (action.name !== 'view') return;
        const fields = [
            { label: 'Name', value: row.name },
            { label: 'Number', value: row.number },
            { label: 'Status', value: row.status },
            { label: 'Email', value: row.email },
            { label: 'Phone', value: row.phone },
            { label: 'AreasOfNeed__c', value: row.areasOfNeed },
            { label: 'TotalPrice', value: row.totalPrice },
            { label: 'PolicyChannel__c', value: row.policyChannel },
            { label: 'DomainType__c', value: row.domainType },
            { label: 'EngagementPoint__c', value: row.engagementPoint },
            { label: 'ExternalId__c', value: row.externalId }
        ];
        UuCustomerViewDetailModal.open({ size: 'large', title: 'Quote', fields, record: row.raw });
    }
    handleCoverageRowAction(event) {
        const { action, row } = event.detail;
        if (action.name !== 'view') return;
        const fields = [
            { label: 'FirstName__c', value: row.FirstName__c },
            { label: 'LastName__c', value: row.LastName__c },
            { label: 'EngagementPoint__c', value: row.EngagementPoint__c },
            { label: 'AreaOfNeed__c', value: row.AreaOfNeed__c },
            { label: 'BirthDate__c', value: row.BirthDate__c },
            { label: 'FiscalCode__c', value: row.FiscalCode__c },
            { label: 'MobilePhone__c', value: row.MobilePhone__c },
            { label: 'Email__c', value: row.Email__c },
            { label: 'Asset__c', value: row.Asset__c },
            { label: 'Fractionation__c', value: row.Fractionation__c },
            { label: 'Amount__c', value: row.Amount__c }
        ];
        UuCustomerViewDetailModal.open({ size: 'large', title: 'Coverage', fields, record: row.raw });
    }

    // societiesDetailed no longer needed; details included in societies()

    // ===== Helpers =====
    persistAccountId() {
        try {
            if (!this.isValidSfId(this.accountId)) return;
            upsertItem(this.storageKey, { id: 'last', accountId: this.accountId, timestamp: Date.now() }, 'id');
        } catch (e) {
            // ignore storage errors
        }
    }

    isValidSfId(id) {
        if (!id) return false;
        const re = /^[a-zA-Z0-9]{15,18}$/;
        return re.test(id);
    }

    normalizeError(e) {
        if (!e) return 'Errore sconosciuto';
        if (typeof e === 'string') return e;
        if (e?.body?.message) return e.body.message;
        if (e?.message) return e.message;
        try {
            return JSON.stringify(e);
        } catch (_) {
            return 'Errore sconosciuto';
        }
    }

    // Formatting helpers
    formatCurrency(v) {
        try {
            if (v === null || v === undefined || isNaN(v)) return '';
            return new Intl.NumberFormat(undefined, { style: 'currency' }).format(v);
        } catch (_) {
            return String(v);
        }
    }
    formatPercent(v) {
        try {
            if (v === null || v === undefined || isNaN(v)) return '';
            const pct = Number(v) / 100;
            return new Intl.NumberFormat(undefined, { style: 'percent', minimumFractionDigits: 0, maximumFractionDigits: 2 }).format(pct);
        } catch (_) {
            return String(v);
        }
    }
}
