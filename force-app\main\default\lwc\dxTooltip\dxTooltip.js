import { LightningElement, api, track } from 'lwc';

const mapperDirectionTooltip = {
  TooltipLD: 'left-bottom',
  TooltipLU: 'left-top',
  TooltipRD: 'right-bottom',
  TooltipRU: 'right-top',
};

export default class Tooltip extends LightningElement {
  @track directionTooltip = 'left-top';
  _field;
  _view;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    if (value?.customAttributes?.tooltipDirection) {
      this.directionTooltip =
        mapperDirectionTooltip[value.customAttributes.tooltipDirection] || 'left-bottom';
    }
  }

  get tooltipLayoutGroups() {
    return this.field.groups[0];
  }

  get tooltipGroupsContent() {
    return `tooltip-groups-content box-tooltip ${this.directionTooltip}`;
  }

  handleClose() {
    this.dispatchEvent(new CustomEvent('closetooltip', { detail: false }));
  }
}
