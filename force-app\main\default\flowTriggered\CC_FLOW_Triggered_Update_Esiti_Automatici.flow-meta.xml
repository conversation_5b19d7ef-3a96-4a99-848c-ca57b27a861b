<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <decisions>
        <name>check_esito_case</name>
        <label>check esito case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>tentatoContatto_con_chiamate_5</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Intermediate_Outcome__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tentato Contatto</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ActualCalls__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>5.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>updateRecordTrigger</targetReference>
            </connector>
            <label>tentatoContatto con chiamate &gt;5</label>
        </rules>
        <rules>
            <name>esito_vendita</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Vendita</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>updateEsitoIntermedioInAttesaPagamento</targetReference>
            </connector>
            <label>esito vendita</label>
        </rules>
        <rules>
            <name>esito_numero_errato</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non contattabile - numero errato</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_update_opportunity</targetReference>
            </connector>
            <label>esito numero errato</label>
        </rules>
        <rules>
            <name>esito_non_interessato</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Esito__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non interessato</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Copy_1_of_update_opportunity</targetReference>
            </connector>
            <label>esito non interessato</label>
        </rules>
    </decisions>
    <description>Flow trigger che scatta per la gestione degli esiti automatici</description>
    <environments>Default</environments>
    <interviewLabel>CC {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-FLOW_Triggered_Update_Esiti_Automatici</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Copy_1_of_Copy_1_of_update_opportunity</name>
        <label>Copy 1 of Copy 1 of update opportunity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Opportunity__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_update_opportunity</name>
        <label>Copy 1 of update opportunity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Opportunity__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>update_opportunity</name>
        <label>update opportunity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Opportunity__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>updateEsitoIntermedioInAttesaPagamento</name>
        <label>updateEsitoIntermedioInAttesaPagamento</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Intermediate_Outcome__c</field>
            <value>
                <stringValue>Attesa di Pagamento</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>updateRecordTrigger</name>
        <label>updateRecordTrigger</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>update_opportunity</targetReference>
        </connector>
        <inputAssignments>
            <field>Esito__c</field>
            <value>
                <stringValue>Mancata risposta -limite chiamate</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>check_esito_case</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Esito__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Sottoesito__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ActualCalls__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <numberValue>5.0</numberValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
