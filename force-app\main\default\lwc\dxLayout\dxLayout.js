import { ICON_MAPPER_URLS } from 'c/dxIconMapping';
import { utils } from 'c/dxUtils';
import { utilsPega } from 'c/dxUtilsPega';
import { LightningElement, api, track } from 'lwc';
export default class Layout extends LightningElement {
  _layout;
  index;

  @track groupFormat;
  @track accordionType;
  _inlineStylesFromGroupFormat = {};

  @api debug;

  @api
  get layout() {
    return this._layout;
  }

  set layout(value) {
    this._layout = { ...value };

    if (
      this._layout.groupFormat &&
      this._layout?.groupFormat.includes('Dynamic') &&
      this._layout?.repeatLayoutFormat
    ) {
      this._layout.groupFormat = this._layout.repeatLayoutFormat;
    }

    if (this._layout.groupFormat.includes('ShowAfter') || this._layout.groupFormat.includes('Accordion')) {
      const preRegexp = this._layout?.groupFormat?.includes('Accordion') ? 'Accordion' : 'ShowAfter';
      const genericRegExp = new RegExp(`${preRegexp}\\s*(?<type>\\S+?)$`);
      const regexExecution = genericRegExp.exec(this._layout?.groupFormat);
      if (regexExecution?.groups?.type) {
        const type = regexExecution?.groups?.type;
        this.accordionType = type;
      }
    }

    const result = utilsPega.layout.getGroupFormat(this._layout.groupFormat);

    if (result?.groupFormat) {
      this.groupFormat = result.groupFormat;
    }
    this._inlineStylesFromGroupFormat = result?.inlineStyles || {};
  }

  get layoutGroupFormat() {
    return this._layout.groupFormat;
  }

  get computedStyles() {
    const rowColCardBoxStyles = {
      ...(utilsPega.rowCol.setAlignment(this.rowColRules?.alignment || [], this.rowColRules?.type) || {}),
      ...(utilsPega.rowCol.setGap(this.rowColRules?.gap || []) || {}),
      ...(utilsPega.rowCol.setColumnsWidth(this.rowColRules?.gridTemplateColumns || []) || {}),
      ...(utilsPega.rowCol.setAvailableSpace(this.rowColRules?.availableSpace || []) || {}),
      ...(utilsPega.rowCol.setMaxWidth((this.rowColRules?.maxWidth || []).join('')) || {}),
      ...(utilsPega.cardBox.setBgColorBox(this.cardBoxRules?.bgColorBox || []) || {}),
    };

    const combinedStyles = {
      ...this._inlineStylesFromGroupFormat,
      ...rowColCardBoxStyles,
    };
    return utils.getStyleStringFromObj(combinedStyles);
  }

  get rowColRules() {
    return utilsPega.rowCol.getRules(this.layout.groupFormat);
  }

  get cardBoxRules() {
    return utilsPega.cardBox.getRules(this.layout.groupFormat);
  }

  get key() {
    return utils.generateKey('v');
  }

  get hasGroupClass() {
    const formats = [
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.mimicASentence,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.mimicASentenceCenter,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.mimicASentenceCenterWeb,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.DefaultBgGrey,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.CardBgWhite,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.cardApp,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.boxWebBorderGray1,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.inlineGridXY,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.inlineGrid7030,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionHeader,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.responsive2col,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.responsive2colPU,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.responsive2col8,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.responsive2col1fr2fr,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.responsive3col,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.priceContainer,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.col,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.row,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.card,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.box,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.roundedCard,
      /*utilsPega.layout.SUPPORTED_GROUP_FORMATS.tooltipCard, logica ad-hoc per tooltip card*/
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.inlineTPDmiddle,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.carrelloPUHeader,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.unicoProtezioneRibbon,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.assurancePackageRibbon,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.dynamic,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionOpened,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionClosed,
      utilsPega.layout.SUPPORTED_GROUP_FORMATS.default,
    ];

    return formats.includes(this.groupFormat);
  }

  get isPuPaddingLayout() {
    return this.groupFormat === 'responsive-padding-container';
  }

  get hasCustomComponentClass() {
    return this.groupFormat == utilsPega.layout.SUPPORTED_GROUP_FORMATS.customComponent;
  }

  get isAcolDrowTrowMcol() {
    return `Responsive ACol DRow TRow MCol ${this.isAColDRowTRowMCol ? 'AColDRowTRowMCol' : ''}`;
  }

  get isAccordionN() {
    return this.groupFormat == utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionN;
  }

  get isCardForm() {
    return this.layout?.groupFormat === 'cardForm';
  }

  get isTooltip() {
    return this.view?.viewID === 'TooltipPertinenze';
  }

  get isTooltipCard() {
    return this.layout?.groupFormat == 'TooltipCard';
  }

  get isFlatCardLayout() {
    return this.groupFormat == utilsPega.layout.SUPPORTED_GROUP_FORMATS.flatCard;
  }

  get componentClass() {
    return `layout ${this.debug ? 'debug' : ''}`.trim();
  }

  get isVisible() {
    return (
      (!!this.layout && !(this.layout.visible === false)) ||
      (!!this.layout && this.groupFormat === utilsPega.layout.SUPPORTED_GROUP_FORMATS.dynamic)
    );
  }

  get isDefault() {
    return (
      !this.isPuPaddingLayout &&
      !this.hasGroupClass &&
      !this.isAccordionOpened &&
      !this.isAccordionOpenedOrClosed &&
      !this.hasCustomComponentClass &&
      !this.isAccordionN &&
      !this.isFlatCardLayout &&
      !this.isTooltipCard
    );
  }

  get iconInfoPu() {
    const iconUrl = ICON_MAPPER_URLS['lightInfoPu'];
    return `background-image: url(${iconUrl});`;
  }

  get isAccordionOpened() {
    return this.groupFormat === utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionOpened;
  }

  get isAccordionOpenedOrClosed() {
    return (
      this.groupFormat === utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionClosed ||
      this.groupFormat === utilsPega.layout.SUPPORTED_GROUP_FORMATS.accordionOpened
    );
  }

  get hasDynamicLayoutRows() {
    return !!this.layout?.rows && this.layout?.rows.length > 0;
  }

  get visibleGroups() {
    const visibleElements = this.layout?.groups?.filter((element) => {
      const key = Object.keys(element)[0];
      return element[key]?.visible === true;
    });

    return visibleElements;
  }
}
