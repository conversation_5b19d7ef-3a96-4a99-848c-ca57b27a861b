<template>
  <div class="uujson-container" style={containerStyle}>
    <div style="position:relative;">
      <lightning-button-icon icon-name="utility:copy" variant="bare" alternative-text="Copia" title="Copia"
        onclick={handleCopyClick} class="copy-btn"></lightning-button-icon>
    </div>
    <template if:true={useJsonFormatter}>
      <div class="editor-host"></div>
    </template>
    <template if:false={useJsonFormatter}>
      <template if:true={editable}>
        <textarea class="editor-textarea" value={textValue} oninput={handleTextareaInput}></textarea>
        <template if:true={parseError}>
          <div class="slds-text-color_error slds-m-top_x-small">{parseError}</div>
        </template>
      </template>
      <template if:false={editable}>
        <pre class="editor-pre">{formattedValue}</pre>
      </template>
    </template>
  </div>
</template>