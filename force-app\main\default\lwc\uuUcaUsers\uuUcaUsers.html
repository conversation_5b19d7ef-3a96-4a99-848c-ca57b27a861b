<template>
  <div class="slds-m-around_medium section ">
    <div class="section slds-m-bottom_medium">
      <lightning-card title="Group Checker" class="slds-m-around_medium">
        <div class="slds-p-around_small">
          <lightning-input
            data-id="group-input"
            label="Group Name"
            value={groupToCheck}
            onchange={handleGroupInputChange}
          ></lightning-input>
          <lightning-button
            data-id="check-button"
            class="slds-m-top_small"
            label="Check"
            onclick={handleGroupCheck}
          ></lightning-button>
          <template if:true={groupCheckMessage}>
            <p data-id="check-result" class="slds-m-top_small">
              {groupCheckMessage}
            </p>
          </template>
        </div>
      </lightning-card>
    </div>

    <div class="section">
      <lightning-card title="UCA Users" class="slds-m-around_medium">
        <div class="slds-p-around_small">
          <lightning-combobox
            name="ucaUsers"
            label="UCA Users"
            value={selectedUserId}
            options={options}
            onchange={handleChange}
          ></lightning-combobox>
          <lightning-input
            data-id="user-id"
            label="User Id"
            value={selectedUserId}
            readonly
          ></lightning-input>
          <template if:true={hasGroups}>
            <lightning-datatable
              key-field="id"
              data={groupsData}
              columns={columns}
            ></lightning-datatable>
          </template>
        </div>
      </lightning-card>
    </div>
  </div>
</template>
