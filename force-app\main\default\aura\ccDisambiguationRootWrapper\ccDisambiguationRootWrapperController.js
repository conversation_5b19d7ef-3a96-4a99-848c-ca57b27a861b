({
  doInit: function (component, event, helper) {
    try {
      var pageRef = component.get("v.pageReference");
      var state = (pageRef && pageRef.state) || {};

      // Prefer explicit c__recordId passed by the navigator (Flow bridge)
      var recId = state.c__recordId || component.get("v.recordId");
      component.set("v.caseId", recId);

      // Set subtab title in console
      var workspaceAPI = component.find("workspace");
      if (workspaceAPI && workspaceAPI.getEnclosingTabId) {
        workspaceAPI
          .getEnclosingTabId()
          .then(function (tabId) {
            // Set label and icon for the subtab
            workspaceAPI.setTabLabel({ tabId: tabId, label: "Preventivazione" });
            workspaceAPI.setTabIcon({ tabId: tabId, icon: "standard:opportunity", iconAlt: "Preventivazione" });
          })
          .catch(function () {
            // Not in console or API not available; ignore
          });
      }
    } catch (e) {
      // Fallback: leave caseId undefined; the LWC can handle missing value
      // No-op
    }
  },
})
