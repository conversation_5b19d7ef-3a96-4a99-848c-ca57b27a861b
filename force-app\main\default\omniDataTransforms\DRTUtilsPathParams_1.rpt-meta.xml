<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;request&quot; : {
    &quot;civico&quot; : &quot;10&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;F205&quot;,
    &quot;comune&quot; : &quot;MILANO&quot;,
    &quot;indirizzo&quot; : &quot;VIA%20QUINTO%20ROMANO&quot;,
    &quot;siglaProvincia&quot; : &quot;MI&quot;
  }
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;Params&quot; : {
    &quot;PathParam&quot; : {
      &quot;civico&quot; : &quot;10&quot;,
      &quot;codiceBelfioreComune&quot; : &quot;F205&quot;,
      &quot;comune&quot; : &quot;MILANO&quot;,
      &quot;indirizzo&quot; : &quot;VIA%20QUINTO%20ROMANO&quot;,
      &quot;siglaProvincia&quot; : &quot;MI&quot;
    }
  }
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DRTUtilsPathParams</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTUtilsPathParamsCustom0jI9b000001bBDdEAMItem0</globalKey>
        <inputFieldName>request</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTUtilsPathParams</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Params:PathParam</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;request&quot; : {
    &quot;category&quot; : &quot;AGENZIA&quot;,
    &quot;lat&quot; : &quot;45.6336676&quot;,
    &quot;lng&quot; : &quot;9.3061203&quot;,
    &quot;maxItem&quot; : &quot;50000&quot;,
    &quot;pro&quot; : &quot;002&quot;
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>DRTUtilsPathParams_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
