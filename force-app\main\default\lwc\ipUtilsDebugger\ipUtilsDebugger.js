import { LightningElement, track } from "lwc";
import IpInvocationHistoryModal from 'c/ipInvocationHistoryModal';
import IpSavedItemsModal from 'c/ipSavedItemsModal';
import { ShowToastEvent } from "lightning/platformShowToastEvent";

import { loadItems, saveItems, upsertItem } from "c/uuLocalStorageUtils";

import IPUtils from "c/ipUtils";

export default class IPUtilsDebugger extends LightningElement {
  @track isLoading = false;

  procedureName = "";
  label = "";
  request = "{}";
  response;
  // removed editable toggle feature

  get disableSend() {
    return !this.procedureName || !this.isValidJson(this.request);
  }

  showResponseJson = false;
  showInvocationHistoryModal = false; // kept for completeness; LightningModal handles visibility
  history = [];
  historyStorageKey = "iPUtilsDebuggerHistory";
  draftStorageKey = "iPUtilsDebuggerDraft";
  mockStorageKey = "iPUtilsDebuggerMockMap"; // { [procedureName]: responseObject }
  historyColumns = [
    { label: 'DateTime', fieldName: 'formattedDate', type: 'text' },
    { label: 'Procedura', fieldName: 'name', type: 'text' },
    { label: 'Label', fieldName: 'label', type: 'text' },
  { label: 'Mock attivo', fieldName: 'mockActive', type: 'boolean', cellAttributes: { alignment: 'center' } },
    {
      type: 'action',
      typeAttributes: {
        rowActions: [
          { label: 'Modifica', name: 'edit', iconName: 'utility:edit' },
          { label: 'Seleziona', name: 'select', iconName: 'utility:check' },
          { label: 'Duplica', name: 'duplicate', iconName: 'utility:copy' },
          { label: 'Rimuovi', name: 'remove', iconName: 'utility:delete' },
          { label: 'Attiva Mock', name: 'mock_activate', iconName: 'utility:toggle' },
          { label: 'Disattiva Mock', name: 'mock_deactivate', iconName: 'utility:toggle' }
        ],
        menuAlignment: 'right'
      }
    }
  ];

  // search/import state
  historySearchTerm = "";
  showImportArea = false;
  importJson = "";
  durationMs = 0;

  connectedCallback() {
    // Global shortcut: Cmd/Ctrl + Enter to send
    this._onKeydown = (e) => this.handleGlobalKeydown(e);
    try { window.addEventListener('keydown', this._onKeydown); } catch(e) {}
    this.history = loadItems(this.historyStorageKey)
      .map((h) => ({ ...h, mockActive: !!h.mockActive, formattedDate: this.formatDate(h.timestamp) }))
      .sort((a, b) => b.timestamp - a.timestamp);
    // load draft if any
    try {
      const draft = window.localStorage.getItem(this.draftStorageKey);
      if (draft) {
        const { procedureName, label, request } = JSON.parse(draft);
        this.procedureName = procedureName || this.procedureName;
        this.label = label || this.label;
        this.request = request || this.request;
      }
    } catch (e) {
      // ignore
    }
  }

  disconnectedCallback() {
    try { window.removeEventListener('keydown', this._onKeydown); } catch(e) {}
  }

  formatDate(timestamp) {
 
    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  handleProcedureChange(event) {
    const newName = event.target.value;
    if (!this.label || this.label === this.procedureName) {
      this.label = newName;
    }
    this.procedureName = newName;
  }

  handleRequestChange(event) {
    this.request = event.target.value;
    this.persistDraft();
  }

  // uuJsonView change handler (edit mode)
  handleRequestJsonChange(event) {
    try {
      const obj = event?.detail?.value ?? {};
      this.request = JSON.stringify(obj, null, 2);
      this.persistDraft();
    } catch (e) {
      // ignore
    }
  }

  handleLabelChange(event) {
    this.label = event.target.value;
  this.persistDraft();
  }

  async handleInvokeIntegrationProcedure() {
    this.response = "";
    try {
      this.isLoading = true;
      this.durationMs = 0;
      const start = performance.now ? performance.now() : Date.now();
      const payload = this.request ? JSON.parse(this.request) : {};
      const result = await IPUtils.invokeIntegrationProcedure(
        this.procedureName,
        { request: payload.request || payload },
      );
      this.response = JSON.stringify(result, null, 2);
      this.addToHistory();
      const end = performance.now ? performance.now() : Date.now();
      this.durationMs = Math.round(end - start);
      this.dispatchEvent(
        new ShowToastEvent({
          title: "Invocazione completata",
          message: `Durata: ${this.durationMs} ms`,
          variant: "success",
        }),
      );
    } catch (error) {
      this.response = error.message;
      this.dispatchEvent(
        new ShowToastEvent({
          title: "Errore",
          message: error?.message || "Errore sconosciuto",
          variant: "error",
        }),
      );
    } finally {
      this.isLoading = false;
    }
  }

  get responseJsonButtonLabel() {
    return this.showResponseJson ? "Nascondi JSON" : "Mostra JSON";
  }

  get hasJsonResponse() {
    return this.isValidJson(this.response);
  }

  get responseObject() {
    try {
      return JSON.parse(this.response);
    } catch (e) {
      return null;
    }
  }

  toggleResponseJson() {
    this.showResponseJson = !this.showResponseJson;
  }

  copyResponseJson() {
    if (this.response && typeof navigator !== "undefined") {
      navigator.clipboard.writeText(this.response);
      this.dispatchEvent(
        new ShowToastEvent({ title: "Copiato", message: "Response copiata", variant: "success" }),
      );
    }
  }

  openInvocationHistory() {
  IpInvocationHistoryModal.open({ size: 'large', label: 'Log Invocazioni' });
  }

  closeInvocationHistory() {
  this.showInvocationHistoryModal = false;
  }

  addToHistory() {
    const key = this.label || this.procedureName;
    const item = {
      id: Date.now().toString(),
      name: this.procedureName,
      request: this.request,
      label: key,
      timestamp: Date.now(),
  mockActive: false,
  response: this.response, // store last response for potential mocking
    };
    this.history = upsertItem(this.historyStorageKey, item, "label");
    this.history = this.history
      .map((h) => ({ ...h, formattedDate: this.formatDate(h.timestamp) }))
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  async openHistory() {
    const result = await IpSavedItemsModal.open({
      size: 'large',
      label: 'Salvati',
      historyStorageKey: this.historyStorageKey,
      mockStorageKey: this.mockStorageKey,
    });
    if (result && result.action === 'select' && result.item) {
      const item = result.item;
      this.procedureName = item.name;
      this.label = item.label;
      this.request = item.request;
    }
    // reload history in host after modal closes to keep list fresh
    this.history = loadItems(this.historyStorageKey)
      .map((h) => ({ ...h, mockActive: !!h.mockActive, formattedDate: this.formatDate(h.timestamp) }))
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  handleHistorySelect(event) {
    const id = event.currentTarget.dataset.id;
    const index = this.history.findIndex((h) => h.id === id);
    if (index > -1) {
      const [item] = this.history.splice(index, 1);
      item.timestamp = Date.now();
      this.history = [item, ...this.history];
      saveItems(this.historyStorageKey, this.history);
      this.procedureName = item.name;
      this.label = item.label;
      this.request = item.request;
      this.showHistoryModal = false;
      // Non invocare l'integration procedure
    }
  }

  // row actions are now handled inside IpSavedItemsModal

  // edit logic moved to IpSavedItemsModal + IpEditSavedItemModal

  // removal handled inside modal

  // Mock activation logic: only one active per procedure (name)
  // mock activation handled inside modal

  // mock deactivation handled inside modal

  // UI helpers and new features
  isValidJson(str) {
    try {
      if (!str || !str.trim()) return false;
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  formatRequest() {
    try {
      if (!this.isValidJson(this.request)) throw new Error("JSON non valido");
      const obj = JSON.parse(this.request);
      this.request = JSON.stringify(obj, null, 2);
      this.persistDraft();
    } catch (e) {
      this.dispatchEvent(new ShowToastEvent({ title: "Errore", message: e.message, variant: "error" }));
    }
  }

  minifyRequest() {
    try {
      if (!this.isValidJson(this.request)) throw new Error("JSON non valido");
      const obj = JSON.parse(this.request);
      this.request = JSON.stringify(obj);
      this.persistDraft();
    } catch (e) {
      this.dispatchEvent(new ShowToastEvent({ title: "Errore", message: e.message, variant: "error" }));
    }
  }

  clearRequest() {
    this.request = "";
    this.persistDraft();
  }

  copyRequest() {
    if (typeof navigator !== "undefined") {
      navigator.clipboard.writeText(this.request || "");
      this.dispatchEvent(
        new ShowToastEvent({ title: "Copiato", message: "Request copiata", variant: "success" }),
      );
    }
  }

  handleGlobalKeydown(event) {
    try {
      const isMac = (navigator.platform || '').toUpperCase().includes('MAC');
      const cmdOrCtrl = isMac ? event.metaKey : event.ctrlKey;
      if (cmdOrCtrl && event.key === 'Enter') {
        event.preventDefault();
        if (!this.disableSend) this.handleInvokeIntegrationProcedure();
      }
    } catch (e) {
      // ignore
    }
  }

  persistDraft() {
    try {
      const draft = {
        procedureName: this.procedureName,
        label: this.label,
        request: this.request,
      };
      window.localStorage.setItem(this.draftStorageKey, JSON.stringify(draft));
    } catch (e) {
      // ignore
    }
  }

  // History search/filter/export/import
  get filteredHistory() {
    const term = (this.historySearchTerm || "").toLowerCase();
    if (!term) return this.history;
    return this.history.filter(
      (h) =>
        (h.name || "").toLowerCase().includes(term) ||
        (h.label || "").toLowerCase().includes(term) ||
        (h.request || "").toLowerCase().includes(term),
    );
  }

  // Request as object for uuJsonView value binding
  get requestObject() {
    try {
      return this.request ? JSON.parse(this.request) : {};
    } catch (e) {
      return {};
    }
  }
  get hasFilteredHistory() {
    return (this.filteredHistory || []).length > 0;
  }

  handleHistorySearchChange(event) {
    this.historySearchTerm = event.target.value;
  }

  handleHistoryDuplicate(event) {
    const id = event.currentTarget.dataset.id;
    const src = this.history.find((h) => h.id === id);
    if (!src) return;
    const copy = {
      ...src,
      id: Date.now().toString(),
      label: `${src.label} (copy)`,
      timestamp: Date.now(),
  mockActive: false, // duplicated items start inactive
  response: src.response,
    };
    this.history = upsertItem(this.historyStorageKey, copy, "id");
    this.history = this.history
      .map((h) => ({ ...h, formattedDate: this.formatDate(h.timestamp) }))
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  handleHistoryClearAll() {
    this.history = [];
    saveItems(this.historyStorageKey, this.history);
  }

  exportHistory() {
    try {
      const data = JSON.stringify(this.history, null, 2);
      if (typeof navigator !== "undefined") {
        navigator.clipboard.writeText(data);
        this.dispatchEvent(
          new ShowToastEvent({ title: "Esportato", message: "Cronologia copiata negli appunti", variant: "success" }),
        );
      }
    } catch (e) {
      this.dispatchEvent(new ShowToastEvent({ title: "Errore", message: e.message, variant: "error" }));
    }
  }

  get importToggleButtonLabel() {
    return this.showImportArea ? "Nascondi import" : "Mostra import";
  }
  toggleImportArea() {
    this.showImportArea = !this.showImportArea;
  }
  handleImportJsonChange(event) {
    this.importJson = event.target.value;
  }
  importHistory() {
    try {
      const arr = JSON.parse(this.importJson);
      if (!Array.isArray(arr)) throw new Error("Formato non valido: atteso un array");
      const normalized = arr.map((h) => ({
        id: h.id?.toString() || Date.now().toString(),
        name: h.name || "",
        request: typeof h.request === "string" ? h.request : JSON.stringify(h.request || {}),
        label: h.label || h.name || "",
  timestamp: h.timestamp || Date.now(),
  mockActive: !!h.mockActive,
        response: typeof h.response === "string" ? h.response : h.response ? JSON.stringify(h.response) : undefined,
      }));
      saveItems(this.historyStorageKey, normalized);
      this.history = loadItems(this.historyStorageKey)
        .map((h) => ({ ...h, formattedDate: this.formatDate(h.timestamp) }))
        .sort((a, b) => b.timestamp - a.timestamp);
      // rebuild mock map from imported items
      const map = {};
      for (const it of this.history) {
        if (it.mockActive && it.name && it.response) {
          try {
            map[it.name] = JSON.parse(it.response);
          } catch (e) {
            // ignore invalid response
          }
        }
      }
      window.localStorage.setItem(this.mockStorageKey, JSON.stringify(map));
      this.dispatchEvent(new ShowToastEvent({ title: "Import completato", variant: "success" }));
    } catch (e) {
      this.dispatchEvent(new ShowToastEvent({ title: "Errore import", message: e.message, variant: "error" }));
    }
  }

  // Mock map helpers
  loadMockMap() {
    try {
      const raw = window.localStorage.getItem(this.mockStorageKey);
      return raw ? JSON.parse(raw) : {};
    } catch (e) {
      return {};
    }
  }
  saveMockMap(map) {
    try {
      window.localStorage.setItem(this.mockStorageKey, JSON.stringify(map));
    } catch (e) {
      // ignore
    }
  }
  saveMockForProcedure(name, responseStr) {
    try {
      const map = this.loadMockMap();
      // ensure only one per procedure (key overwrite)
      let resp;
      try {
        resp = typeof responseStr === "string" ? JSON.parse(responseStr) : responseStr;
      } catch (e) {
        resp = responseStr;
      }
      map[name] = resp;
      this.saveMockMap(map);
    } catch (e) {
      // ignore
    }
  }
  removeMockForProcedure(name) {
    const map = this.loadMockMap();
    if (map && Object.prototype.hasOwnProperty.call(map, name)) {
      delete map[name];
      this.saveMockMap(map);
    }
  }
}
