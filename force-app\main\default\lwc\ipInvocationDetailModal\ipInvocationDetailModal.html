<template>
  <lightning-modal-header label="Dettagli Invocazione"></lightning-modal-header>
  <lightning-modal-body>
    <lightning-accordion allow-multiple-sections-open onsectiontoggle={handleSectionToggle} active-section-name={activeSections}>
      <lightning-accordion-section name="request" label="Richiesta">
        <div class="slds-box slds-theme_default slds-scrollable_y" style="max-height: 28rem;">
          <c-uu-json-view value={requestObject} editable={requestEditable} height="28rem" onchange={handleRequestJsonChange}></c-uu-json-view>
        </div>
        <lightning-button-group class="slds-m-top_small">
          <lightning-button onclick={toggleRequestEdit} label={requestReadonlyLabel} variant="neutral"></lightning-button>
          <template if:false={requestReadonly}>
            <lightning-button label="Invoca" variant="brand" onclick={handleInvokeEditedRequest}></lightning-button>
          </template>
        </lightning-button-group>
      </lightning-accordion-section>
      <lightning-accordion-section name="response" label="Risposta">
        <div class="slds-box slds-theme_default slds-scrollable_y" style="max-height: 28rem;">
          <c-uu-json-view value={responseObject} height="28rem"></c-uu-json-view>
        </div>
      </lightning-accordion-section>
    </lightning-accordion>
  </lightning-modal-body>
  <lightning-modal-footer>
    <lightning-button variant="neutral" label="Chiudi" onclick={handleClose}></lightning-button>
    <lightning-button variant="brand" label="Salva Request" class="slds-m-left_small" data-id={timestamp} onclick={handleSaveRequest}></lightning-button>
    <lightning-button-menu alternative-text="Copia" class="slds-m-left_small" menu-alignment="right" onselect={handleCopyMenuSelect} icon-name="utility:copy_to_clipboard">
      <lightning-menu-item value="request" label="Copia Richiesta"></lightning-menu-item>
      <lightning-menu-item value="response" label="Copia Risposta"></lightning-menu-item>
      <lightning-menu-item value="json" label="Copia JSON"></lightning-menu-item>
    </lightning-button-menu>
  </lightning-modal-footer>
</template>
