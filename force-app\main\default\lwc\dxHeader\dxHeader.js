import { LightningElement, api } from "lwc";
import { utils } from "c/dxUtils";

export default class HeaderDxApi extends LightningElement {
  _field;

  @api groups;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
  }

  get computedClass() {
    return `header background-${this.field.customAttributes.background}`;
  }

  get caption() {
    const caption = utils.getFirstCaptionByControlFormat(
      this.groups,
      "Standard",
    );
    return caption;
  }

  get icon() {
    return utils.getFirstIconInGroupsByResource(this.groups, "arrowLeft");
  }
}
