import { LightningElement, api, track } from "lwc";

export default class UuNetworkSection extends LightningElement {
  @api data;
  @api preferredNetwork;
  @api agency;

  @track showUcaUsers = false;

  get ucaUsers() {
    return [this.data?.ucaUser];
  }

  networkColumns = [
    { label: "Id", fieldName: "Id" },
    { label: "Name", fieldName: "Name" },
    { label: "Agency", fieldName: "Agency__c" },
    { label: "External Id", fieldName: "ExternalId__c" },
    { label: "Fiscal Code", fieldName: "FiscalCode__c" },
    { label: "Is Active", fieldName: "IsActive__c", type: "boolean" },
    { label: "Network User", fieldName: "NetworkUser__c" },
    { label: "Permission Sets", fieldName: "PermissionSets__c" },
    { label: "Society", fieldName: "Society__c" },
    { label: "User", fieldName: "User__c" },
    { label: "Preferred", fieldName: "Preferred__c", type: "boolean" }
  ];

  toggleSection(event) {
    const { section } = event.currentTarget.dataset;
    this[section] = !this[section];
  }
}
