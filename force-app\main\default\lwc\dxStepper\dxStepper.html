<template>
  <!-- Versione 1: semplice progress bar, se necessario -->
  <template if:true={isProgressVersion}>
    <div class="d-flex w-100 empty-bar-pu mrg-stepper-pu">
      <div class="full-bar-pu"></div>
    </div>
  </template>

  <template if:false={controlFormat}>
    <span class={format}>{field.label}</span>
  </template>
  <template if:true={controlFormat}>
    <c-dx-custom-text-styles content={field.label} text-css={controlFormat}></c-dx-custom-text-styles>
  </template>

  <!-- Versione 2: stepper completo -->
  <template if:true={isStepperVersion}>
    <div class="StepperContainer">
      <template for:each={steps} for:item="step">
        <template if:false={step.space}>
          <span key={step.id} class="StepperStep">
            <span class={step.checkedClass}>
              {step.displayNumber}
            </span>
            <span class={step.stepNameClass} title={step.label}>
              {step.label}
            </span>
          </span>
        </template>
        <template if:true={step.space}>
          <span key={step.id} class="StepperSpace"></span>
        </template>
      </template>
    </div>
  </template>

  <!-- Versione 3: stepper CC -->
  <template if:true={isStepperCC}>
    <div class="stepper-cc">
      {currentStepData.label}
    </div>
  </template>
</template>