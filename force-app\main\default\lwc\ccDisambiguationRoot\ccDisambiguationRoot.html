<template>
  <!-- Disambiguation UI -->
  <div>

    <!-- Header -->
    <div class="slds-page-header slds-p-around_medium slds-border_bottom">
      <div class="slds-media">
        <div class="slds-media__figure slds-m-right_small">
          <lightning-icon icon-name="standard:opportunity" size="large" alternative-text="Logo"></lightning-icon>
        </div>
        <div class="slds-media__body">
          <p class="slds-text-title_caps slds-truncate" title="Trattativa 000">Trattativa 000</p>
          <h1 class="slds-page-header__title slds-truncate" title="Nuovo Preventivo">Nuovo Preventivo</h1>
        </div>
      </div>
    </div>

    <div class="slds-page-body slds-p-bottom_large slds-border_right slds-border_bottom slds-border_left slds-box slds-theme_default">
      <template if:false={showInterprete}>
        <!-- Sottotitolo/descrizione -->
        <div
          class="slds-p-vertical_large slds-align_absolute-center slds-text-heading_medium slds-text-color_weak slds-text-align_center">
          Seleziona l’ambito per creare un nuovo preventivo
        </div>

        <!-- Form: picklist visible until an entity is selected -->
        <div class="slds-p-horizontal_large">
          <template if:false={hasSelectedEntity}>
            <div class="slds-form-element slds-m-bottom_large">
              <div class="slds-form-element__control">
                <lightning-combobox name="ambito" label="Ambito di bisogno" placeholder="Scegli l'ambito"
                  value={selectedEntityCode} options={entityOptions} onchange={handleEntityChange}>
                </lightning-combobox>
              </div>
            </div>
          </template>

          <!-- Entity specific sections -->
          <template lwc:if={hasSelectedEntity}>
            <template lwc:if={show.PUPET}>
              <c-cc-disambiguation-dog-cat entity-domains={entityDomains} onfieldchange={handleChildFieldChange}>
              </c-cc-disambiguation-dog-cat>
            </template>
            <template lwc:if={show.PUCASA}>
              <c-cc-disambiguation-house entity-domains={entityDomains} onfieldchange={handleChildFieldChange}>
              </c-cc-disambiguation-house>
            </template>
            <template lwc:if={show.PUVEICOLO}>
              <c-cc-disambiguation-vehicle entity-domains={entityDomains} onfieldchange={handleChildFieldChange}>
              </c-cc-disambiguation-vehicle>
            </template>
            <template lwc:if={show.PUFAMIGLIA}>
              <c-cc-disambiguation-family entity-domains={entityDomains} onfieldchange={handleChildFieldChange}>
              </c-cc-disambiguation-family>
            </template>
            <template lwc:if={show.PUVIAGGI}>
              <c-cc-disambiguation-travel entity-domains={entityDomains} show-steps={showSteps}
                onfieldchange={handleChildFieldChange}>
              </c-cc-disambiguation-travel>
            </template>
            <template lwc:if={show.PUMOBILITA}>
              <c-cc-disambiguation-mobility entity-domains={entityDomains} onfieldchange={handleChildFieldChange}>
              </c-cc-disambiguation-mobility>
            </template>
          </template>
        </div>

        <!-- CTA: visible only after selecting an entity -->
        <template if:true={hasSelectedEntity}>
          <div class="slds-p-vertical_xx-large slds-align_absolute-center">
            <lightning-button variant="brand" label="Prosegui" onclick={handleClick}
              disabled={isProceedDisabled}></lightning-button>
          </div>
        </template>
      </template>

      <!-- Interprete & other states -->
      <template if:true={showInterprete}>
        <c-dx-interprete request={payload} debug={debug} onnoresult={handleNoResult}></c-dx-interprete>
      </template>
    </div>
  </div>

  <template if:true={showPopup}>
    <c-dx-error-popup error-message={modalErrorMessage} more-data={popupMoreData} onclosepopup={closePopup}
      onloadpage={showError}></c-dx-error-popup>
  </template>

  <template if:true={showFailurePage}>
    <c-dx-page-error request={request} error-message={errorMessage} reload={reloadVisible}></c-dx-page-error>
  </template>

  <template if:true={spinner}>
    <lightning-spinner alternative-text="Loading..." size="large"></lightning-spinner>
  </template>

  <!-- Missing context modal -->
  <template if:true={showMissingContextModal}>
    <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="missing-context-title"
      class="slds-modal slds-fade-in-open">
      <div class="slds-modal__container">
        <header class="slds-modal__header">
          <h2 id="missing-context-title" class="slds-text-heading_medium">
            Informazioni mancanti
          </h2>
        </header>
        <div class="slds-modal__content slds-p-around_medium" id="missing-context-content">
          <p class="slds-m-bottom_small">
            Non è possibile proseguire perché mancano alcune informazioni obbligatorie.
          </p>
          <template if:true={missingParams}>
            <p class="slds-m-bottom_small">
              Dati mancanti: {missingParams}.
            </p>
          </template>
          <p class="slds-text-color_weak">
            Verifica di aver aperto la scheda corretta oppure contatta il supporto.
          </p>
        </div>
        <footer class="slds-modal__footer">
          <lightning-button label="Chiudi" onclick={closeMissingContextModal}></lightning-button>
        </footer>
      </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
  </template>
</template>
